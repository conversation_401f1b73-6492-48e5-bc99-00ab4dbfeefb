import React, { useRef, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler,
} from 'chart.js';
import { Chart } from 'react-chartjs-2';
import { useTheme } from '@/contexts/ThemeContext';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

interface BaseChartProps {
  type: 'line' | 'bar' | 'doughnut' | 'pie' | 'area';
  data: any;
  options?: any;
  height?: number;
  className?: string;
  title?: string;
  description?: string;
}

const BaseChart: React.FC<BaseChartProps> = ({
  type,
  data,
  options = {},
  height = 300,
  className = '',
  title,
  description,
}) => {
  const chartRef = useRef<ChartJS>(null);
  const { actualTheme } = useTheme();

  // 主题相关的颜色配置
  const themeColors = {
    light: {
      background: '#ffffff',
      text: '#374151',
      grid: '#e5e7eb',
      border: '#d1d5db',
    },
    dark: {
      background: '#1f2937',
      text: '#f3f4f6',
      grid: '#374151',
      border: '#4b5563',
    },
  };

  const colors = themeColors[actualTheme];

  // 默认配置
  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: colors.text,
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: actualTheme === 'dark' ? '#374151' : '#ffffff',
        titleColor: colors.text,
        bodyColor: colors.text,
        borderColor: colors.border,
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat('zh-CN').format(context.parsed.y);
            }
            return label;
          },
        },
      },
    },
    scales: type !== 'doughnut' && type !== 'pie' ? {
      x: {
        grid: {
          color: colors.grid,
          borderColor: colors.border,
        },
        ticks: {
          color: colors.text,
          font: {
            size: 11,
          },
        },
      },
      y: {
        grid: {
          color: colors.grid,
          borderColor: colors.border,
        },
        ticks: {
          color: colors.text,
          font: {
            size: 11,
          },
          callback: function(value: any) {
            return new Intl.NumberFormat('zh-CN').format(value);
          },
        },
      },
    } : undefined,
    elements: {
      point: {
        radius: 4,
        hoverRadius: 6,
      },
      line: {
        tension: 0.4,
      },
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
  };

  // 合并配置
  const mergedOptions = {
    ...defaultOptions,
    ...options,
    plugins: {
      ...defaultOptions.plugins,
      ...options.plugins,
    },
  };

  // 处理面积图
  const processedData = type === 'area' ? {
    ...data,
    datasets: data.datasets.map((dataset: any) => ({
      ...dataset,
      fill: true,
      backgroundColor: dataset.backgroundColor || 'rgba(59, 130, 246, 0.1)',
    })),
  } : data;

  const chartType = type === 'area' ? 'line' : type;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {(title || description) && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          {title && (
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {title}
            </h3>
          )}
          {description && (
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {description}
            </p>
          )}
        </div>
      )}
      
      <div className="p-4">
        <div style={{ height: `${height}px` }}>
          <Chart
            ref={chartRef}
            type={chartType}
            data={processedData}
            options={mergedOptions}
          />
        </div>
      </div>
    </div>
  );
};

export default BaseChart;
