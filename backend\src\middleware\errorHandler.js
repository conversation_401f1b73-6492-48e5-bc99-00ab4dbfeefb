const logger = require('../utils/logger');

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, statusCode, code = null, details = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 异步错误处理包装器
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * 验证错误处理
 */
const handleValidationError = (error) => {
  const errors = Object.values(error.errors).map(err => err.message);
  const message = `数据验证失败: ${errors.join(', ')}`;
  return new AppError(message, 400, 'VALIDATION_ERROR', errors);
};

/**
 * 数据库错误处理
 */
const handleDatabaseError = (error) => {
  // PostgreSQL错误码处理
  switch (error.code) {
    case '23505': // 唯一约束违反
      return new AppError('数据已存在', 409, 'DUPLICATE_ENTRY');
    case '23503': // 外键约束违反
      return new AppError('关联数据不存在', 400, 'FOREIGN_KEY_VIOLATION');
    case '23502': // 非空约束违反
      return new AppError('必填字段不能为空', 400, 'NOT_NULL_VIOLATION');
    case '42P01': // 表不存在
      return new AppError('数据表不存在', 500, 'TABLE_NOT_EXISTS');
    case '42703': // 列不存在
      return new AppError('数据列不存在', 500, 'COLUMN_NOT_EXISTS');
    default:
      return new AppError('数据库操作失败', 500, 'DATABASE_ERROR', error.message);
  }
};

/**
 * JWT错误处理
 */
const handleJWTError = (error) => {
  if (error.name === 'JsonWebTokenError') {
    return new AppError('无效的访问令牌', 401, 'INVALID_TOKEN');
  }
  if (error.name === 'TokenExpiredError') {
    return new AppError('访问令牌已过期', 401, 'TOKEN_EXPIRED');
  }
  return new AppError('令牌验证失败', 401, 'TOKEN_ERROR');
};

/**
 * 限流错误处理
 */
const handleRateLimitError = () => {
  return new AppError('请求过于频繁，请稍后再试', 429, 'RATE_LIMIT_EXCEEDED');
};

/**
 * 文件上传错误处理
 */
const handleMulterError = (error) => {
  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      return new AppError('文件大小超出限制', 400, 'FILE_TOO_LARGE');
    case 'LIMIT_FILE_COUNT':
      return new AppError('文件数量超出限制', 400, 'TOO_MANY_FILES');
    case 'LIMIT_UNEXPECTED_FILE':
      return new AppError('不支持的文件字段', 400, 'UNEXPECTED_FILE');
    default:
      return new AppError('文件上传失败', 400, 'UPLOAD_ERROR', error.message);
  }
};

/**
 * 发送错误响应
 */
const sendErrorResponse = (err, req, res) => {
  const { statusCode, message, code, details } = err;
  
  // 错误响应格式
  const errorResponse = {
    success: false,
    error: message,
    code: code || 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method
  };
  
  // 开发环境添加详细信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
    if (details) {
      errorResponse.details = details;
    }
  }
  
  res.status(statusCode).json(errorResponse);
};

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;
  
  // 记录错误日志
  logger.error(`错误: ${error.message}`, {
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    params: req.params,
    query: req.query
  });
  
  // 处理不同类型的错误
  if (error.name === 'ValidationError') {
    error = handleValidationError(error);
  } else if (error.code && error.code.startsWith('23')) {
    error = handleDatabaseError(error);
  } else if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    error = handleJWTError(error);
  } else if (error.type === 'entity.too.large') {
    error = new AppError('请求体过大', 413, 'PAYLOAD_TOO_LARGE');
  } else if (error.code === 'EBADCSRFTOKEN') {
    error = new AppError('无效的CSRF令牌', 403, 'INVALID_CSRF_TOKEN');
  } else if (error.code === 'LIMIT_FILE_SIZE') {
    error = handleMulterError(error);
  } else if (!error.isOperational) {
    // 未知错误，不暴露详细信息
    error = new AppError(
      process.env.NODE_ENV === 'production' ? '服务器内部错误' : error.message,
      500,
      'INTERNAL_ERROR'
    );
  }
  
  sendErrorResponse(error, req, res);
};

/**
 * 404错误处理
 */
const notFoundHandler = (req, res, next) => {
  const error = new AppError(`接口 ${req.originalUrl} 不存在`, 404, 'NOT_FOUND');
  next(error);
};

/**
 * 创建错误实例的便捷方法
 */
const createError = {
  badRequest: (message, code = 'BAD_REQUEST', details = null) => 
    new AppError(message, 400, code, details),
  
  unauthorized: (message = '未授权访问', code = 'UNAUTHORIZED') => 
    new AppError(message, 401, code),
  
  forbidden: (message = '禁止访问', code = 'FORBIDDEN') => 
    new AppError(message, 403, code),
  
  notFound: (message = '资源不存在', code = 'NOT_FOUND') => 
    new AppError(message, 404, code),
  
  conflict: (message, code = 'CONFLICT', details = null) => 
    new AppError(message, 409, code, details),
  
  tooManyRequests: (message = '请求过于频繁', code = 'TOO_MANY_REQUESTS') => 
    new AppError(message, 429, code),
  
  internal: (message = '服务器内部错误', code = 'INTERNAL_ERROR', details = null) => 
    new AppError(message, 500, code, details),
  
  serviceUnavailable: (message = '服务暂时不可用', code = 'SERVICE_UNAVAILABLE') => 
    new AppError(message, 503, code),
};

module.exports = {
  AppError,
  asyncHandler,
  errorHandler,
  notFoundHandler,
  createError
};
