import React from 'react';
import BaseChart from './BaseChart';

interface PieChartData {
  label: string;
  value: number;
  color?: string;
}

interface PieChartProps {
  data: PieChartData[];
  type?: 'pie' | 'doughnut';
  title?: string;
  height?: number;
  className?: string;
  showLegend?: boolean;
  showPercentage?: boolean;
  centerText?: string;
}

const PieChart: React.FC<PieChartProps> = ({
  data,
  type = 'doughnut',
  title,
  height = 300,
  className = '',
  showLegend = true,
  showPercentage = true,
  centerText,
}) => {
  // 默认颜色配置
  const defaultColors = [
    '#3b82f6', // blue
    '#10b981', // green
    '#f59e0b', // yellow
    '#ef4444', // red
    '#8b5cf6', // purple
    '#06b6d4', // cyan
    '#f97316', // orange
    '#84cc16', // lime
    '#ec4899', // pink
    '#6b7280', // gray
  ];

  // 计算总值
  const total = data.reduce((sum, item) => sum + item.value, 0);

  // 处理数据
  const chartData = {
    labels: data.map(item => item.label),
    datasets: [
      {
        data: data.map(item => item.value),
        backgroundColor: data.map((item, index) => 
          item.color || defaultColors[index % defaultColors.length]
        ),
        borderColor: '#ffffff',
        borderWidth: 2,
        hoverBorderWidth: 3,
      },
    ],
  };

  // 图表配置
  const options = {
    plugins: {
      legend: {
        display: showLegend,
        position: 'right' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, index: number) => {
                const value = data.datasets[0].data[index];
                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
                
                return {
                  text: showPercentage ? `${label} (${percentage}%)` : label,
                  fillStyle: data.datasets[0].backgroundColor[index],
                  strokeStyle: data.datasets[0].borderColor,
                  lineWidth: data.datasets[0].borderWidth,
                  hidden: false,
                  index: index,
                };
              });
            }
            return [];
          },
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
            return `${label}: ${value.toLocaleString('zh-CN')} (${percentage}%)`;
          },
        },
      },
    },
    cutout: type === 'doughnut' ? '60%' : '0%',
    maintainAspectRatio: false,
    responsive: true,
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {title && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h3>
        </div>
      )}
      
      <div className="p-4">
        <div className="relative" style={{ height: `${height}px` }}>
          <BaseChart
            type={type}
            data={chartData}
            options={options}
            height={height}
          />
          
          {/* 中心文本（仅适用于doughnut图） */}
          {type === 'doughnut' && centerText && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {centerText}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  总计
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* 数据表格 */}
        <div className="mt-4 overflow-hidden">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                  项目
                </th>
                <th className="text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                  数值
                </th>
                <th className="text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider py-2">
                  占比
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {data.map((item, index) => {
                const percentage = total > 0 ? ((item.value / total) * 100).toFixed(1) : '0.0';
                const color = item.color || defaultColors[index % defaultColors.length];
                
                return (
                  <tr key={item.label}>
                    <td className="py-2 text-sm">
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: color }}
                        />
                        <span className="text-gray-900 dark:text-white">
                          {item.label}
                        </span>
                      </div>
                    </td>
                    <td className="py-2 text-sm text-right text-gray-900 dark:text-white">
                      {item.value.toLocaleString('zh-CN')}
                    </td>
                    <td className="py-2 text-sm text-right text-gray-500 dark:text-gray-400">
                      {percentage}%
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// 市场份额饼图
export const MarketShareChart: React.FC<{
  data: Array<{ platform: string; share: number; color?: string }>;
  title?: string;
  height?: number;
  className?: string;
}> = ({ data, title = '市场份额分布', height = 300, className = '' }) => {
  const chartData = data.map(item => ({
    label: item.platform,
    value: item.share,
    color: item.color,
  }));

  return (
    <PieChart
      data={chartData}
      type="doughnut"
      title={title}
      height={height}
      className={className}
      centerText={`${data.length}个平台`}
    />
  );
};

// 类别分布饼图
export const CategoryChart: React.FC<{
  data: Array<{ category: string; count: number; color?: string }>;
  title?: string;
  height?: number;
  className?: string;
}> = ({ data, title = '类别分布', height = 300, className = '' }) => {
  const total = data.reduce((sum, item) => sum + item.count, 0);
  
  const chartData = data.map(item => ({
    label: item.category,
    value: item.count,
    color: item.color,
  }));

  return (
    <PieChart
      data={chartData}
      type="doughnut"
      title={title}
      height={height}
      className={className}
      centerText={total.toLocaleString('zh-CN')}
    />
  );
};

export default PieChart;
