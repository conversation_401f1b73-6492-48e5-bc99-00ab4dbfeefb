/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('api_keys', function(table) {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 关联用户
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // API密钥信息
    table.string('name', 100).notNullable();
    table.string('key', 255).notNullable().unique();
    table.text('description').nullable();
    
    // 权限和限制
    table.json('permissions').nullable(); // 允许的操作
    table.json('rate_limits').nullable(); // 速率限制
    table.integer('daily_limit').nullable(); // 每日请求限制
    table.integer('monthly_limit').nullable(); // 每月请求限制
    
    // 状态信息
    table.enum('status', ['active', 'inactive', 'revoked']).defaultTo('active');
    table.timestamp('expires_at').nullable();
    table.timestamp('last_used_at').nullable();
    
    // 使用统计
    table.integer('total_requests').defaultTo(0);
    table.integer('daily_requests').defaultTo(0);
    table.integer('monthly_requests').defaultTo(0);
    table.date('last_reset_date').defaultTo(knex.fn.now());
    
    // 安全信息
    table.json('allowed_ips').nullable(); // 允许的IP地址
    table.json('allowed_domains').nullable(); // 允许的域名
    table.string('created_by_ip', 45).nullable();
    table.string('last_used_ip', 45).nullable();
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['user_id']);
    table.index(['key']);
    table.index(['status']);
    table.index(['expires_at']);
    table.index(['last_used_at']);
    table.index(['created_at']);
    
    // 复合索引
    table.index(['user_id', 'status']);
    table.index(['status', 'expires_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('api_keys');
};
