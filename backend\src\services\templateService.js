const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs').promises;
const { createError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

class TemplateService {
  constructor() {
    this.templateFields = {
      // 必填字段
      required: [
        { key: 'title', name: '产品标题', type: 'string', maxLength: 500 },
        { key: 'platform', name: '平台', type: 'enum', values: ['taobao', 'tmall', 'jd', 'pdd', 'manual'] },
        { key: 'current_price', name: '当前价格', type: 'number', min: 0 }
      ],
      // 可选字段
      optional: [
        { key: 'description', name: '产品描述', type: 'string', maxLength: 2000 },
        { key: 'url', name: '产品链接', type: 'url' },
        { key: 'image_url', name: '图片链接', type: 'url' },
        { key: 'brand', name: '品牌', type: 'string', maxLength: 100 },
        { key: 'model', name: '型号', type: 'string', maxLength: 100 },
        { key: 'platform_product_id', name: '平台商品ID', type: 'string', maxLength: 100 },
        { key: 'shop_name', name: '店铺名称', type: 'string', maxLength: 200 },
        { key: 'shop_url', name: '店铺链接', type: 'url' },
        { key: 'original_price', name: '原价', type: 'number', min: 0 },
        { key: 'currency', name: '货币', type: 'string', default: 'CNY' },
        { key: 'sales_count', name: '销量', type: 'integer', min: 0 },
        { key: 'rating', name: '评分', type: 'number', min: 0, max: 5 },
        { key: 'review_count', name: '评论数', type: 'integer', min: 0 },
        { key: 'stock_quantity', name: '库存数量', type: 'integer', min: 0 },
        { key: 'category', name: '类别', type: 'string', maxLength: 100 },
        { key: 'subcategory', name: '子类别', type: 'string', maxLength: 100 },
        { key: 'tags', name: '标签', type: 'string', description: '多个标签用逗号分隔' },
        { key: 'status', name: '状态', type: 'enum', values: ['active', 'inactive', 'out_of_stock', 'discontinued'], default: 'active' }
      ]
    };
  }

  /**
   * 生成Excel模板
   */
  async generateExcelTemplate() {
    try {
      const workbook = XLSX.utils.book_new();
      
      // 创建数据表
      const dataHeaders = [
        ...this.templateFields.required.map(field => `${field.name}*`),
        ...this.templateFields.optional.map(field => field.name)
      ];
      
      // 示例数据
      const sampleData = [
        [
          'iPhone 15 Pro Max 256GB 深空黑色',
          'tmall',
          8999,
          '苹果iPhone 15 Pro Max，搭载A17 Pro芯片，钛金属设计',
          'https://detail.tmall.com/item.htm?id=123456789',
          'https://img.alicdn.com/imgextra/i1/123456789.jpg',
          'Apple',
          'iPhone 15 Pro Max',
          '123456789',
          'Apple官方旗舰店',
          'https://apple.tmall.com',
          9999,
          'CNY',
          1000,
          4.8,
          500,
          100,
          '手机数码',
          '智能手机',
          '苹果,iPhone,5G,拍照',
          'active'
        ]
      ];
      
      const dataSheet = [dataHeaders, ...sampleData];
      const worksheet = XLSX.utils.aoa_to_sheet(dataSheet);
      
      // 设置列宽
      const colWidths = dataHeaders.map(() => ({ wch: 20 }));
      worksheet['!cols'] = colWidths;
      
      XLSX.utils.book_append_sheet(workbook, worksheet, '产品数据');
      
      // 创建说明表
      const instructionData = [
        ['字段名称', '是否必填', '数据类型', '说明', '示例'],
        ...this.templateFields.required.map(field => [
          field.name,
          '是',
          this.getFieldTypeDescription(field),
          this.getFieldDescription(field),
          this.getFieldExample(field)
        ]),
        ...this.templateFields.optional.map(field => [
          field.name,
          '否',
          this.getFieldTypeDescription(field),
          this.getFieldDescription(field),
          this.getFieldExample(field)
        ])
      ];
      
      const instructionSheet = XLSX.utils.aoa_to_sheet(instructionData);
      instructionSheet['!cols'] = [
        { wch: 15 },
        { wch: 10 },
        { wch: 15 },
        { wch: 40 },
        { wch: 25 }
      ];
      
      XLSX.utils.book_append_sheet(workbook, instructionSheet, '字段说明');
      
      // 生成文件
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      
      return {
        buffer,
        filename: `产品数据导入模板_${new Date().toISOString().split('T')[0]}.xlsx`,
        contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };
    } catch (error) {
      logger.error('生成Excel模板失败:', error);
      throw createError.internal('模板生成失败', 'TEMPLATE_GENERATION_ERROR');
    }
  }

  /**
   * 生成CSV模板
   */
  async generateCSVTemplate() {
    try {
      const headers = [
        ...this.templateFields.required.map(field => `${field.name}*`),
        ...this.templateFields.optional.map(field => field.name)
      ];
      
      // 示例数据
      const sampleRow = [
        'iPhone 15 Pro Max 256GB 深空黑色',
        'tmall',
        '8999',
        '苹果iPhone 15 Pro Max，搭载A17 Pro芯片，钛金属设计',
        'https://detail.tmall.com/item.htm?id=123456789',
        'https://img.alicdn.com/imgextra/i1/123456789.jpg',
        'Apple',
        'iPhone 15 Pro Max',
        '123456789',
        'Apple官方旗舰店',
        'https://apple.tmall.com',
        '9999',
        'CNY',
        '1000',
        '4.8',
        '500',
        '100',
        '手机数码',
        '智能手机',
        '苹果,iPhone,5G,拍照',
        'active'
      ];
      
      const csvContent = [
        headers.join(','),
        sampleRow.map(value => `"${value}"`).join(',')
      ].join('\n');
      
      return {
        content: csvContent,
        filename: `产品数据导入模板_${new Date().toISOString().split('T')[0]}.csv`,
        contentType: 'text/csv; charset=utf-8'
      };
    } catch (error) {
      logger.error('生成CSV模板失败:', error);
      throw createError.internal('模板生成失败', 'TEMPLATE_GENERATION_ERROR');
    }
  }

  /**
   * 获取字段类型描述
   */
  getFieldTypeDescription(field) {
    switch (field.type) {
      case 'string':
        return `文本${field.maxLength ? `(最大${field.maxLength}字符)` : ''}`;
      case 'number':
        return '数字';
      case 'integer':
        return '整数';
      case 'enum':
        return `枚举(${field.values.join('/')})`;
      case 'url':
        return '网址';
      default:
        return '文本';
    }
  }

  /**
   * 获取字段描述
   */
  getFieldDescription(field) {
    const descriptions = {
      title: '产品的完整标题名称',
      platform: '产品所属的电商平台',
      current_price: '产品当前售价',
      description: '产品的详细描述信息',
      url: '产品的详情页链接',
      image_url: '产品主图的图片链接',
      brand: '产品品牌名称',
      model: '产品型号或规格',
      platform_product_id: '在对应平台的商品ID',
      shop_name: '销售该产品的店铺名称',
      shop_url: '店铺的链接地址',
      original_price: '产品的原价（未打折前）',
      currency: '价格货币单位，默认CNY',
      sales_count: '产品的总销量',
      rating: '产品评分，1-5分',
      review_count: '产品的评论总数',
      stock_quantity: '产品的库存数量',
      category: '产品的主要类别',
      subcategory: '产品的子类别',
      tags: '产品标签，多个标签用英文逗号分隔',
      status: '产品状态：active(正常)/inactive(下架)/out_of_stock(缺货)/discontinued(停产)'
    };
    
    return descriptions[field.key] || field.description || '';
  }

  /**
   * 获取字段示例
   */
  getFieldExample(field) {
    const examples = {
      title: 'iPhone 15 Pro Max 256GB',
      platform: 'tmall',
      current_price: '8999',
      description: '苹果最新旗舰手机',
      url: 'https://detail.tmall.com/item.htm?id=123456',
      image_url: 'https://img.alicdn.com/imgextra/i1/123456.jpg',
      brand: 'Apple',
      model: 'iPhone 15 Pro Max',
      platform_product_id: '123456789',
      shop_name: 'Apple官方旗舰店',
      shop_url: 'https://apple.tmall.com',
      original_price: '9999',
      currency: 'CNY',
      sales_count: '1000',
      rating: '4.8',
      review_count: '500',
      stock_quantity: '100',
      category: '手机数码',
      subcategory: '智能手机',
      tags: '苹果,iPhone,5G',
      status: 'active'
    };
    
    return examples[field.key] || '';
  }

  /**
   * 获取模板字段信息
   */
  getTemplateFields() {
    return {
      required: this.templateFields.required,
      optional: this.templateFields.optional,
      totalFields: this.templateFields.required.length + this.templateFields.optional.length
    };
  }

  /**
   * 验证字段值
   */
  validateFieldValue(field, value) {
    const errors = [];
    
    // 检查必填字段
    if (field.required && (value === null || value === undefined || value === '')) {
      errors.push(`${field.name}是必填字段`);
      return errors;
    }
    
    // 如果值为空且非必填，跳过验证
    if (!value && !field.required) {
      return errors;
    }
    
    // 类型验证
    switch (field.type) {
      case 'string':
        if (typeof value !== 'string') {
          errors.push(`${field.name}必须是文本类型`);
        } else if (field.maxLength && value.length > field.maxLength) {
          errors.push(`${field.name}长度不能超过${field.maxLength}个字符`);
        }
        break;
        
      case 'number':
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          errors.push(`${field.name}必须是有效数字`);
        } else {
          if (field.min !== undefined && numValue < field.min) {
            errors.push(`${field.name}不能小于${field.min}`);
          }
          if (field.max !== undefined && numValue > field.max) {
            errors.push(`${field.name}不能大于${field.max}`);
          }
        }
        break;
        
      case 'integer':
        const intValue = parseInt(value);
        if (isNaN(intValue) || intValue !== parseFloat(value)) {
          errors.push(`${field.name}必须是整数`);
        } else {
          if (field.min !== undefined && intValue < field.min) {
            errors.push(`${field.name}不能小于${field.min}`);
          }
          if (field.max !== undefined && intValue > field.max) {
            errors.push(`${field.name}不能大于${field.max}`);
          }
        }
        break;
        
      case 'enum':
        if (!field.values.includes(value)) {
          errors.push(`${field.name}必须是以下值之一：${field.values.join(', ')}`);
        }
        break;
        
      case 'url':
        try {
          new URL(value);
        } catch {
          errors.push(`${field.name}必须是有效的网址`);
        }
        break;
    }
    
    return errors;
  }
}

module.exports = new TemplateService();
