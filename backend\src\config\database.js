const knex = require('knex');
const logger = require('../utils/logger');

// Knex配置
const knexConfig = {
  client: 'postgresql',
  connection: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'smartpick',
    user: process.env.DB_USER || 'smartpick_user',
    password: process.env.DB_PASSWORD || 'smartpick_password',
  },
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100,
  },
  migrations: {
    directory: './migrations',
    tableName: 'knex_migrations'
  },
  seeds: {
    directory: './seeds'
  },
  debug: process.env.NODE_ENV === 'development'
};

// 创建数据库连接实例
const db = knex(knexConfig);

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    // 测试数据库连接
    await db.raw('SELECT 1+1 as result');
    logger.info('数据库连接测试成功');
    
    // 运行待处理的迁移
    const [batchNo, migrations] = await db.migrate.latest();
    if (migrations.length > 0) {
      logger.info(`执行了 ${migrations.length} 个迁移文件，批次号: ${batchNo}`);
      migrations.forEach(migration => {
        logger.info(`- ${migration}`);
      });
    } else {
      logger.info('数据库已是最新版本，无需迁移');
    }
    
    return db;
  } catch (error) {
    logger.error('数据库连接失败:', error);
    throw error;
  }
}

/**
 * 关闭数据库连接
 */
async function closeDB() {
  try {
    await db.destroy();
    logger.info('数据库连接已关闭');
  } catch (error) {
    logger.error('关闭数据库连接时出错:', error);
    throw error;
  }
}

/**
 * 检查数据库连接状态
 */
async function checkDBHealth() {
  try {
    await db.raw('SELECT 1');
    return { status: 'healthy', message: '数据库连接正常' };
  } catch (error) {
    return { status: 'unhealthy', message: error.message };
  }
}

/**
 * 执行事务
 */
async function transaction(callback) {
  return await db.transaction(callback);
}

/**
 * 获取表信息
 */
async function getTableInfo(tableName) {
  try {
    const columns = await db(tableName).columnInfo();
    const count = await db(tableName).count('* as count').first();
    return {
      table: tableName,
      columns: Object.keys(columns),
      rowCount: parseInt(count.count)
    };
  } catch (error) {
    logger.error(`获取表 ${tableName} 信息失败:`, error);
    throw error;
  }
}

/**
 * 数据库统计信息
 */
async function getDatabaseStats() {
  try {
    const tables = ['users', 'products', 'analysis_results', 'crawler_logs'];
    const stats = {};
    
    for (const table of tables) {
      try {
        const result = await db(table).count('* as count').first();
        stats[table] = parseInt(result.count);
      } catch (error) {
        stats[table] = 0; // 表可能不存在
      }
    }
    
    return stats;
  } catch (error) {
    logger.error('获取数据库统计信息失败:', error);
    return {};
  }
}

module.exports = {
  db,
  connectDB,
  closeDB,
  checkDBHealth,
  transaction,
  getTableInfo,
  getDatabaseStats,
  knexConfig
};
