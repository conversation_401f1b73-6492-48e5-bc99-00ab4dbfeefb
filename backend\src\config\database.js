const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');

// 创建Prisma客户端实例
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development'
    ? ['query', 'info', 'warn', 'error']
    : ['warn', 'error'],
  errorFormat: 'pretty',
});

// 数据库连接实例（向后兼容）
const db = prisma;

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    // 测试数据库连接
    await prisma.$connect();
    logger.info('Prisma数据库连接成功');

    // 测试查询
    await prisma.$queryRaw`SELECT 1 as result`;
    logger.info('数据库连接测试成功');

    return prisma;
  } catch (error) {
    logger.error('数据库连接失败:', error);
    throw error;
  }
}

/**
 * 关闭数据库连接
 */
async function closeDB() {
  try {
    await prisma.$disconnect();
    logger.info('Prisma数据库连接已关闭');
  } catch (error) {
    logger.error('关闭数据库连接时出错:', error);
    throw error;
  }
}

/**
 * 检查数据库连接状态
 */
async function checkDBHealth() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return { status: 'healthy', message: '数据库连接正常' };
  } catch (error) {
    return { status: 'unhealthy', message: error.message };
  }
}

/**
 * 执行事务
 */
async function transaction(callback) {
  return await prisma.$transaction(callback);
}

/**
 * 获取表信息
 */
async function getTableInfo(modelName) {
  try {
    // Prisma中使用模型名称而不是表名
    const modelMap = {
      users: 'user',
      products: 'product',
      analysis_results: 'analysisResult',
      import_tasks: 'importTask',
      price_history: 'priceHistory',
      api_keys: 'apiKey',
      system_logs: 'systemLog'
    };

    const model = modelMap[modelName] || modelName;
    const count = await prisma[model].count();

    return {
      table: modelName,
      model: model,
      rowCount: count
    };
  } catch (error) {
    logger.error(`获取表 ${modelName} 信息失败:`, error);
    throw error;
  }
}

/**
 * 数据库统计信息
 */
async function getDatabaseStats() {
  try {
    const stats = {};

    // 使用Prisma模型获取统计信息
    stats.users = await prisma.user.count();
    stats.products = await prisma.product.count();
    stats.analysis_results = await prisma.analysisResult.count();
    stats.import_tasks = await prisma.importTask.count();
    stats.price_history = await prisma.priceHistory.count();
    stats.api_keys = await prisma.apiKey.count();
    stats.system_logs = await prisma.systemLog.count();

    return stats;
  } catch (error) {
    logger.error('获取数据库统计信息失败:', error);
    return {};
  }
}

module.exports = {
  db,
  prisma,
  connectDB,
  closeDB,
  checkDBHealth,
  transaction,
  getTableInfo,
  getDatabaseStats
};
