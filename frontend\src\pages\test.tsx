import React from 'react';

const TestPage: React.FC = () => {
  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '2rem'
    }}>
      <div className="card" style={{ maxWidth: '600px', width: '100%' }}>
        <div className="card-header">
          <h1 style={{
            fontSize: '2rem',
            fontWeight: '700',
            color: 'var(--color-gray-900)',
            margin: 0
          }}>
            🎉 现代化UI测试页面
          </h1>
        </div>
        
        <div className="card-body">
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: 'var(--color-gray-800)',
              marginBottom: '1rem'
            }}>
              UI组件展示
            </h2>
            
            {/* 按钮测试 */}
            <div style={{ marginBottom: '1.5rem' }}>
              <h3 style={{ fontSize: '1rem', fontWeight: '500', marginBottom: '0.75rem' }}>按钮组件</h3>
              <div style={{ display: 'flex', gap: '0.75rem', flexWrap: 'wrap' }}>
                <button className="btn btn-primary">主要按钮</button>
                <button className="btn btn-secondary">次要按钮</button>
                <button className="btn btn-ghost">幽灵按钮</button>
                <button className="btn btn-primary btn-sm">小按钮</button>
                <button className="btn btn-primary" disabled>禁用按钮</button>
              </div>
            </div>

            {/* 徽章测试 */}
            <div style={{ marginBottom: '1.5rem' }}>
              <h3 style={{ fontSize: '1rem', fontWeight: '500', marginBottom: '0.75rem' }}>徽章组件</h3>
              <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                <span className="badge badge-primary">主要</span>
                <span className="badge badge-success">成功</span>
                <span className="badge badge-warning">警告</span>
                <span className="badge badge-error">错误</span>
              </div>
            </div>

            {/* 输入框测试 */}
            <div style={{ marginBottom: '1.5rem' }}>
              <h3 style={{ fontSize: '1rem', fontWeight: '500', marginBottom: '0.75rem' }}>输入框组件</h3>
              <div style={{ display: 'grid', gap: '1rem', maxWidth: '300px' }}>
                <div className="form-group">
                  <div className="input-group">
                    <input
                      type="text"
                      className="input input-floating"
                      placeholder="请输入文本"
                    />
                    <label className="input-label">浮动标签</label>
                  </div>
                </div>
                
                <input
                  type="email"
                  className="input"
                  placeholder="普通输入框"
                />
              </div>
            </div>

            {/* 加载动画测试 */}
            <div style={{ marginBottom: '1.5rem' }}>
              <h3 style={{ fontSize: '1rem', fontWeight: '500', marginBottom: '0.75rem' }}>加载动画</h3>
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div className="loading-spinner"></div>
                <span>加载中...</span>
              </div>
            </div>

            {/* 工具提示测试 */}
            <div>
              <h3 style={{ fontSize: '1rem', fontWeight: '500', marginBottom: '0.75rem' }}>工具提示</h3>
              <span className="tooltip" data-tooltip="这是一个工具提示">
                <button className="btn btn-secondary">悬停查看提示</button>
              </span>
            </div>
          </div>
        </div>

        <div className="card-footer">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
              现代化UI组件系统 v1.0
            </span>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <a href="/" className="btn btn-ghost btn-sm">返回首页</a>
              <a href="/auth/login" className="btn btn-primary btn-sm">登录页面</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestPage;
