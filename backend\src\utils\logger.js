const winston = require('winston');
const path = require('path');

// 日志级别
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// 日志颜色
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

winston.addColors(colors);

// 日志格式
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// 文件传输器配置
const fileTransports = [
  // 错误日志文件
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'error.log'),
    level: 'error',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
    maxsize: 5242880, // 5MB
    maxFiles: 5,
  }),
  
  // 所有日志文件
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'combined.log'),
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
    maxsize: 5242880, // 5MB
    maxFiles: 5,
  }),
];

// 控制台传输器
const consoleTransport = new winston.transports.Console({
  format,
});

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels,
  transports: [
    consoleTransport,
    ...fileTransports,
  ],
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'exceptions.log'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    }),
  ],
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'rejections.log'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    }),
  ],
});

// 生产环境不输出到控制台
if (process.env.NODE_ENV === 'production') {
  logger.remove(consoleTransport);
}

/**
 * 创建子logger
 */
function createChildLogger(service) {
  return logger.child({ service });
}

/**
 * 记录HTTP请求
 */
function logRequest(req, res, next) {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
    };
    
    if (res.statusCode >= 400) {
      logger.warn(`HTTP ${res.statusCode} ${req.method} ${req.originalUrl} - ${duration}ms`, logData);
    } else {
      logger.http(`HTTP ${res.statusCode} ${req.method} ${req.originalUrl} - ${duration}ms`, logData);
    }
  });
  
  next();
}

/**
 * 记录数据库查询
 */
function logQuery(query, duration) {
  logger.debug(`数据库查询: ${query} - ${duration}ms`);
}

/**
 * 记录API调用
 */
function logApiCall(service, endpoint, duration, success = true) {
  const logData = {
    service,
    endpoint,
    duration: `${duration}ms`,
    success
  };
  
  if (success) {
    logger.info(`API调用成功: ${service} ${endpoint} - ${duration}ms`, logData);
  } else {
    logger.error(`API调用失败: ${service} ${endpoint} - ${duration}ms`, logData);
  }
}

/**
 * 记录爬虫活动
 */
function logCrawler(platform, action, data = {}) {
  logger.info(`爬虫活动: ${platform} - ${action}`, {
    platform,
    action,
    ...data
  });
}

/**
 * 记录用户活动
 */
function logUserActivity(userId, action, data = {}) {
  logger.info(`用户活动: ${userId} - ${action}`, {
    userId,
    action,
    ...data
  });
}

/**
 * 记录系统事件
 */
function logSystemEvent(event, data = {}) {
  logger.info(`系统事件: ${event}`, {
    event,
    ...data
  });
}

/**
 * 记录性能指标
 */
function logPerformance(operation, duration, data = {}) {
  logger.info(`性能指标: ${operation} - ${duration}ms`, {
    operation,
    duration,
    ...data
  });
}

module.exports = {
  logger,
  createChildLogger,
  logRequest,
  logQuery,
  logApiCall,
  logCrawler,
  logUserActivity,
  logSystemEvent,
  logPerformance,
  
  // 直接导出常用方法
  info: logger.info.bind(logger),
  error: logger.error.bind(logger),
  warn: logger.warn.bind(logger),
  debug: logger.debug.bind(logger),
  http: logger.http.bind(logger),
};
