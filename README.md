# 🛍️ SmartPick - 智能选品系统

基于AI的智能电商选品分析平台，帮助商家做出更明智的产品选择决策。

## ✨ 核心功能

- **📊 数据导入系统**：支持Excel、CSV格式的批量产品数据导入
- **🤖 AI驱动分析**：集成Google Gemini AI，提供深度产品分析和市场洞察
- **📈 多维度评估**：价格竞争力、销售表现、用户评价、市场趋势综合分析
- **📊 可视化报表**：丰富的图表展示，包括价格趋势、雷达图、销售分析等
- **🎯 智能推荐**：基于AI算法的选品建议和风险评估
- **👥 用户管理**：完整的用户认证和权限管理系统

## 🏗️ 技术架构

### 后端技术栈
- **Node.js + Express.js** - 服务端框架
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **Bull Queue** - 任务队列系统
- **JWT** - 身份认证
- **XLSX/CSV** - 文件处理

### 前端技术栈
- **Next.js 13** - React全栈框架
- **TypeScript** - 类型安全
- **TailwindCSS** - 样式框架
- **Chart.js** - 数据可视化
- **React Query** - 数据状态管理

### 部署架构
- **Docker** - 容器化部署
- **Nginx** - 反向代理和负载均衡
- **Docker Compose** - 多容器编排

## 🚀 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose

### 1. 克隆项目
```bash
git clone <repository-url>
cd smartpick
```

### 2. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

### 3. 启动服务
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或者分别启动开发环境
cd backend && npm install && npm run dev
cd frontend && npm install && npm run dev
```

### 4. 访问应用
- 前端应用：http://localhost:3000
- 后端API：http://localhost:8000
- API文档：http://localhost:8000/api/docs

## 📋 数据导入指南

### 支持的文件格式
- Excel文件 (.xlsx, .xls)
- CSV文件 (.csv)
- 最大文件大小：50MB
- 最大行数：10,000行

### 数据模板字段

#### 必填字段
- **产品标题** - 产品的完整名称
- **平台** - 电商平台 (taobao/tmall/jd/pdd/manual)
- **当前价格** - 产品当前售价

#### 可选字段
- **产品描述** - 详细描述信息
- **产品链接** - 商品详情页URL
- **图片链接** - 产品主图URL
- **品牌** - 产品品牌
- **型号** - 产品型号
- **原价** - 未打折前的价格
- **销量** - 产品销售数量
- **评分** - 用户评分 (1-5分)
- **评论数** - 用户评论总数
- **库存数量** - 当前库存
- **类别** - 产品主类别
- **子类别** - 产品子类别
- **标签** - 产品标签 (逗号分隔)

### 导入流程
1. 下载数据模板 (Excel或CSV格式)
2. 按照模板格式填写产品数据
3. 上传文件进行导入
4. 查看导入结果和错误报告
5. 对导入的产品进行AI分析

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smartpick
DB_USER=smartpick_user
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Gemini AI配置
GEMINI_API_KEY=your_gemini_api_key

# 数据导入配置
IMPORT_MAX_FILE_SIZE=52428800  # 50MB
IMPORT_MAX_ROWS=10000
IMPORT_ALLOWED_TYPES=xlsx,xls,csv
```

## 📊 AI分析功能

### 分析维度
- **价格竞争力** - 与同类产品价格对比
- **销售表现** - 销量和增长趋势分析
- **用户评价** - 评分和评论质量分析
- **竞争环境** - 市场竞争激烈程度
- **趋势预测** - 价格和销量趋势预测
- **利润空间** - 成本和利润率估算

### 智能推荐
- 综合评分计算
- 风险等级评估
- 选品建议生成
- 市场机会识别

## 🔒 安全特性

- JWT身份认证
- 密码加密存储
- API访问限流
- 文件类型验证
- SQL注入防护
- XSS攻击防护

## 📈 监控和日志

- 应用性能监控
- 错误日志记录
- 用户行为追踪
- 系统健康检查
- 导入任务状态监控

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

如果您遇到问题或有建议，请：
- 提交 [Issue](https://github.com/your-repo/smartpick/issues)
- 发送邮件至 <EMAIL>
- 查看 [文档](https://docs.smartpick.com)

## 🎯 路线图

- [ ] 支持更多文件格式 (JSON, XML)
- [ ] 实时价格监控和预警
- [ ] 竞品自动发现和跟踪
- [ ] 移动端应用开发
- [ ] 高级数据分析和机器学习
- [ ] 第三方平台API集成

---

**SmartPick** - 让选品更智能 🚀
