const { createError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { db } = require('../config/database');
const geminiService = require('./geminiService');

class AnalysisService {
  constructor() {
    this.weights = {
      price: 0.25,        // 价格权重
      sales: 0.20,        // 销量权重
      rating: 0.15,       // 评分权重
      competition: 0.15,  // 竞争度权重
      trend: 0.15,        // 趋势权重
      profit: 0.10        // 利润率权重
    };
  }

  /**
   * 综合选品分析
   */
  async analyzeProduct(productData, options = {}) {
    try {
      const {
        includeMarketAnalysis = true,
        includeCompetitorAnalysis = true,
        includeTrendAnalysis = true,
        analysisDepth = 'detailed'
      } = options;

      logger.info('开始产品分析:', productData.title);

      // 基础指标分析
      const basicMetrics = this.calculateBasicMetrics(productData);
      
      // 价格分析
      const priceAnalysis = this.analyzePricing(productData);
      
      // 销售表现分析
      const salesAnalysis = this.analyzeSalesPerformance(productData);
      
      // 竞争度分析
      const competitionAnalysis = await this.analyzeCompetition(productData);
      
      // 市场趋势分析（如果需要）
      let trendAnalysis = null;
      if (includeTrendAnalysis) {
        trendAnalysis = await this.analyzeTrend(productData);
      }
      
      // 利润分析
      const profitAnalysis = this.analyzeProfitability(productData);
      
      // 风险评估
      const riskAssessment = this.assessRisks(productData);
      
      // 计算综合评分
      const overallScore = this.calculateOverallScore({
        basicMetrics,
        priceAnalysis,
        salesAnalysis,
        competitionAnalysis,
        trendAnalysis,
        profitAnalysis
      });
      
      // 生成选品建议
      const recommendation = this.generateRecommendation({
        overallScore,
        basicMetrics,
        priceAnalysis,
        salesAnalysis,
        competitionAnalysis,
        riskAssessment
      });

      const result = {
        productId: productData.id,
        productTitle: productData.title,
        platform: productData.platform,
        analysisTimestamp: new Date().toISOString(),
        overallScore,
        basicMetrics,
        priceAnalysis,
        salesAnalysis,
        competitionAnalysis,
        trendAnalysis,
        profitAnalysis,
        riskAssessment,
        recommendation,
        metadata: {
          analysisDepth,
          version: '1.0',
          weights: this.weights
        }
      };

      logger.info('产品分析完成:', { productId: productData.id, score: overallScore });
      return result;
    } catch (error) {
      logger.error('产品分析失败:', error);
      throw error;
    }
  }

  /**
   * 计算基础指标
   */
  calculateBasicMetrics(productData) {
    const metrics = {
      priceScore: this.calculatePriceScore(productData.current_price),
      salesScore: this.calculateSalesScore(productData.sales_count),
      ratingScore: this.calculateRatingScore(productData.rating, productData.review_count),
      popularityScore: this.calculatePopularityScore(productData.sales_count, productData.review_count)
    };

    metrics.averageScore = Object.values(metrics).reduce((sum, score) => sum + score, 0) / Object.keys(metrics).length;
    
    return metrics;
  }

  /**
   * 价格评分计算
   */
  calculatePriceScore(price) {
    if (price <= 0) return 0;
    
    // 价格区间评分逻辑
    if (price < 50) return 85;      // 低价商品，容易销售
    if (price < 200) return 90;     // 中低价，最佳价格区间
    if (price < 500) return 80;     // 中价位
    if (price < 1000) return 70;    // 中高价位
    if (price < 2000) return 60;    // 高价位
    return 50;                      // 超高价位
  }

  /**
   * 销量评分计算
   */
  calculateSalesScore(salesCount) {
    if (salesCount <= 0) return 0;
    
    // 销量评分逻辑（对数函数）
    const logSales = Math.log10(salesCount + 1);
    return Math.min(100, logSales * 25);
  }

  /**
   * 评分评分计算
   */
  calculateRatingScore(rating, reviewCount) {
    if (rating <= 0 || reviewCount <= 0) return 0;
    
    // 评分基础分数
    const ratingScore = (rating / 5.0) * 100;
    
    // 评论数量调整因子
    const reviewFactor = Math.min(1.0, Math.log10(reviewCount + 1) / 3);
    
    return ratingScore * reviewFactor;
  }

  /**
   * 受欢迎程度评分
   */
  calculatePopularityScore(salesCount, reviewCount) {
    const salesWeight = 0.7;
    const reviewWeight = 0.3;
    
    const salesScore = Math.min(100, Math.log10(salesCount + 1) * 20);
    const reviewScore = Math.min(100, Math.log10(reviewCount + 1) * 25);
    
    return salesScore * salesWeight + reviewScore * reviewWeight;
  }

  /**
   * 价格分析
   */
  analyzePricing(productData) {
    const currentPrice = productData.current_price;
    const originalPrice = productData.original_price || currentPrice;
    
    const analysis = {
      currentPrice,
      originalPrice,
      discountPercentage: originalPrice > currentPrice 
        ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100) 
        : 0,
      priceLevel: this.categorizePriceLevel(currentPrice),
      competitiveness: this.assessPriceCompetitiveness(currentPrice, productData.category),
      recommendation: ''
    };

    // 价格建议
    if (analysis.discountPercentage > 30) {
      analysis.recommendation = '折扣力度大，具有价格优势';
    } else if (analysis.priceLevel === 'low') {
      analysis.recommendation = '价格较低，适合薄利多销';
    } else if (analysis.priceLevel === 'medium') {
      analysis.recommendation = '价格适中，平衡利润和销量';
    } else {
      analysis.recommendation = '价格较高，需要强化产品价值';
    }

    return analysis;
  }

  /**
   * 价格水平分类
   */
  categorizePriceLevel(price) {
    if (price < 100) return 'low';
    if (price < 500) return 'medium';
    if (price < 1000) return 'high';
    return 'premium';
  }

  /**
   * 价格竞争力评估
   */
  assessPriceCompetitiveness(price, category) {
    // 这里可以根据类别的平均价格来评估竞争力
    // 暂时使用简单逻辑
    const categoryAverages = {
      '手机数码': 2000,
      '服装鞋包': 200,
      '家用电器': 1000,
      '美妆护肤': 150,
      '食品饮料': 50
    };

    const avgPrice = categoryAverages[category] || 300;
    const ratio = price / avgPrice;

    if (ratio < 0.7) return 'very_competitive';
    if (ratio < 0.9) return 'competitive';
    if (ratio < 1.1) return 'average';
    if (ratio < 1.3) return 'expensive';
    return 'very_expensive';
  }

  /**
   * 销售表现分析
   */
  analyzeSalesPerformance(productData) {
    const salesCount = productData.sales_count || 0;
    const rating = productData.rating || 0;
    const reviewCount = productData.review_count || 0;

    const performance = {
      salesVolume: this.categorizeSalesVolume(salesCount),
      customerSatisfaction: this.categorizeCustomerSatisfaction(rating, reviewCount),
      marketAcceptance: this.assessMarketAcceptance(salesCount, reviewCount),
      growthPotential: this.assessGrowthPotential(productData)
    };

    return performance;
  }

  /**
   * 销量分类
   */
  categorizeSalesVolume(salesCount) {
    if (salesCount < 100) return 'low';
    if (salesCount < 1000) return 'medium';
    if (salesCount < 10000) return 'high';
    return 'very_high';
  }

  /**
   * 客户满意度分类
   */
  categorizeCustomerSatisfaction(rating, reviewCount) {
    if (reviewCount < 10) return 'insufficient_data';
    
    if (rating >= 4.5) return 'excellent';
    if (rating >= 4.0) return 'good';
    if (rating >= 3.5) return 'average';
    if (rating >= 3.0) return 'below_average';
    return 'poor';
  }

  /**
   * 市场接受度评估
   */
  assessMarketAcceptance(salesCount, reviewCount) {
    const conversionRate = reviewCount > 0 ? salesCount / reviewCount : 0;
    
    if (conversionRate > 50) return 'high';
    if (conversionRate > 20) return 'medium';
    if (conversionRate > 10) return 'low';
    return 'very_low';
  }

  /**
   * 增长潜力评估
   */
  assessGrowthPotential(productData) {
    // 基于多个因素评估增长潜力
    let score = 0;
    
    // 评分因素
    if (productData.rating > 4.0) score += 20;
    if (productData.sales_count > 1000) score += 20;
    if (productData.review_count > 100) score += 15;
    if (productData.current_price < 500) score += 15;
    
    // 新品加分
    const createdDate = new Date(productData.created_at);
    const daysSinceCreated = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceCreated < 30) score += 20;
    else if (daysSinceCreated < 90) score += 10;
    
    if (score >= 80) return 'very_high';
    if (score >= 60) return 'high';
    if (score >= 40) return 'medium';
    if (score >= 20) return 'low';
    return 'very_low';
  }
  /**
   * 竞争度分析
   */
  async analyzeCompetition(productData) {
    try {
      // 查找同类产品
      const competitors = await db('products')
        .where('category', productData.category)
        .where('platform', productData.platform)
        .whereNot('id', productData.id)
        .orderBy('sales_count', 'desc')
        .limit(10);

      const analysis = {
        competitorCount: competitors.length,
        averagePrice: 0,
        averageSales: 0,
        averageRating: 0,
        competitionLevel: 'low',
        marketPosition: 'unknown',
        advantages: [],
        disadvantages: []
      };

      if (competitors.length > 0) {
        // 计算平均值
        analysis.averagePrice = competitors.reduce((sum, p) => sum + (p.current_price || 0), 0) / competitors.length;
        analysis.averageSales = competitors.reduce((sum, p) => sum + (p.sales_count || 0), 0) / competitors.length;
        analysis.averageRating = competitors.reduce((sum, p) => sum + (p.rating || 0), 0) / competitors.length;

        // 竞争激烈程度
        if (competitors.length > 50) analysis.competitionLevel = 'very_high';
        else if (competitors.length > 20) analysis.competitionLevel = 'high';
        else if (competitors.length > 10) analysis.competitionLevel = 'medium';
        else analysis.competitionLevel = 'low';

        // 市场地位
        const priceRank = competitors.filter(p => (p.current_price || 0) < (productData.current_price || 0)).length;
        const salesRank = competitors.filter(p => (p.sales_count || 0) > (productData.sales_count || 0)).length;

        if (salesRank < competitors.length * 0.2) analysis.marketPosition = 'leader';
        else if (salesRank < competitors.length * 0.5) analysis.marketPosition = 'strong';
        else if (salesRank < competitors.length * 0.8) analysis.marketPosition = 'average';
        else analysis.marketPosition = 'weak';

        // 优劣势分析
        if ((productData.current_price || 0) < analysis.averagePrice) {
          analysis.advantages.push('价格优势');
        } else {
          analysis.disadvantages.push('价格偏高');
        }

        if ((productData.sales_count || 0) > analysis.averageSales) {
          analysis.advantages.push('销量领先');
        } else {
          analysis.disadvantages.push('销量落后');
        }

        if ((productData.rating || 0) > analysis.averageRating) {
          analysis.advantages.push('评分优秀');
        } else {
          analysis.disadvantages.push('评分偏低');
        }
      }

      return analysis;
    } catch (error) {
      logger.error('竞争分析失败:', error);
      return {
        competitorCount: 0,
        competitionLevel: 'unknown',
        error: error.message
      };
    }
  }

  /**
   * 趋势分析
   */
  async analyzeTrend(productData) {
    try {
      // 获取价格历史数据
      const priceHistory = await db('price_history')
        .where('product_id', productData.id)
        .orderBy('recorded_at', 'desc')
        .limit(30);

      const analysis = {
        priceTrend: 'stable',
        priceVolatility: 'low',
        salesTrend: 'unknown',
        seasonality: 'none',
        forecast: {
          nextWeekPrice: productData.current_price,
          nextMonthPrice: productData.current_price,
          confidence: 0.5
        }
      };

      if (priceHistory.length >= 7) {
        // 价格趋势分析
        const recentPrices = priceHistory.slice(0, 7).map(h => h.price);
        const olderPrices = priceHistory.slice(7, 14).map(h => h.price);

        if (recentPrices.length > 0 && olderPrices.length > 0) {
          const recentAvg = recentPrices.reduce((sum, p) => sum + p, 0) / recentPrices.length;
          const olderAvg = olderPrices.reduce((sum, p) => sum + p, 0) / olderPrices.length;

          const changePercent = ((recentAvg - olderAvg) / olderAvg) * 100;

          if (changePercent > 5) analysis.priceTrend = 'rising';
          else if (changePercent < -5) analysis.priceTrend = 'falling';
          else analysis.priceTrend = 'stable';

          // 价格波动性
          const variance = recentPrices.reduce((sum, p) => sum + Math.pow(p - recentAvg, 2), 0) / recentPrices.length;
          const volatility = Math.sqrt(variance) / recentAvg;

          if (volatility > 0.1) analysis.priceVolatility = 'high';
          else if (volatility > 0.05) analysis.priceVolatility = 'medium';
          else analysis.priceVolatility = 'low';

          // 简单预测
          analysis.forecast.nextWeekPrice = recentAvg + (recentAvg - olderAvg) * 0.5;
          analysis.forecast.nextMonthPrice = recentAvg + (recentAvg - olderAvg) * 2;
          analysis.forecast.confidence = Math.max(0.3, 1 - volatility);
        }
      }

      return analysis;
    } catch (error) {
      logger.error('趋势分析失败:', error);
      return {
        priceTrend: 'unknown',
        error: error.message
      };
    }
  }

  /**
   * 利润分析
   */
  analyzeProfitability(productData) {
    const currentPrice = productData.current_price || 0;

    // 估算成本（简化计算）
    const estimatedCost = currentPrice * 0.6; // 假设成本为售价的60%
    const estimatedProfit = currentPrice - estimatedCost;
    const profitMargin = currentPrice > 0 ? (estimatedProfit / currentPrice) * 100 : 0;

    const analysis = {
      estimatedCost,
      estimatedProfit,
      profitMargin,
      profitLevel: this.categorizeProfitLevel(profitMargin),
      recommendation: ''
    };

    // 利润建议
    if (profitMargin > 50) {
      analysis.recommendation = '利润率很高，可考虑适当降价提升竞争力';
    } else if (profitMargin > 30) {
      analysis.recommendation = '利润率良好，保持当前策略';
    } else if (profitMargin > 15) {
      analysis.recommendation = '利润率一般，需要控制成本或提升价值';
    } else {
      analysis.recommendation = '利润率偏低，需要重新评估定价策略';
    }

    return analysis;
  }

  /**
   * 利润水平分类
   */
  categorizeProfitLevel(profitMargin) {
    if (profitMargin > 50) return 'very_high';
    if (profitMargin > 30) return 'high';
    if (profitMargin > 15) return 'medium';
    if (profitMargin > 5) return 'low';
    return 'very_low';
  }

  /**
   * 风险评估
   */
  assessRisks(productData) {
    const risks = [];
    let overallRiskLevel = 'low';

    // 价格风险
    if ((productData.current_price || 0) > 1000) {
      risks.push({
        type: 'price',
        level: 'medium',
        description: '高价商品，市场接受度风险'
      });
    }

    // 销量风险
    if ((productData.sales_count || 0) < 100) {
      risks.push({
        type: 'sales',
        level: 'high',
        description: '销量较低，市场验证不足'
      });
    }

    // 评分风险
    if ((productData.rating || 0) < 3.5) {
      risks.push({
        type: 'rating',
        level: 'high',
        description: '评分偏低，用户满意度风险'
      });
    }

    // 库存风险
    if ((productData.stock_quantity || 0) < 10) {
      risks.push({
        type: 'inventory',
        level: 'medium',
        description: '库存不足，可能影响销售'
      });
    }

    // 计算整体风险等级
    const highRisks = risks.filter(r => r.level === 'high').length;
    const mediumRisks = risks.filter(r => r.level === 'medium').length;

    if (highRisks > 1) overallRiskLevel = 'high';
    else if (highRisks > 0 || mediumRisks > 2) overallRiskLevel = 'medium';
    else if (mediumRisks > 0) overallRiskLevel = 'low';
    else overallRiskLevel = 'very_low';

    return {
      overallRiskLevel,
      risks,
      riskCount: risks.length
    };
  }

  /**
   * 计算综合评分
   */
  calculateOverallScore(analysisData) {
    const {
      basicMetrics,
      priceAnalysis,
      salesAnalysis,
      competitionAnalysis,
      trendAnalysis,
      profitAnalysis
    } = analysisData;

    let score = 0;

    // 基础指标权重
    score += (basicMetrics.averageScore || 0) * this.weights.price;

    // 销售表现权重
    const salesScore = this.getSalesPerformanceScore(salesAnalysis);
    score += salesScore * this.weights.sales;

    // 评分权重
    score += (basicMetrics.ratingScore || 0) * this.weights.rating;

    // 竞争度权重（竞争越激烈分数越低）
    const competitionScore = this.getCompetitionScore(competitionAnalysis);
    score += competitionScore * this.weights.competition;

    // 趋势权重
    if (trendAnalysis) {
      const trendScore = this.getTrendScore(trendAnalysis);
      score += trendScore * this.weights.trend;
    }

    // 利润权重
    const profitScore = this.getProfitScore(profitAnalysis);
    score += profitScore * this.weights.profit;

    return Math.round(Math.min(100, Math.max(0, score)));
  }

  /**
   * 获取销售表现评分
   */
  getSalesPerformanceScore(salesAnalysis) {
    const volumeScores = { very_high: 100, high: 80, medium: 60, low: 40 };
    const satisfactionScores = { excellent: 100, good: 80, average: 60, below_average: 40, poor: 20 };

    const volumeScore = volumeScores[salesAnalysis.salesVolume] || 0;
    const satisfactionScore = satisfactionScores[salesAnalysis.customerSatisfaction] || 0;

    return (volumeScore + satisfactionScore) / 2;
  }

  /**
   * 获取竞争评分
   */
  getCompetitionScore(competitionAnalysis) {
    const levelScores = { low: 90, medium: 70, high: 50, very_high: 30 };
    const positionScores = { leader: 100, strong: 80, average: 60, weak: 40 };

    const levelScore = levelScores[competitionAnalysis.competitionLevel] || 50;
    const positionScore = positionScores[competitionAnalysis.marketPosition] || 50;

    return (levelScore + positionScore) / 2;
  }

  /**
   * 获取趋势评分
   */
  getTrendScore(trendAnalysis) {
    const trendScores = { rising: 80, stable: 70, falling: 40 };
    const volatilityScores = { low: 80, medium: 60, high: 40 };

    const trendScore = trendScores[trendAnalysis.priceTrend] || 50;
    const volatilityScore = volatilityScores[trendAnalysis.priceVolatility] || 50;

    return (trendScore + volatilityScore) / 2;
  }

  /**
   * 获取利润评分
   */
  getProfitScore(profitAnalysis) {
    const profitScores = { very_high: 90, high: 80, medium: 70, low: 50, very_low: 30 };
    return profitScores[profitAnalysis.profitLevel] || 50;
  }

  /**
   * 生成选品建议
   */
  generateRecommendation(analysisData) {
    const { overallScore, basicMetrics, priceAnalysis, salesAnalysis, competitionAnalysis, riskAssessment } = analysisData;

    let recommendation = 'neutral';
    let confidence = 0.5;
    let reasons = [];
    let suggestions = [];

    // 基于综合评分的基础建议
    if (overallScore >= 80) {
      recommendation = 'highly_recommended';
      confidence = 0.9;
      reasons.push('综合评分优秀');
    } else if (overallScore >= 65) {
      recommendation = 'recommended';
      confidence = 0.7;
      reasons.push('综合评分良好');
    } else if (overallScore >= 50) {
      recommendation = 'neutral';
      confidence = 0.5;
      reasons.push('综合评分一般');
    } else {
      recommendation = 'not_recommended';
      confidence = 0.3;
      reasons.push('综合评分偏低');
    }

    // 风险调整
    if (riskAssessment.overallRiskLevel === 'high') {
      if (recommendation === 'highly_recommended') recommendation = 'recommended';
      else if (recommendation === 'recommended') recommendation = 'neutral';
      confidence *= 0.8;
      reasons.push('存在较高风险');
    }

    // 具体建议
    if (priceAnalysis.competitiveness === 'very_competitive') {
      suggestions.push('价格具有竞争优势，可重点推广');
    }

    if (salesAnalysis.salesVolume === 'high' || salesAnalysis.salesVolume === 'very_high') {
      suggestions.push('销量表现优秀，市场认可度高');
    }

    if (competitionAnalysis.competitionLevel === 'low') {
      suggestions.push('竞争较少，市场机会较大');
    }

    return {
      recommendation,
      confidence: Math.round(confidence * 100),
      reasons,
      suggestions,
      summary: this.generateRecommendationSummary(recommendation, overallScore)
    };
  }

  /**
   * 生成建议摘要
   */
  generateRecommendationSummary(recommendation, score) {
    const summaries = {
      highly_recommended: `强烈推荐此商品（评分：${score}分）。该商品在多个维度表现优秀，具有很好的市场潜力和盈利空间。`,
      recommended: `推荐此商品（评分：${score}分）。该商品整体表现良好，值得考虑加入选品清单。`,
      neutral: `中性评价（评分：${score}分）。该商品有一定潜力，但需要进一步分析市场情况和竞争环境。`,
      not_recommended: `不推荐此商品（评分：${score}分）。该商品存在较多问题，建议寻找其他替代品。`
    };

    return summaries[recommendation] || '无法生成建议摘要';
  }
}

module.exports = new AnalysisService();
