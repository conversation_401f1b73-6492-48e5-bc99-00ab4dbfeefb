// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String   @unique
  password          String
  firstName         String?
  lastName          String?
  avatar            String?
  phone             String?
  role              String   @default("USER")
  status            String   @default("ACTIVE")
  emailVerified     Boolean  @default(false)
  emailVerifiedAt   DateTime?
  lastLoginAt       DateTime?
  loginCount        Int      @default(0)
  preferences       String?
  settings          String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // 关联关系
  products          Product[]
  analysisResults   AnalysisResult[]
  importTasks       ImportTask[]

  @@map("users")
}

// 产品表
model Product {
  id                  String   @id @default(cuid())
  userId              String
  title               String
  description         String?
  url                 String
  imageUrl            String?
  brand               String?
  model               String?
  platform            String   @default("MANUAL")
  platformProductId  String?
  shopName            String?
  shopUrl             String?
  importTaskId        String?
  dataSource          String   @default("MANUAL")
  currentPrice        Float?
  originalPrice       Float?
  minPrice            Float?
  maxPrice            Float?
  currency            String   @default("CNY")
  salesCount          Int?
  rating              Float?
  reviewCount         Int?
  stockQuantity       Int?
  category            String?
  subcategory         String?
  tags                String?
  attributes          String?
  status              String   @default("ACTIVE")
  isTracked           Boolean  @default(false)
  isAnalyzed          Boolean  @default(false)
  lastImportedAt      DateTime?
  importCount         Int      @default(0)
  importErrors        String?
  analysisSummary     String?
  analysisScore       Float?
  marketPosition      String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // 关联关系
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  importTask          ImportTask? @relation(fields: [importTaskId], references: [id], onDelete: SetNull)
  analysisResults     AnalysisResult[]

  @@index([userId])
  @@index([platform])
  @@index([category])
  @@index([status])
  @@index([isTracked])
  @@index([currentPrice])
  @@index([salesCount])
  @@index([rating])
  @@index([createdAt])
  @@index([lastImportedAt])
  @@index([dataSource])
  @@index([platform, platformProductId])
  @@index([userId, status])
  @@index([category, platform])
  @@map("products")
}

// 分析结果表
model AnalysisResult {
  id                String   @id @default(cuid())
  userId            String
  productId         String?
  title             String
  description       String?
  type              String   @default("PRODUCT")
  status            String   @default("PENDING")
  priority          String   @default("NORMAL")
  config            String?
  inputData         String?
  outputData        String?
  summary           String?
  insights          String?
  recommendations   String?
  chartsData        String?
  confidenceScore   Float?
  startedAt         DateTime?
  completedAt       DateTime?
  durationMs        Int?
  errorMessage      String?
  errorDetails      String?
  retryCount        Int      @default(0)
  viewCount         Int      @default(0)
  shareCount        Int      @default(0)
  exportCount       Int      @default(0)
  lastViewedAt      DateTime?
  shareToken        String?
  shareType         String   @default("PRIVATE")
  shareExpiresAt    DateTime?
  tags              String?
  category          String?
  isFavorite        Boolean  @default(false)
  isArchived        Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // 关联关系
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product           Product? @relation(fields: [productId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([productId])
  @@index([type])
  @@index([status])
  @@index([priority])
  @@index([createdAt])
  @@index([completedAt])
  @@index([shareToken])
  @@index([userId, status])
  @@index([type, status])
  @@map("analysis_results")
}

// 数据导入任务表
model ImportTask {
  id                String   @id @default(cuid())
  userId            String
  title             String
  description       String?
  fileName          String
  filePath          String
  fileSize          Int
  fileType          String   @default("CSV")
  status            String   @default("PENDING")
  startedAt         DateTime?
  completedAt       DateTime?
  durationMs        Int?
  totalRows         Int      @default(0)
  processedRows     Int      @default(0)
  successRows       Int      @default(0)
  failedRows        Int      @default(0)
  errorMessage      String?
  errorDetails      String?
  validationErrors  String?
  importSummary     String?
  config            String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // 关联关系
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  products          Product[]

  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@index([fileType])
  @@map("import_tasks")
}


