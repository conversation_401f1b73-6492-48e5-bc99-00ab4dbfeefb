// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin';
  status: 'active' | 'inactive' | 'banned';
  avatar_url?: string;
  bio?: string;
  phone?: string;
  email_verified: boolean;
  preferences?: UserPreferences;
  timezone: string;
  language: string;
  analysis_count: number;
  product_count: number;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    push: boolean;
    analysis_complete: boolean;
    price_alert: boolean;
  };
  dashboard: {
    default_view: string;
    items_per_page: number;
  };
}

// 产品相关类型
export interface Product {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  url: string;
  image_url?: string;
  brand?: string;
  model?: string;
  platform: Platform;
  platform_product_id?: string;
  shop_name?: string;
  shop_url?: string;
  current_price?: number;
  original_price?: number;
  min_price?: number;
  max_price?: number;
  currency: string;
  sales_count?: number;
  rating?: number;
  review_count?: number;
  stock_quantity?: number;
  category?: string;
  subcategory?: string;
  tags?: string[];
  attributes?: Record<string, any>;
  status: ProductStatus;
  is_tracked: boolean;
  is_analyzed: boolean;
  last_crawled_at?: string;
  crawl_count: number;
  crawl_errors?: any[];
  analysis_summary?: ProductAnalysisSummary;
  analysis_score?: number;
  market_position?: any;
  created_at: string;
  updated_at: string;
}

export type Platform = 'taobao' | 'tmall' | 'jd' | 'pdd';
export type ProductStatus = 'active' | 'inactive' | 'out_of_stock' | 'discontinued';

export interface ProductAnalysisSummary {
  score: number;
  recommendation: string;
  lastAnalyzed: string;
}

// 分析相关类型
export interface AnalysisResult {
  id: string;
  user_id: string;
  product_id?: string;
  title: string;
  description?: string;
  type: AnalysisType;
  status: AnalysisStatus;
  priority: Priority;
  config: any;
  input_data?: any;
  gemini_model?: string;
  result?: any;
  insights?: any;
  recommendations?: any;
  charts_data?: any;
  confidence_score?: number;
  started_at?: string;
  completed_at?: string;
  duration_ms?: number;
  error_message?: string;
  error_details?: any;
  retry_count: number;
  view_count: number;
  share_count: number;
  export_count: number;
  last_viewed_at?: string;
  share_token?: string;
  share_type: 'private' | 'public';
  share_expires_at?: string;
  tags?: string[];
  category?: string;
  is_favorite: boolean;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
}

export type AnalysisType = 'product' | 'market' | 'competitor' | 'trend';
export type AnalysisStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type Priority = 'low' | 'normal' | 'high';

// 爬虫相关类型
export interface CrawlerTask {
  id: string;
  user_id: string;
  product_id?: string;
  title: string;
  description?: string;
  type: CrawlerTaskType;
  status: CrawlerTaskStatus;
  priority: Priority;
  platform: Platform;
  target_url?: string;
  keyword?: string;
  category?: string;
  config?: any;
  filters?: any;
  started_at?: string;
  completed_at?: string;
  duration_ms?: number;
  retry_count: number;
  max_retries: number;
  total_items: number;
  success_items: number;
  failed_items: number;
  pages_crawled: number;
  error_message?: string;
  error_details?: any;
  warnings?: any[];
  crawled_data?: any;
  metadata?: any;
  proxy_used?: string;
  user_agent?: string;
  request_headers?: any;
  request_count: number;
  scheduled_at?: string;
  is_recurring: boolean;
  cron_expression?: string;
  next_run_at?: string;
  created_at: string;
  updated_at: string;
}

export type CrawlerTaskType = 'product' | 'search' | 'category' | 'shop' | 'review';
export type CrawlerTaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// 价格历史类型
export interface PriceHistory {
  id: string;
  product_id: string;
  price: number;
  original_price?: number;
  discount_amount?: number;
  discount_percentage?: number;
  currency: string;
  is_promotion: boolean;
  promotion_type?: string;
  promotion_description?: string;
  promotion_start_at?: string;
  promotion_end_at?: string;
  stock_quantity?: number;
  is_in_stock: boolean;
  sales_count?: number;
  rating?: number;
  review_count?: number;
  source: 'crawler' | 'api' | 'manual';
  crawler_task_id?: string;
  recorded_at: string;
  record_date: string;
  record_hour?: number;
  created_at: string;
  updated_at: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  code?: string;
  timestamp?: string;
  path?: string;
  method?: string;
}

export interface PaginationResponse<T = any> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// 表单类型
export interface LoginForm {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface ProductForm {
  title: string;
  url: string;
  platform: Platform;
  category?: string;
  description?: string;
}

export interface AnalysisForm {
  type: AnalysisType;
  productData?: any;
  config: any;
  priority?: Priority;
}

// 统计类型
export interface Statistics {
  totalProducts: number;
  totalAnalyses: number;
  completedAnalyses: number;
  averageScore: number;
  successRate: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  status?: string;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  action?: {
    label: string;
    url: string;
  };
}

// 主题类型
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
  };
}

// 导航类型
export interface NavItem {
  name: string;
  href: string;
  icon?: React.ComponentType<any>;
  badge?: string | number;
  children?: NavItem[];
}

// 图表数据类型
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

// 错误类型
export interface AppError {
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
}
