# 数据库配置
DATABASE_URL=postgresql://smartpick_user:smartpick_password@localhost:5432/smartpick
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smartpick
DB_USER=smartpick_user
DB_PASSWORD=smartpick_password

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# 服务器配置
NODE_ENV=development
PORT=8000
API_PREFIX=/api/v1

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
BCRYPT_ROUNDS=12

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 爬虫配置
CRAWLER_DELAY_MIN=1000
CRAWLER_DELAY_MAX=3000
CRAWLER_CONCURRENT_LIMIT=5
CRAWLER_RETRY_ATTEMPTS=3

# Chrome配置
CHROME_EXECUTABLE_PATH=/usr/bin/google-chrome
CHROMEDRIVER_PATH=/usr/local/bin/chromedriver

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# 第三方API配置
TAOBAO_API_KEY=your-taobao-api-key
JD_API_KEY=your-jd-api-key
PDD_API_KEY=your-pdd-api-key

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
