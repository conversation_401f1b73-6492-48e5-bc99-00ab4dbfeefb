# 数据库配置
DATABASE_URL=postgresql://smartpick_user:smartpick_password@localhost:5432/smartpick
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smartpick
DB_USER=smartpick_user
DB_PASSWORD=smartpick_password

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# 服务器配置
NODE_ENV=development
PORT=8000
API_PREFIX=/api/v1

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
BCRYPT_ROUNDS=12

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 数据导入配置
IMPORT_MAX_FILE_SIZE=52428800
IMPORT_MAX_ROWS=10000
IMPORT_ALLOWED_TYPES=xlsx,xls,csv
IMPORT_UPLOAD_PATH=uploads/imports

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# 第三方API配置
TAOBAO_API_KEY=your-taobao-api-key
JD_API_KEY=your-jd-api-key
PDD_API_KEY=your-pdd-api-key
GEMINI_API_KEY=your-gemini-api-key

# 加密配置
ENCRYPTION_KEY=your-super-secret-encryption-key-change-this

# Redis密码配置
REDIS_PASSWORD=your-secure-redis-password

# 安全配置
CORS_ORIGIN=http://localhost:3000

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# 队列配置
QUEUE_REDIS_URL=redis://localhost:6379
QUEUE_CONCURRENCY=5

# 通知配置
NOTIFICATION_WEBHOOK_URL=your-webhook-url
NOTIFICATION_SLACK_TOKEN=your-slack-token

# 备份配置
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_ACCESS_KEY=your-s3-access-key
BACKUP_S3_SECRET_KEY=your-s3-secret-key
BACKUP_S3_REGION=us-east-1

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
SENTRY_DSN=your-sentry-dsn
ANALYTICS_ID=your-analytics-id

# SSL配置（生产环境使用）
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# 域名配置（生产环境使用）
# DOMAIN=smartpick.example.com
# SUBDOMAIN=api.smartpick.example.com
