/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  // 获取测试用户ID
  const testUser = await knex('users').where('email', '<EMAIL>').first();
  
  if (!testUser) {
    console.log('❌ 测试用户不存在，跳过产品种子数据');
    return;
  }
  
  // 删除现有产品数据
  await knex('products').del();
  
  // 插入示例产品
  await knex('products').insert([
    {
      id: knex.raw('gen_random_uuid()'),
      user_id: testUser.id,
      title: 'iPhone 15 Pro Max 256GB 深空黑色',
      description: 'Apple iPhone 15 Pro Max，搭载A17 Pro芯片，钛金属设计，支持5G网络',
      url: 'https://item.taobao.com/item.htm?id=123456789',
      image_url: 'https://example.com/iphone15.jpg',
      brand: 'Apple',
      model: 'iPhone 15 Pro Max',
      platform: 'taobao',
      platform_product_id: '123456789',
      shop_name: 'Apple官方旗舰店',
      shop_url: 'https://apple.taobao.com',
      current_price: 9999.00,
      original_price: 10999.00,
      min_price: 9999.00,
      max_price: 10999.00,
      currency: 'CNY',
      sales_count: 15420,
      rating: 4.8,
      review_count: 8932,
      stock_quantity: 500,
      category: '手机数码',
      subcategory: '智能手机',
      tags: JSON.stringify(['苹果', '5G', '拍照', '游戏']),
      attributes: JSON.stringify({
        color: '深空黑色',
        storage: '256GB',
        network: '5G',
        screen_size: '6.7英寸'
      }),
      status: 'active',
      is_tracked: true,
      is_analyzed: false,
      last_crawled_at: knex.fn.now(),
      crawl_count: 1,
      created_at: knex.fn.now(),
      updated_at: knex.fn.now()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      user_id: testUser.id,
      title: '小米14 Ultra 16GB+1TB 黑色 徕卡影像',
      description: '小米14 Ultra，徕卡专业影像，骁龙8 Gen3处理器，2K屏幕',
      url: 'https://item.jd.com/item.htm?id=987654321',
      image_url: 'https://example.com/mi14ultra.jpg',
      brand: '小米',
      model: '小米14 Ultra',
      platform: 'jd',
      platform_product_id: '987654321',
      shop_name: '小米京东自营旗舰店',
      shop_url: 'https://xiaomi.jd.com',
      current_price: 6499.00,
      original_price: 6999.00,
      min_price: 6299.00,
      max_price: 6999.00,
      currency: 'CNY',
      sales_count: 8765,
      rating: 4.7,
      review_count: 4521,
      stock_quantity: 200,
      category: '手机数码',
      subcategory: '智能手机',
      tags: JSON.stringify(['小米', '徕卡', '拍照', '性能']),
      attributes: JSON.stringify({
        color: '黑色',
        storage: '1TB',
        ram: '16GB',
        camera: '徕卡影像'
      }),
      status: 'active',
      is_tracked: true,
      is_analyzed: true,
      last_crawled_at: knex.fn.now(),
      crawl_count: 3,
      analysis_summary: JSON.stringify({
        score: 85,
        strengths: ['性价比高', '拍照优秀', '性能强劲'],
        weaknesses: ['品牌溢价较低', '生态相对封闭'],
        market_position: '高端性价比'
      }),
      analysis_score: 85.0,
      created_at: knex.fn.now(),
      updated_at: knex.fn.now()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      user_id: testUser.id,
      title: '华为Mate60 Pro 12GB+256GB 雅川青',
      description: '华为Mate60 Pro，麒麟9000S处理器，卫星通话，超感知影像',
      url: 'https://detail.tmall.com/item.htm?id=555666777',
      image_url: 'https://example.com/mate60pro.jpg',
      brand: '华为',
      model: 'Mate60 Pro',
      platform: 'tmall',
      platform_product_id: '555666777',
      shop_name: '华为官方旗舰店',
      shop_url: 'https://huawei.tmall.com',
      current_price: 6999.00,
      original_price: 6999.00,
      min_price: 6799.00,
      max_price: 6999.00,
      currency: 'CNY',
      sales_count: 12340,
      rating: 4.9,
      review_count: 6789,
      stock_quantity: 150,
      category: '手机数码',
      subcategory: '智能手机',
      tags: JSON.stringify(['华为', '卫星通话', '商务', '拍照']),
      attributes: JSON.stringify({
        color: '雅川青',
        storage: '256GB',
        ram: '12GB',
        special: '卫星通话'
      }),
      status: 'active',
      is_tracked: false,
      is_analyzed: false,
      last_crawled_at: knex.fn.now(),
      crawl_count: 2,
      created_at: knex.fn.now(),
      updated_at: knex.fn.now()
    }
  ]);
  
  console.log('✅ 示例产品数据创建成功');
};
