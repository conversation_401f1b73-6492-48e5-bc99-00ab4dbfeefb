/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('crawler_tasks', function(table) {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 关联信息
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('product_id').nullable();
    table.foreign('product_id').references('id').inTable('products').onDelete('SET NULL');
    
    // 任务基本信息
    table.string('title', 200).notNullable();
    table.text('description').nullable();
    table.enum('type', ['product', 'search', 'category', 'shop', 'review']).notNullable();
    table.enum('status', ['pending', 'running', 'completed', 'failed', 'cancelled']).defaultTo('pending');
    table.enum('priority', ['low', 'normal', 'high']).defaultTo('normal');
    
    // 爬取配置
    table.enum('platform', ['taobao', 'tmall', 'jd', 'pdd']).notNullable();
    table.string('target_url', 1000).nullable();
    table.string('keyword', 200).nullable();
    table.string('category', 100).nullable();
    table.json('config').nullable();
    table.json('filters').nullable();
    
    // 执行信息
    table.timestamp('started_at').nullable();
    table.timestamp('completed_at').nullable();
    table.integer('duration_ms').nullable();
    table.integer('retry_count').defaultTo(0);
    table.integer('max_retries').defaultTo(3);
    
    // 结果统计
    table.integer('total_items').defaultTo(0);
    table.integer('success_items').defaultTo(0);
    table.integer('failed_items').defaultTo(0);
    table.integer('pages_crawled').defaultTo(0);
    
    // 错误信息
    table.text('error_message').nullable();
    table.json('error_details').nullable();
    table.json('warnings').nullable();
    
    // 爬取数据
    table.json('crawled_data').nullable();
    table.json('metadata').nullable();
    
    // 代理和请求信息
    table.string('proxy_used', 200).nullable();
    table.string('user_agent', 500).nullable();
    table.json('request_headers').nullable();
    table.integer('request_count').defaultTo(0);
    
    // 调度信息
    table.timestamp('scheduled_at').nullable();
    table.boolean('is_recurring').defaultTo(false);
    table.string('cron_expression', 100).nullable();
    table.timestamp('next_run_at').nullable();
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['user_id']);
    table.index(['product_id']);
    table.index(['type']);
    table.index(['status']);
    table.index(['platform']);
    table.index(['priority']);
    table.index(['is_recurring']);
    table.index(['created_at']);
    table.index(['started_at']);
    table.index(['completed_at']);
    table.index(['scheduled_at']);
    table.index(['next_run_at']);
    
    // 复合索引
    table.index(['user_id', 'status']);
    table.index(['platform', 'type']);
    table.index(['status', 'priority']);
    table.index(['is_recurring', 'next_run_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('crawler_tasks');
};
