// 基础图表组件
export { default as BaseChart } from './BaseChart';

// 价格趋势图表
export { default as PriceTrendChart } from './PriceTrendChart';

// 雷达图表
export { default as RadarChart, AnalysisRadarChart, ComparisonRadarChart } from './RadarChart';

// 销售数据图表
export { default as SalesChart } from './SalesChart';

// 饼图和环形图
export { default as PieChart, MarketShareChart, CategoryChart } from './PieChart';

// 图表工具函数
export const chartUtils = {
  // 生成颜色调色板
  generateColorPalette: (count: number) => {
    const colors = [
      '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
      '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6b7280',
    ];
    
    if (count <= colors.length) {
      return colors.slice(0, count);
    }
    
    // 如果需要更多颜色，生成渐变色
    const result = [...colors];
    const step = 360 / (count - colors.length);
    
    for (let i = colors.length; i < count; i++) {
      const hue = (i - colors.length) * step;
      result.push(`hsl(${hue}, 70%, 50%)`);
    }
    
    return result;
  },

  // 格式化数值
  formatValue: (value: number, type: 'currency' | 'number' | 'percentage' = 'number') => {
    switch (type) {
      case 'currency':
        return `¥${value.toLocaleString('zh-CN')}`;
      case 'percentage':
        return `${value.toFixed(1)}%`;
      default:
        return value.toLocaleString('zh-CN');
    }
  },

  // 计算增长率
  calculateGrowthRate: (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  },

  // 数据聚合
  aggregateData: (
    data: Array<{ date: string; value: number }>,
    period: 'daily' | 'weekly' | 'monthly'
  ) => {
    const grouped = new Map<string, number>();
    
    data.forEach(item => {
      const date = new Date(item.date);
      let key: string;
      
      switch (period) {
        case 'weekly':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split('T')[0];
          break;
        case 'monthly':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
        default:
          key = date.toISOString().split('T')[0];
      }
      
      grouped.set(key, (grouped.get(key) || 0) + item.value);
    });
    
    return Array.from(grouped.entries()).map(([date, value]) => ({
      date,
      value,
    }));
  },

  // 移动平均线
  calculateMovingAverage: (data: number[], window: number) => {
    const result: number[] = [];
    
    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - window + 1);
      const slice = data.slice(start, i + 1);
      const average = slice.reduce((sum, val) => sum + val, 0) / slice.length;
      result.push(average);
    }
    
    return result;
  },

  // 数据平滑
  smoothData: (data: number[], factor: number = 0.3) => {
    if (data.length === 0) return [];
    
    const result = [data[0]];
    
    for (let i = 1; i < data.length; i++) {
      result[i] = factor * data[i] + (1 - factor) * result[i - 1];
    }
    
    return result;
  },

  // 异常值检测
  detectOutliers: (data: number[], threshold: number = 2) => {
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const stdDev = Math.sqrt(variance);
    
    return data.map((value, index) => ({
      index,
      value,
      isOutlier: Math.abs(value - mean) > threshold * stdDev,
    }));
  },

  // 趋势分析
  analyzeTrend: (data: number[]) => {
    if (data.length < 2) return { trend: 'stable', slope: 0, correlation: 0 };
    
    const n = data.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = data;
    
    // 计算线性回归
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    const sumYY = y.reduce((sum, val) => sum + val * val, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const correlation = (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
    
    let trend: 'rising' | 'falling' | 'stable';
    if (Math.abs(slope) < 0.1) {
      trend = 'stable';
    } else if (slope > 0) {
      trend = 'rising';
    } else {
      trend = 'falling';
    }
    
    return { trend, slope, correlation };
  },
};

// 图表主题配置
export const chartThemes = {
  light: {
    background: '#ffffff',
    text: '#374151',
    grid: '#e5e7eb',
    border: '#d1d5db',
  },
  dark: {
    background: '#1f2937',
    text: '#f3f4f6',
    grid: '#374151',
    border: '#4b5563',
  },
};

// 常用图表配置预设
export const chartPresets = {
  // 简洁风格
  minimal: {
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          display: false,
        },
      },
    },
  },
  
  // 详细风格
  detailed: {
    plugins: {
      legend: {
        display: true,
        position: 'top' as const,
      },
    },
    scales: {
      x: {
        grid: {
          display: true,
        },
        ticks: {
          maxTicksLimit: 10,
        },
      },
      y: {
        grid: {
          display: true,
        },
        ticks: {
          maxTicksLimit: 8,
        },
      },
    },
  },
};
