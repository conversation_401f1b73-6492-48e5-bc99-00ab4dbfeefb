"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/modern.css":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/modern.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* 现代化UI样式系统 */\\n\\n/* CSS变量定义 */\\n:root {\\n  /* 颜色系统 */\\n  --color-primary-50: #eff6ff;\\n  --color-primary-100: #dbeafe;\\n  --color-primary-200: #bfdbfe;\\n  --color-primary-300: #93c5fd;\\n  --color-primary-400: #60a5fa;\\n  --color-primary-500: #3b82f6;\\n  --color-primary-600: #2563eb;\\n  --color-primary-700: #1d4ed8;\\n  --color-primary-800: #1e40af;\\n  --color-primary-900: #1e3a8a;\\n\\n  --color-gray-50: #f9fafb;\\n  --color-gray-100: #f3f4f6;\\n  --color-gray-200: #e5e7eb;\\n  --color-gray-300: #d1d5db;\\n  --color-gray-400: #9ca3af;\\n  --color-gray-500: #6b7280;\\n  --color-gray-600: #4b5563;\\n  --color-gray-700: #374151;\\n  --color-gray-800: #1f2937;\\n  --color-gray-900: #111827;\\n\\n  --color-success-500: #10b981;\\n  --color-success-600: #059669;\\n  --color-warning-500: #f59e0b;\\n  --color-warning-600: #d97706;\\n  --color-error-500: #ef4444;\\n  --color-error-600: #dc2626;\\n\\n  /* 间距系统 */\\n  --spacing-1: 0.25rem;\\n  --spacing-2: 0.5rem;\\n  --spacing-3: 0.75rem;\\n  --spacing-4: 1rem;\\n  --spacing-5: 1.25rem;\\n  --spacing-6: 1.5rem;\\n  --spacing-8: 2rem;\\n  --spacing-10: 2.5rem;\\n  --spacing-12: 3rem;\\n  --spacing-16: 4rem;\\n  --spacing-20: 5rem;\\n  --spacing-24: 6rem;\\n\\n  /* 圆角系统 */\\n  --radius-sm: 0.125rem;\\n  --radius-base: 0.25rem;\\n  --radius-md: 0.375rem;\\n  --radius-lg: 0.5rem;\\n  --radius-xl: 0.75rem;\\n  --radius-2xl: 1rem;\\n  --radius-full: 9999px;\\n\\n  /* 阴影系统 */\\n  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n\\n  /* 字体系统 */\\n  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\\n  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;\\n\\n  /* 图标尺寸系统 */\\n  --icon-xs: 0.75rem;\\n  --icon-sm: 1rem;\\n  --icon-base: 1.25rem;\\n  --icon-lg: 1.5rem;\\n  --icon-xl: 2rem;\\n\\n  /* 过渡动画 */\\n  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);\\n  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);\\n  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n/* 基础重置 */\\n*,\\n*::before,\\n*::after {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml {\\n  font-family: var(--font-sans);\\n  line-height: 1.6;\\n  -webkit-text-size-adjust: 100%;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  color: var(--color-gray-900);\\n  background-color: var(--color-gray-50);\\n  min-height: 100vh;\\n}\\n\\n#__next {\\n  min-height: 100vh;\\n}\\n\\n/* 现代化按钮组件 */\\n.btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--spacing-2);\\n  padding: var(--spacing-3) var(--spacing-6);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  line-height: 1.25;\\n  border-radius: var(--radius-lg);\\n  border: 1px solid transparent;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  text-decoration: none;\\n  outline: none;\\n  position: relative;\\n  min-height: 2.75rem;\\n  white-space: nowrap;\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\n\\n.btn:focus-visible {\\n  outline: 2px solid var(--color-primary-500);\\n  outline-offset: 2px;\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n\\n/* 按钮变体 */\\n.btn-primary {\\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));\\n  color: white;\\n  border-color: var(--color-primary-500);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.btn-primary:hover:not(:disabled) {\\n  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-secondary {\\n  background: white;\\n  color: var(--color-gray-700);\\n  border-color: var(--color-gray-300);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.btn-secondary:hover:not(:disabled) {\\n  background: var(--color-gray-50);\\n  border-color: var(--color-gray-400);\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-ghost {\\n  background: transparent;\\n  color: var(--color-gray-700);\\n  border-color: transparent;\\n}\\n\\n.btn-ghost:hover:not(:disabled) {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n/* 按钮尺寸 */\\n.btn-sm {\\n  padding: var(--spacing-2) var(--spacing-4);\\n  font-size: 0.75rem;\\n  min-height: 2rem;\\n}\\n\\n.btn-lg {\\n  padding: var(--spacing-4) var(--spacing-8);\\n  font-size: 1rem;\\n  min-height: 3rem;\\n}\\n\\n/* 现代化输入框组件 */\\n.input-group {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.input {\\n  display: block;\\n  width: 100%;\\n  padding: var(--spacing-3) var(--spacing-4);\\n  font-size: 0.875rem;\\n  line-height: 1.5;\\n  color: var(--color-gray-900);\\n  background: white;\\n  border: 1.5px solid var(--color-gray-300);\\n  border-radius: var(--radius-lg);\\n  transition: all var(--transition-fast);\\n  outline: none;\\n  min-height: 2.75rem;\\n}\\n\\n.input::placeholder {\\n  color: var(--color-gray-400);\\n}\\n\\n.input:focus {\\n  border-color: var(--color-primary-500);\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n\\n.input:invalid {\\n  border-color: var(--color-error-500);\\n}\\n\\n.input:invalid:focus {\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n\\n/* 浮动标签效果 */\\n.input-floating {\\n  padding-top: var(--spacing-6);\\n  padding-bottom: var(--spacing-2);\\n}\\n\\n.input-floating + .input-label {\\n  position: absolute;\\n  top: 0;\\n  left: var(--spacing-4);\\n  padding: 0 var(--spacing-1);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: var(--color-gray-600);\\n  background: white;\\n  transform: translateY(0.75rem);\\n  transition: all var(--transition-fast);\\n  pointer-events: none;\\n}\\n\\n.input-floating:focus + .input-label,\\n.input-floating:not(:placeholder-shown) + .input-label {\\n  transform: translateY(-0.5rem);\\n  font-size: 0.625rem;\\n  color: var(--color-primary-600);\\n}\\n\\n/* 现代化卡片组件 */\\n.card {\\n  background: white;\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-base);\\n  border: 1px solid var(--color-gray-200);\\n  overflow: hidden;\\n  transition: all var(--transition-base);\\n}\\n\\n.card:hover {\\n  box-shadow: var(--shadow-lg);\\n  transform: translateY(-2px);\\n}\\n\\n.card-header {\\n  padding: var(--spacing-6);\\n  border-bottom: 1px solid var(--color-gray-200);\\n  background: linear-gradient(135deg, var(--color-gray-50), white);\\n}\\n\\n.card-body {\\n  padding: var(--spacing-6);\\n}\\n\\n.card-footer {\\n  padding: var(--spacing-6);\\n  border-top: 1px solid var(--color-gray-200);\\n  background: var(--color-gray-50);\\n}\\n\\n/* 现代化徽章组件 */\\n.badge {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--spacing-1);\\n  padding: var(--spacing-1) var(--spacing-3);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border-radius: var(--radius-full);\\n  white-space: nowrap;\\n}\\n\\n.badge-primary {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-800);\\n}\\n\\n.badge-success {\\n  background: #d1fae5;\\n  color: #065f46;\\n}\\n\\n.badge-warning {\\n  background: #fef3c7;\\n  color: #92400e;\\n}\\n\\n.badge-error {\\n  background: #fee2e2;\\n  color: #991b1b;\\n}\\n\\n/* 现代化加载动画 */\\n.loading-spinner {\\n  display: inline-block;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  border: 2px solid var(--color-gray-200);\\n  border-top: 2px solid var(--color-primary-500);\\n  border-radius: 50%;\\n  animation: spin 1s linear infinite;\\n}\\n\\n@keyframes spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n/* 现代化模态框 */\\n.modal-overlay {\\n  position: fixed;\\n  inset: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  z-index: 50;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-4);\\n}\\n\\n.modal-content {\\n  background: white;\\n  border-radius: var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  max-width: 32rem;\\n  width: 100%;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  transform: scale(0.95);\\n  transition: all var(--transition-base);\\n}\\n\\n.modal-content.show {\\n  transform: scale(1);\\n}\\n\\n/* 现代化导航 */\\n.nav-link {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-3);\\n  padding: var(--spacing-3) var(--spacing-4);\\n  color: var(--color-gray-700);\\n  text-decoration: none;\\n  border-radius: var(--radius-lg);\\n  transition: all var(--transition-fast);\\n  font-weight: 500;\\n}\\n\\n.nav-link:hover {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n.nav-link.active {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-700);\\n}\\n\\n/* 现代化表格 */\\n.table {\\n  width: 100%;\\n  border-collapse: collapse;\\n  background: white;\\n  border-radius: var(--radius-lg);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-base);\\n}\\n\\n.table th {\\n  background: var(--color-gray-50);\\n  padding: var(--spacing-4);\\n  text-align: left;\\n  font-weight: 600;\\n  color: var(--color-gray-700);\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n\\n.table td {\\n  padding: var(--spacing-4);\\n  border-bottom: 1px solid var(--color-gray-100);\\n}\\n\\n.table tr:hover {\\n  background: var(--color-gray-50);\\n}\\n\\n/* 现代化表单 */\\n.form-group {\\n  margin-bottom: var(--spacing-6);\\n}\\n\\n.form-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--color-gray-700);\\n  margin-bottom: var(--spacing-2);\\n}\\n\\n.form-error {\\n  margin-top: var(--spacing-1);\\n  font-size: 0.75rem;\\n  color: var(--color-error-600);\\n}\\n\\n/* 现代化工具提示 */\\n.tooltip {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.tooltip::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: var(--color-gray-900);\\n  color: white;\\n  padding: var(--spacing-2) var(--spacing-3);\\n  border-radius: var(--radius-md);\\n  font-size: 0.75rem;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: opacity var(--transition-fast);\\n  z-index: 10;\\n}\\n\\n.tooltip:hover::after {\\n  opacity: 1;\\n}\\n\\n/* 图标尺寸类 */\\n.icon-xs {\\n  width: var(--icon-xs);\\n  height: var(--icon-xs);\\n}\\n\\n.icon-sm {\\n  width: var(--icon-sm);\\n  height: var(--icon-sm);\\n}\\n\\n.icon-base {\\n  width: var(--icon-base);\\n  height: var(--icon-base);\\n}\\n\\n.icon-lg {\\n  width: var(--icon-lg);\\n  height: var(--icon-lg);\\n}\\n\\n.icon-xl {\\n  width: var(--icon-xl);\\n  height: var(--icon-xl);\\n}\\n\\n/* 图标容器 */\\n.icon-container {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n\\n.icon-container-sm {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: var(--radius-md);\\n}\\n\\n.icon-container-md {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: var(--radius-lg);\\n}\\n\\n.icon-container-lg {\\n  width: 3rem;\\n  height: 3rem;\\n  border-radius: var(--radius-lg);\\n}\\n\\n/* Logo和品牌样式 */\\n.logo-container {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.logo-icon {\\n  width: 1rem;\\n  height: 1rem;\\n  color: white;\\n}\\n\\n/* 布局工具类 */\\n.flex {\\n  display: flex;\\n}\\n\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n\\n.items-center {\\n  align-items: center;\\n}\\n\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n\\n.font-bold {\\n  font-weight: 700;\\n}\\n\\n.text-gray-900 {\\n  color: var(--color-gray-900);\\n}\\n\\n.text-white {\\n  color: white;\\n}\\n\\n/* 链接样式 */\\n.link {\\n  text-decoration: none;\\n  color: inherit;\\n  transition: all var(--transition-fast);\\n}\\n\\n.link:hover {\\n  opacity: 0.8;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/modern.css\"],\"names\":[],\"mappings\":\"AAAA,cAAc;;AAEd,YAAY;AACZ;EACE,SAAS;EACT,2BAA2B;EAC3B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;;EAE5B,wBAAwB;EACxB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;;EAEzB,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,0BAA0B;EAC1B,0BAA0B;;EAE1B,SAAS;EACT,oBAAoB;EACpB,mBAAmB;EACnB,oBAAoB;EACpB,iBAAiB;EACjB,oBAAoB;EACpB,mBAAmB;EACnB,iBAAiB;EACjB,oBAAoB;EACpB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;;EAElB,SAAS;EACT,qBAAqB;EACrB,sBAAsB;EACtB,qBAAqB;EACrB,mBAAmB;EACnB,oBAAoB;EACpB,kBAAkB;EAClB,qBAAqB;;EAErB,SAAS;EACT,4CAA4C;EAC5C,8EAA8E;EAC9E,kFAAkF;EAClF,oFAAoF;EACpF,sFAAsF;;EAEtF,SAAS;EACT,iHAAiH;EACjH,kGAAkG;;EAElG,WAAW;EACX,kBAAkB;EAClB,eAAe;EACf,oBAAoB;EACpB,iBAAiB;EACjB,eAAe;;EAEf,SAAS;EACT,qDAAqD;EACrD,qDAAqD;EACrD,qDAAqD;AACvD;;AAEA,SAAS;AACT;;;EAGE,sBAAsB;EACtB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,6BAA6B;EAC7B,gBAAgB;EAChB,8BAA8B;EAC9B,mCAAmC;EACnC,kCAAkC;EAClC,uBAAuB;AACzB;;AAEA;EACE,4BAA4B;EAC5B,sCAAsC;EACtC,iBAAiB;AACnB;;AAEA;EACE,iBAAiB;AACnB;;AAEA,YAAY;AACZ;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,qBAAqB;EACrB,0CAA0C;EAC1C,mBAAmB;EACnB,gBAAgB;EAChB,iBAAiB;EACjB,+BAA+B;EAC/B,6BAA6B;EAC7B,eAAe;EACf,sCAAsC;EACtC,qBAAqB;EACrB,aAAa;EACb,kBAAkB;EAClB,mBAAmB;EACnB,mBAAmB;EACnB,yBAAiB;KAAjB,sBAAiB;UAAjB,iBAAiB;AACnB;;AAEA;EACE,2CAA2C;EAC3C,mBAAmB;AACrB;;AAEA;EACE,YAAY;EACZ,mBAAmB;EACnB,oBAAoB;AACtB;;AAEA,SAAS;AACT;EACE,uFAAuF;EACvF,YAAY;EACZ,sCAAsC;EACtC,4BAA4B;AAC9B;;AAEA;EACE,uFAAuF;EACvF,4BAA4B;EAC5B,2BAA2B;AAC7B;;AAEA;EACE,iBAAiB;EACjB,4BAA4B;EAC5B,mCAAmC;EACnC,4BAA4B;AAC9B;;AAEA;EACE,gCAAgC;EAChC,mCAAmC;EACnC,4BAA4B;EAC5B,2BAA2B;AAC7B;;AAEA;EACE,uBAAuB;EACvB,4BAA4B;EAC5B,yBAAyB;AAC3B;;AAEA;EACE,iCAAiC;EACjC,4BAA4B;AAC9B;;AAEA,SAAS;AACT;EACE,0CAA0C;EAC1C,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,0CAA0C;EAC1C,eAAe;EACf,gBAAgB;AAClB;;AAEA,aAAa;AACb;EACE,kBAAkB;EAClB,WAAW;AACb;;AAEA;EACE,cAAc;EACd,WAAW;EACX,0CAA0C;EAC1C,mBAAmB;EACnB,gBAAgB;EAChB,4BAA4B;EAC5B,iBAAiB;EACjB,yCAAyC;EACzC,+BAA+B;EAC/B,sCAAsC;EACtC,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,sCAAsC;EACtC,6CAA6C;AAC/C;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,4CAA4C;AAC9C;;AAEA,WAAW;AACX;EACE,6BAA6B;EAC7B,gCAAgC;AAClC;;AAEA;EACE,kBAAkB;EAClB,MAAM;EACN,sBAAsB;EACtB,2BAA2B;EAC3B,kBAAkB;EAClB,gBAAgB;EAChB,4BAA4B;EAC5B,iBAAiB;EACjB,8BAA8B;EAC9B,sCAAsC;EACtC,oBAAoB;AACtB;;AAEA;;EAEE,8BAA8B;EAC9B,mBAAmB;EACnB,+BAA+B;AACjC;;AAEA,YAAY;AACZ;EACE,iBAAiB;EACjB,+BAA+B;EAC/B,8BAA8B;EAC9B,uCAAuC;EACvC,gBAAgB;EAChB,sCAAsC;AACxC;;AAEA;EACE,4BAA4B;EAC5B,2BAA2B;AAC7B;;AAEA;EACE,yBAAyB;EACzB,8CAA8C;EAC9C,gEAAgE;AAClE;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,2CAA2C;EAC3C,gCAAgC;AAClC;;AAEA,YAAY;AACZ;EACE,oBAAoB;EACpB,mBAAmB;EACnB,qBAAqB;EACrB,0CAA0C;EAC1C,kBAAkB;EAClB,gBAAgB;EAChB,iCAAiC;EACjC,mBAAmB;AACrB;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;AACjC;;AAEA;EACE,mBAAmB;EACnB,cAAc;AAChB;;AAEA;EACE,mBAAmB;EACnB,cAAc;AAChB;;AAEA;EACE,mBAAmB;EACnB,cAAc;AAChB;;AAEA,YAAY;AACZ;EACE,qBAAqB;EACrB,aAAa;EACb,cAAc;EACd,uCAAuC;EACvC,8CAA8C;EAC9C,kBAAkB;EAClB,kCAAkC;AACpC;;AAEA;EACE,KAAK,uBAAuB,EAAE;EAC9B,OAAO,yBAAyB,EAAE;AACpC;;AAEA,WAAW;AACX;EACE,eAAe;EACf,QAAQ;EACR,8BAA8B;EAC9B,kCAA0B;UAA1B,0BAA0B;EAC1B,WAAW;EACX,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,yBAAyB;AAC3B;;AAEA;EACE,iBAAiB;EACjB,gCAAgC;EAChC,4BAA4B;EAC5B,gBAAgB;EAChB,WAAW;EACX,gBAAgB;EAChB,gBAAgB;EAChB,sBAAsB;EACtB,sCAAsC;AACxC;;AAEA;EACE,mBAAmB;AACrB;;AAEA,UAAU;AACV;EACE,aAAa;EACb,mBAAmB;EACnB,qBAAqB;EACrB,0CAA0C;EAC1C,4BAA4B;EAC5B,qBAAqB;EACrB,+BAA+B;EAC/B,sCAAsC;EACtC,gBAAgB;AAClB;;AAEA;EACE,iCAAiC;EACjC,4BAA4B;AAC9B;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;AACjC;;AAEA,UAAU;AACV;EACE,WAAW;EACX,yBAAyB;EACzB,iBAAiB;EACjB,+BAA+B;EAC/B,gBAAgB;EAChB,8BAA8B;AAChC;;AAEA;EACE,gCAAgC;EAChC,yBAAyB;EACzB,gBAAgB;EAChB,gBAAgB;EAChB,4BAA4B;EAC5B,8CAA8C;AAChD;;AAEA;EACE,yBAAyB;EACzB,8CAA8C;AAChD;;AAEA;EACE,gCAAgC;AAClC;;AAEA,UAAU;AACV;EACE,+BAA+B;AACjC;;AAEA;EACE,cAAc;EACd,mBAAmB;EACnB,gBAAgB;EAChB,4BAA4B;EAC5B,+BAA+B;AACjC;;AAEA;EACE,4BAA4B;EAC5B,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA,YAAY;AACZ;EACE,kBAAkB;EAClB,qBAAqB;AACvB;;AAEA;EACE,2BAA2B;EAC3B,kBAAkB;EAClB,YAAY;EACZ,SAAS;EACT,2BAA2B;EAC3B,iCAAiC;EACjC,YAAY;EACZ,0CAA0C;EAC1C,+BAA+B;EAC/B,kBAAkB;EAClB,mBAAmB;EACnB,UAAU;EACV,oBAAoB;EACpB,0CAA0C;EAC1C,WAAW;AACb;;AAEA;EACE,UAAU;AACZ;;AAEA,UAAU;AACV;EACE,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA;EACE,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA;EACE,uBAAuB;EACvB,wBAAwB;AAC1B;;AAEA;EACE,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA;EACE,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA,SAAS;AACT;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,cAAc;AAChB;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,cAAc;EACd,+BAA+B;AACjC;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,+BAA+B;AACjC;;AAEA,cAAc;AACd;EACE,aAAa;EACb,cAAc;EACd,uFAAuF;EACvF,+BAA+B;EAC/B,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,4BAA4B;AAC9B;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,YAAY;AACd;;AAEA,UAAU;AACV;EACE,aAAa;AACf;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,kBAAkB;EAClB,oBAAoB;AACtB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,YAAY;AACd;;AAEA,SAAS;AACT;EACE,qBAAqB;EACrB,cAAc;EACd,sCAAsC;AACxC;;AAEA;EACE,YAAY;AACd\",\"sourcesContent\":[\"/* 现代化UI样式系统 */\\n\\n/* CSS变量定义 */\\n:root {\\n  /* 颜色系统 */\\n  --color-primary-50: #eff6ff;\\n  --color-primary-100: #dbeafe;\\n  --color-primary-200: #bfdbfe;\\n  --color-primary-300: #93c5fd;\\n  --color-primary-400: #60a5fa;\\n  --color-primary-500: #3b82f6;\\n  --color-primary-600: #2563eb;\\n  --color-primary-700: #1d4ed8;\\n  --color-primary-800: #1e40af;\\n  --color-primary-900: #1e3a8a;\\n\\n  --color-gray-50: #f9fafb;\\n  --color-gray-100: #f3f4f6;\\n  --color-gray-200: #e5e7eb;\\n  --color-gray-300: #d1d5db;\\n  --color-gray-400: #9ca3af;\\n  --color-gray-500: #6b7280;\\n  --color-gray-600: #4b5563;\\n  --color-gray-700: #374151;\\n  --color-gray-800: #1f2937;\\n  --color-gray-900: #111827;\\n\\n  --color-success-500: #10b981;\\n  --color-success-600: #059669;\\n  --color-warning-500: #f59e0b;\\n  --color-warning-600: #d97706;\\n  --color-error-500: #ef4444;\\n  --color-error-600: #dc2626;\\n\\n  /* 间距系统 */\\n  --spacing-1: 0.25rem;\\n  --spacing-2: 0.5rem;\\n  --spacing-3: 0.75rem;\\n  --spacing-4: 1rem;\\n  --spacing-5: 1.25rem;\\n  --spacing-6: 1.5rem;\\n  --spacing-8: 2rem;\\n  --spacing-10: 2.5rem;\\n  --spacing-12: 3rem;\\n  --spacing-16: 4rem;\\n  --spacing-20: 5rem;\\n  --spacing-24: 6rem;\\n\\n  /* 圆角系统 */\\n  --radius-sm: 0.125rem;\\n  --radius-base: 0.25rem;\\n  --radius-md: 0.375rem;\\n  --radius-lg: 0.5rem;\\n  --radius-xl: 0.75rem;\\n  --radius-2xl: 1rem;\\n  --radius-full: 9999px;\\n\\n  /* 阴影系统 */\\n  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n\\n  /* 字体系统 */\\n  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\\n  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;\\n\\n  /* 图标尺寸系统 */\\n  --icon-xs: 0.75rem;\\n  --icon-sm: 1rem;\\n  --icon-base: 1.25rem;\\n  --icon-lg: 1.5rem;\\n  --icon-xl: 2rem;\\n\\n  /* 过渡动画 */\\n  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);\\n  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);\\n  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n/* 基础重置 */\\n*,\\n*::before,\\n*::after {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml {\\n  font-family: var(--font-sans);\\n  line-height: 1.6;\\n  -webkit-text-size-adjust: 100%;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  color: var(--color-gray-900);\\n  background-color: var(--color-gray-50);\\n  min-height: 100vh;\\n}\\n\\n#__next {\\n  min-height: 100vh;\\n}\\n\\n/* 现代化按钮组件 */\\n.btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--spacing-2);\\n  padding: var(--spacing-3) var(--spacing-6);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  line-height: 1.25;\\n  border-radius: var(--radius-lg);\\n  border: 1px solid transparent;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  text-decoration: none;\\n  outline: none;\\n  position: relative;\\n  min-height: 2.75rem;\\n  white-space: nowrap;\\n  user-select: none;\\n}\\n\\n.btn:focus-visible {\\n  outline: 2px solid var(--color-primary-500);\\n  outline-offset: 2px;\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n\\n/* 按钮变体 */\\n.btn-primary {\\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));\\n  color: white;\\n  border-color: var(--color-primary-500);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.btn-primary:hover:not(:disabled) {\\n  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-secondary {\\n  background: white;\\n  color: var(--color-gray-700);\\n  border-color: var(--color-gray-300);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.btn-secondary:hover:not(:disabled) {\\n  background: var(--color-gray-50);\\n  border-color: var(--color-gray-400);\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-ghost {\\n  background: transparent;\\n  color: var(--color-gray-700);\\n  border-color: transparent;\\n}\\n\\n.btn-ghost:hover:not(:disabled) {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n/* 按钮尺寸 */\\n.btn-sm {\\n  padding: var(--spacing-2) var(--spacing-4);\\n  font-size: 0.75rem;\\n  min-height: 2rem;\\n}\\n\\n.btn-lg {\\n  padding: var(--spacing-4) var(--spacing-8);\\n  font-size: 1rem;\\n  min-height: 3rem;\\n}\\n\\n/* 现代化输入框组件 */\\n.input-group {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.input {\\n  display: block;\\n  width: 100%;\\n  padding: var(--spacing-3) var(--spacing-4);\\n  font-size: 0.875rem;\\n  line-height: 1.5;\\n  color: var(--color-gray-900);\\n  background: white;\\n  border: 1.5px solid var(--color-gray-300);\\n  border-radius: var(--radius-lg);\\n  transition: all var(--transition-fast);\\n  outline: none;\\n  min-height: 2.75rem;\\n}\\n\\n.input::placeholder {\\n  color: var(--color-gray-400);\\n}\\n\\n.input:focus {\\n  border-color: var(--color-primary-500);\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n\\n.input:invalid {\\n  border-color: var(--color-error-500);\\n}\\n\\n.input:invalid:focus {\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n\\n/* 浮动标签效果 */\\n.input-floating {\\n  padding-top: var(--spacing-6);\\n  padding-bottom: var(--spacing-2);\\n}\\n\\n.input-floating + .input-label {\\n  position: absolute;\\n  top: 0;\\n  left: var(--spacing-4);\\n  padding: 0 var(--spacing-1);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: var(--color-gray-600);\\n  background: white;\\n  transform: translateY(0.75rem);\\n  transition: all var(--transition-fast);\\n  pointer-events: none;\\n}\\n\\n.input-floating:focus + .input-label,\\n.input-floating:not(:placeholder-shown) + .input-label {\\n  transform: translateY(-0.5rem);\\n  font-size: 0.625rem;\\n  color: var(--color-primary-600);\\n}\\n\\n/* 现代化卡片组件 */\\n.card {\\n  background: white;\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-base);\\n  border: 1px solid var(--color-gray-200);\\n  overflow: hidden;\\n  transition: all var(--transition-base);\\n}\\n\\n.card:hover {\\n  box-shadow: var(--shadow-lg);\\n  transform: translateY(-2px);\\n}\\n\\n.card-header {\\n  padding: var(--spacing-6);\\n  border-bottom: 1px solid var(--color-gray-200);\\n  background: linear-gradient(135deg, var(--color-gray-50), white);\\n}\\n\\n.card-body {\\n  padding: var(--spacing-6);\\n}\\n\\n.card-footer {\\n  padding: var(--spacing-6);\\n  border-top: 1px solid var(--color-gray-200);\\n  background: var(--color-gray-50);\\n}\\n\\n/* 现代化徽章组件 */\\n.badge {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--spacing-1);\\n  padding: var(--spacing-1) var(--spacing-3);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border-radius: var(--radius-full);\\n  white-space: nowrap;\\n}\\n\\n.badge-primary {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-800);\\n}\\n\\n.badge-success {\\n  background: #d1fae5;\\n  color: #065f46;\\n}\\n\\n.badge-warning {\\n  background: #fef3c7;\\n  color: #92400e;\\n}\\n\\n.badge-error {\\n  background: #fee2e2;\\n  color: #991b1b;\\n}\\n\\n/* 现代化加载动画 */\\n.loading-spinner {\\n  display: inline-block;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  border: 2px solid var(--color-gray-200);\\n  border-top: 2px solid var(--color-primary-500);\\n  border-radius: 50%;\\n  animation: spin 1s linear infinite;\\n}\\n\\n@keyframes spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n/* 现代化模态框 */\\n.modal-overlay {\\n  position: fixed;\\n  inset: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  backdrop-filter: blur(4px);\\n  z-index: 50;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-4);\\n}\\n\\n.modal-content {\\n  background: white;\\n  border-radius: var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  max-width: 32rem;\\n  width: 100%;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  transform: scale(0.95);\\n  transition: all var(--transition-base);\\n}\\n\\n.modal-content.show {\\n  transform: scale(1);\\n}\\n\\n/* 现代化导航 */\\n.nav-link {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-3);\\n  padding: var(--spacing-3) var(--spacing-4);\\n  color: var(--color-gray-700);\\n  text-decoration: none;\\n  border-radius: var(--radius-lg);\\n  transition: all var(--transition-fast);\\n  font-weight: 500;\\n}\\n\\n.nav-link:hover {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n.nav-link.active {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-700);\\n}\\n\\n/* 现代化表格 */\\n.table {\\n  width: 100%;\\n  border-collapse: collapse;\\n  background: white;\\n  border-radius: var(--radius-lg);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-base);\\n}\\n\\n.table th {\\n  background: var(--color-gray-50);\\n  padding: var(--spacing-4);\\n  text-align: left;\\n  font-weight: 600;\\n  color: var(--color-gray-700);\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n\\n.table td {\\n  padding: var(--spacing-4);\\n  border-bottom: 1px solid var(--color-gray-100);\\n}\\n\\n.table tr:hover {\\n  background: var(--color-gray-50);\\n}\\n\\n/* 现代化表单 */\\n.form-group {\\n  margin-bottom: var(--spacing-6);\\n}\\n\\n.form-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--color-gray-700);\\n  margin-bottom: var(--spacing-2);\\n}\\n\\n.form-error {\\n  margin-top: var(--spacing-1);\\n  font-size: 0.75rem;\\n  color: var(--color-error-600);\\n}\\n\\n/* 现代化工具提示 */\\n.tooltip {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.tooltip::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: var(--color-gray-900);\\n  color: white;\\n  padding: var(--spacing-2) var(--spacing-3);\\n  border-radius: var(--radius-md);\\n  font-size: 0.75rem;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: opacity var(--transition-fast);\\n  z-index: 10;\\n}\\n\\n.tooltip:hover::after {\\n  opacity: 1;\\n}\\n\\n/* 图标尺寸类 */\\n.icon-xs {\\n  width: var(--icon-xs);\\n  height: var(--icon-xs);\\n}\\n\\n.icon-sm {\\n  width: var(--icon-sm);\\n  height: var(--icon-sm);\\n}\\n\\n.icon-base {\\n  width: var(--icon-base);\\n  height: var(--icon-base);\\n}\\n\\n.icon-lg {\\n  width: var(--icon-lg);\\n  height: var(--icon-lg);\\n}\\n\\n.icon-xl {\\n  width: var(--icon-xl);\\n  height: var(--icon-xl);\\n}\\n\\n/* 图标容器 */\\n.icon-container {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n\\n.icon-container-sm {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: var(--radius-md);\\n}\\n\\n.icon-container-md {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: var(--radius-lg);\\n}\\n\\n.icon-container-lg {\\n  width: 3rem;\\n  height: 3rem;\\n  border-radius: var(--radius-lg);\\n}\\n\\n/* Logo和品牌样式 */\\n.logo-container {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.logo-icon {\\n  width: 1rem;\\n  height: 1rem;\\n  color: white;\\n}\\n\\n/* 布局工具类 */\\n.flex {\\n  display: flex;\\n}\\n\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n\\n.items-center {\\n  align-items: center;\\n}\\n\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n\\n.font-bold {\\n  font-weight: 700;\\n}\\n\\n.text-gray-900 {\\n  color: var(--color-gray-900);\\n}\\n\\n.text-white {\\n  color: white;\\n}\\n\\n/* 链接样式 */\\n.link {\\n  text-decoration: none;\\n  color: inherit;\\n  transition: all var(--transition-fast);\\n}\\n\\n.link:hover {\\n  opacity: 0.8;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/modern.css\n"));

/***/ })

});