// 简单的启动测试
console.log('开始测试...');

try {
  console.log('1. 测试环境变量...');
  require('dotenv').config();
  console.log('✓ 环境变量加载成功');

  console.log('2. 测试Prisma...');
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();
  console.log('✓ Prisma客户端创建成功');

  console.log('3. 测试Express...');
  const express = require('express');
  const app = express();
  console.log('✓ Express应用创建成功');

  console.log('4. 测试数据库连接...');
  prisma.$connect().then(() => {
    console.log('✓ 数据库连接成功');
    
    console.log('5. 启动服务器...');
    const PORT = process.env.PORT || 8000;
    app.listen(PORT, () => {
      console.log(`✓ 服务器启动成功，端口: ${PORT}`);
    });
  }).catch(error => {
    console.error('✗ 数据库连接失败:', error);
  });

} catch (error) {
  console.error('✗ 启动测试失败:', error);
}
