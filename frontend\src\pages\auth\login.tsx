import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/LoadingScreen';
import toast from 'react-hot-toast';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // 如果已经登录，重定向到仪表板
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, authLoading, router]);

  // 如果正在加载认证状态，显示加载界面
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // 如果已经认证，不显示登录表单
  if (isAuthenticated) {
    return null;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast.error('请填写所有字段');
      return;
    }

    setIsLoading(true);
    
    try {
      await login({ email, password });
      toast.success('登录成功');
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.message || '登录失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestLogin = async () => {
    setEmail('<EMAIL>');
    setPassword('test123');
    
    setIsLoading(true);
    try {
      await login({ email: '<EMAIL>', password: 'test123' });
      toast.success('登录成功');
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.message || '登录失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '3rem 1rem'
    }}>
      <div className="card" style={{ maxWidth: '28rem', width: '100%' }}>
        <div className="card-body" style={{ padding: '2rem' }}>
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <div style={{
              width: '4rem',
              height: '4rem',
              margin: '0 auto 1.5rem',
              background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
              borderRadius: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: 'var(--shadow-lg)'
            }}>
              <svg style={{ width: '2rem', height: '2rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h1 style={{
              fontSize: '1.875rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              欢迎回来
            </h1>
            <p style={{
              fontSize: '0.875rem',
              color: 'var(--color-gray-600)'
            }}>
              登录到 SmartPick 智能选品分析平台
            </p>
          </div>
        
          <form onSubmit={handleSubmit} style={{ marginTop: '1.5rem' }}>
            <div style={{ marginBottom: '1.5rem' }}>
              <div className="form-group">
                <div className="input-group">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="input input-floating"
                    placeholder="请输入邮箱地址"
                  />
                  <label htmlFor="email" className="input-label">
                    邮箱地址
                  </label>
                </div>
              </div>
            
              <div className="form-group">
                <div className="input-group">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="input input-floating"
                    placeholder="请输入密码"
                    style={{ paddingRight: '3rem' }}
                  />
                  <label htmlFor="password" className="input-label">
                    密码
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    style={{
                      position: 'absolute',
                      right: '0.75rem',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      color: 'var(--color-gray-400)',
                      padding: '0.25rem'
                    }}
                  >
                    {showPassword ? (
                      <EyeSlashIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    ) : (
                      <EyeIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '1.5rem'
            }}>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                color: 'var(--color-gray-700)',
                cursor: 'pointer'
              }}>
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  style={{
                    width: '1rem',
                    height: '1rem',
                    accentColor: 'var(--color-primary-500)'
                  }}
                />
                记住我
              </label>

              <Link
                href="/auth/forgot-password"
                style={{
                  fontSize: '0.875rem',
                  color: 'var(--color-primary-600)',
                  textDecoration: 'none',
                  fontWeight: '500'
                }}
              >
                忘记密码？
              </Link>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              <button
                type="submit"
                disabled={isLoading}
                className="btn btn-primary"
                style={{ width: '100%' }}
              >
                {isLoading ? (
                  <>
                    <div className="loading-spinner" style={{ width: '1rem', height: '1rem' }}></div>
                    登录中...
                  </>
                ) : (
                  '登录'
                )}
              </button>

              <button
                type="button"
                onClick={handleTestLogin}
                disabled={isLoading}
                className="btn btn-secondary"
                style={{ width: '100%' }}
              >
                使用测试账户登录
              </button>
            </div>

            <div style={{
              textAlign: 'center',
              marginTop: '1.5rem',
              paddingTop: '1.5rem',
              borderTop: '1px solid var(--color-gray-200)'
            }}>
              <span style={{
                fontSize: '0.875rem',
                color: 'var(--color-gray-600)'
              }}>
                还没有账户？{' '}
                <Link
                  href="/auth/register"
                  style={{
                    color: 'var(--color-primary-600)',
                    textDecoration: 'none',
                    fontWeight: '500'
                  }}
                >
                  立即注册
                </Link>
              </span>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
