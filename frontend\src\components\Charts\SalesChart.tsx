import React from 'react';
import BaseChart from './BaseChart';

interface SalesData {
  date: string;
  sales: number;
  revenue?: number;
  orders?: number;
}

interface SalesChartProps {
  data: SalesData[];
  type?: 'sales' | 'revenue' | 'orders' | 'combined';
  chartType?: 'bar' | 'line' | 'area';
  title?: string;
  height?: number;
  className?: string;
  period?: 'daily' | 'weekly' | 'monthly';
}

const SalesChart: React.FC<SalesChartProps> = ({
  data,
  type = 'sales',
  chartType = 'bar',
  title,
  height = 300,
  className = '',
  period = 'daily',
}) => {
  // 格式化日期标签
  const formatDateLabel = (dateString: string) => {
    const date = new Date(dateString);
    
    switch (period) {
      case 'daily':
        return date.toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric',
        });
      case 'weekly':
        return `第${Math.ceil(date.getDate() / 7)}周`;
      case 'monthly':
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'short',
        });
      default:
        return date.toLocaleDateString('zh-CN');
    }
  };

  // 处理数据
  const processedData = React.useMemo(() => {
    const sortedData = [...data].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    const labels = sortedData.map(item => formatDateLabel(item.date));

    const datasets = [];

    // 根据类型添加数据集
    if (type === 'sales' || type === 'combined') {
      datasets.push({
        label: '销量',
        data: sortedData.map(item => item.sales),
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
        yAxisID: 'y',
      });
    }

    if (type === 'revenue' || type === 'combined') {
      datasets.push({
        label: '收入',
        data: sortedData.map(item => item.revenue || 0),
        backgroundColor: 'rgba(16, 185, 129, 0.8)',
        borderColor: 'rgb(16, 185, 129)',
        borderWidth: 2,
        yAxisID: type === 'combined' ? 'y1' : 'y',
      });
    }

    if (type === 'orders' || type === 'combined') {
      datasets.push({
        label: '订单数',
        data: sortedData.map(item => item.orders || 0),
        backgroundColor: 'rgba(245, 158, 11, 0.8)',
        borderColor: 'rgb(245, 158, 11)',
        borderWidth: 2,
        yAxisID: 'y',
      });
    }

    return { labels, datasets };
  }, [data, type, period]);

  // 图表配置
  const options = React.useMemo(() => {
    const baseOptions = {
      plugins: {
        tooltip: {
          callbacks: {
            label: function(context: any) {
              const label = context.dataset.label || '';
              const value = context.parsed.y;
              
              if (label === '收入') {
                return `${label}: ¥${value.toLocaleString('zh-CN')}`;
              } else {
                return `${label}: ${value.toLocaleString('zh-CN')}`;
              }
            },
          },
        },
        legend: {
          display: type === 'combined',
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          position: 'left' as const,
          ticks: {
            callback: function(value: any) {
              if (type === 'revenue') {
                return '¥' + value.toLocaleString('zh-CN');
              }
              return value.toLocaleString('zh-CN');
            },
          },
        },
        ...(type === 'combined' && {
          y1: {
            type: 'linear' as const,
            display: true,
            position: 'right' as const,
            beginAtZero: true,
            grid: {
              drawOnChartArea: false,
            },
            ticks: {
              callback: function(value: any) {
                return '¥' + value.toLocaleString('zh-CN');
              },
            },
          },
        }),
      },
    };

    return baseOptions;
  }, [type]);

  // 计算统计信息
  const stats = React.useMemo(() => {
    if (data.length === 0) return null;

    const totalSales = data.reduce((sum, item) => sum + item.sales, 0);
    const totalRevenue = data.reduce((sum, item) => sum + (item.revenue || 0), 0);
    const totalOrders = data.reduce((sum, item) => sum + (item.orders || 0), 0);
    const avgSales = totalSales / data.length;
    const avgRevenue = totalRevenue / data.length;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // 计算增长率
    const currentPeriod = data.slice(-Math.ceil(data.length / 2));
    const previousPeriod = data.slice(0, Math.floor(data.length / 2));
    
    const currentSales = currentPeriod.reduce((sum, item) => sum + item.sales, 0);
    const previousSales = previousPeriod.reduce((sum, item) => sum + item.sales, 0);
    const salesGrowth = previousSales > 0 ? ((currentSales - previousSales) / previousSales) * 100 : 0;

    return {
      totalSales,
      totalRevenue,
      totalOrders,
      avgSales,
      avgRevenue,
      avgOrderValue,
      salesGrowth,
    };
  }, [data]);

  const getTitle = () => {
    if (title) return title;
    
    const typeLabels = {
      sales: '销量趋势',
      revenue: '收入趋势',
      orders: '订单趋势',
      combined: '销售数据概览',
    };
    
    return typeLabels[type];
  };

  return (
    <div className={className}>
      <BaseChart
        type={chartType}
        data={processedData}
        options={options}
        height={height}
        title={getTitle()}
      />
      
      {/* 统计信息 */}
      {stats && (
        <div className="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-4">
          {(type === 'sales' || type === 'combined') && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
              <div className="text-xs text-blue-600 dark:text-blue-400">总销量</div>
              <div className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                {stats.totalSales.toLocaleString('zh-CN')}
              </div>
              <div className="text-xs text-blue-600 dark:text-blue-400">
                平均: {Math.round(stats.avgSales).toLocaleString('zh-CN')}
              </div>
            </div>
          )}
          
          {(type === 'revenue' || type === 'combined') && (
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
              <div className="text-xs text-green-600 dark:text-green-400">总收入</div>
              <div className="text-lg font-semibold text-green-900 dark:text-green-100">
                ¥{stats.totalRevenue.toLocaleString('zh-CN')}
              </div>
              <div className="text-xs text-green-600 dark:text-green-400">
                平均: ¥{Math.round(stats.avgRevenue).toLocaleString('zh-CN')}
              </div>
            </div>
          )}
          
          {(type === 'orders' || type === 'combined') && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-3">
              <div className="text-xs text-yellow-600 dark:text-yellow-400">总订单</div>
              <div className="text-lg font-semibold text-yellow-900 dark:text-yellow-100">
                {stats.totalOrders.toLocaleString('zh-CN')}
              </div>
              <div className="text-xs text-yellow-600 dark:text-yellow-400">
                客单价: ¥{Math.round(stats.avgOrderValue).toLocaleString('zh-CN')}
              </div>
            </div>
          )}
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="text-xs text-gray-500 dark:text-gray-400">销量增长</div>
            <div className={`text-lg font-semibold ${
              stats.salesGrowth >= 0 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-red-600 dark:text-red-400'
            }`}>
              {stats.salesGrowth >= 0 ? '+' : ''}
              {stats.salesGrowth.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              环比上期
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SalesChart;
