/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 基本信息
    table.string('username', 50).notNullable().unique();
    table.string('email', 255).notNullable().unique();
    table.string('password_hash', 255).notNullable();
    table.string('phone', 20).nullable();
    table.text('bio').nullable();
    table.string('avatar_url', 500).nullable();
    
    // 角色和状态
    table.enum('role', ['user', 'admin']).defaultTo('user');
    table.enum('status', ['active', 'inactive', 'banned']).defaultTo('active');
    
    // 邮箱验证
    table.boolean('email_verified').defaultTo(false);
    table.string('email_verification_token', 255).nullable();
    table.timestamp('email_verification_expires_at').nullable();
    
    // 密码重置
    table.string('password_reset_token', 255).nullable();
    table.timestamp('password_reset_expires_at').nullable();
    
    // Gemini API配置
    table.text('gemini_api_key_encrypted').nullable();
    table.string('gemini_model', 50).defaultTo('gemini-pro');
    table.json('gemini_config').nullable();
    
    // 用户偏好设置
    table.json('preferences').nullable();
    table.string('timezone', 50).defaultTo('Asia/Shanghai');
    table.string('language', 10).defaultTo('zh-CN');
    
    // 统计信息
    table.integer('analysis_count').defaultTo(0);
    table.integer('product_count').defaultTo(0);
    table.timestamp('last_login_at').nullable();
    table.string('last_login_ip', 45).nullable();
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['email']);
    table.index(['username']);
    table.index(['status']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
