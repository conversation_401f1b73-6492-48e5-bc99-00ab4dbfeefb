const { validationResult } = require('express-validator');
const { createError } = require('./errorHandler');

/**
 * 验证请求中间件
 */
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));
    
    const error = createError.badRequest(
      '请求参数验证失败',
      'VALIDATION_ERROR',
      errorMessages
    );
    
    return next(error);
  }
  
  next();
};

/**
 * 自定义验证器
 */
const customValidators = {
  /**
   * 验证中国手机号
   */
  isChineseMobile: (value) => {
    const mobileRegex = /^1[3-9]\d{9}$/;
    return mobileRegex.test(value);
  },

  /**
   * 验证强密码
   */
  isStrongPassword: (value) => {
    // 至少8位，包含大小写字母、数字和特殊字符
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return strongPasswordRegex.test(value);
  },

  /**
   * 验证用户名格式
   */
  isValidUsername: (value) => {
    // 2-20位，只能包含字母、数字、下划线和中文
    const usernameRegex = /^[a-zA-Z0-9_\u4e00-\u9fa5]{2,20}$/;
    return usernameRegex.test(value);
  },

  /**
   * 验证商品URL
   */
  isProductUrl: (value) => {
    const urlPatterns = [
      /^https?:\/\/(item\.)?taobao\.com\/item\.htm\?.*id=\d+/,
      /^https?:\/\/detail\.tmall\.com\/item\.htm\?.*id=\d+/,
      /^https?:\/\/item\.jd\.com\/\d+\.html/,
      /^https?:\/\/mobile\.yangkeduo\.com\/goods\.html\?.*goods_id=\d+/
    ];
    
    return urlPatterns.some(pattern => pattern.test(value));
  },

  /**
   * 验证价格范围
   */
  isPriceRange: (value) => {
    if (typeof value !== 'object' || !value.min || !value.max) {
      return false;
    }
    
    const min = parseFloat(value.min);
    const max = parseFloat(value.max);
    
    return !isNaN(min) && !isNaN(max) && min >= 0 && max >= min;
  },

  /**
   * 验证日期范围
   */
  isDateRange: (value) => {
    if (typeof value !== 'object' || !value.start || !value.end) {
      return false;
    }
    
    const startDate = new Date(value.start);
    const endDate = new Date(value.end);
    
    return !isNaN(startDate.getTime()) && !isNaN(endDate.getTime()) && startDate <= endDate;
  },

  /**
   * 验证颜色值
   */
  isColor: (value) => {
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return colorRegex.test(value);
  },

  /**
   * 验证JSON字符串
   */
  isValidJSON: (value) => {
    try {
      JSON.parse(value);
      return true;
    } catch (error) {
      return false;
    }
  },

  /**
   * 验证数组长度
   */
  isArrayLength: (min, max) => {
    return (value) => {
      if (!Array.isArray(value)) {
        return false;
      }
      return value.length >= min && value.length <= max;
    };
  },

  /**
   * 验证文件类型
   */
  isFileType: (allowedTypes) => {
    return (value) => {
      if (!value || !value.mimetype) {
        return false;
      }
      return allowedTypes.includes(value.mimetype);
    };
  },

  /**
   * 验证文件大小
   */
  isFileSize: (maxSize) => {
    return (value) => {
      if (!value || !value.size) {
        return false;
      }
      return value.size <= maxSize;
    };
  }
};

/**
 * 条件验证中间件
 */
const conditionalValidation = (condition, validations) => {
  return (req, res, next) => {
    if (condition(req)) {
      // 如果条件满足，应用验证规则
      const validationChain = Array.isArray(validations) ? validations : [validations];
      
      let index = 0;
      const runValidation = () => {
        if (index >= validationChain.length) {
          return validateRequest(req, res, next);
        }
        
        const validation = validationChain[index++];
        validation.run(req).then(() => {
          runValidation();
        });
      };
      
      runValidation();
    } else {
      // 条件不满足，跳过验证
      next();
    }
  };
};

/**
 * 批量验证中间件
 */
const batchValidation = (validationGroups) => {
  return async (req, res, next) => {
    const errors = [];
    
    for (const group of validationGroups) {
      const { condition, validations, name } = group;
      
      if (!condition || condition(req)) {
        const validationChain = Array.isArray(validations) ? validations : [validations];
        
        for (const validation of validationChain) {
          await validation.run(req);
        }
        
        const result = validationResult(req);
        if (!result.isEmpty()) {
          errors.push({
            group: name || 'default',
            errors: result.array()
          });
        }
      }
    }
    
    if (errors.length > 0) {
      const error = createError.badRequest(
        '批量验证失败',
        'BATCH_VALIDATION_ERROR',
        errors
      );
      return next(error);
    }
    
    next();
  };
};

/**
 * 异步验证中间件
 */
const asyncValidation = (asyncValidator) => {
  return async (req, res, next) => {
    try {
      const isValid = await asyncValidator(req);
      if (!isValid) {
        const error = createError.badRequest('异步验证失败', 'ASYNC_VALIDATION_ERROR');
        return next(error);
      }
      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  validateRequest,
  customValidators,
  conditionalValidation,
  batchValidation,
  asyncValidation
};
