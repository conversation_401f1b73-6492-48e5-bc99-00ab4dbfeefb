const analysisService = require('../services/analysisService');
const geminiService = require('../services/geminiService');
const { createError } = require('../middleware/errorHandler');
const { db } = require('../config/database');
const logger = require('../utils/logger');
const Bull = require('bull');

// 创建分析任务队列
const analysisQueue = new Bull('analysis tasks', {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  }
});

// 处理分析任务
analysisQueue.process('product-analysis', async (job) => {
  const { analysisId, productData, options, userId, apiKey } = job.data;
  
  try {
    // 更新任务状态
    await db('analysis_results')
      .where({ id: analysisId })
      .update({
        status: 'running',
        started_at: new Date()
      });
    
    // 执行分析
    const result = await analysisService.analyzeProduct(productData, options);
    
    // 如果有Gemini API密钥，进行AI增强分析
    if (apiKey) {
      try {
        const aiAnalysis = await geminiService.analyzeProduct(apiKey, productData, options.analysisType || 'detailed');
        result.aiInsights = aiAnalysis;
      } catch (aiError) {
        logger.warn('AI分析失败，使用基础分析结果:', aiError.message);
      }
    }
    
    // 保存分析结果
    await db('analysis_results')
      .where({ id: analysisId })
      .update({
        status: 'completed',
        result: JSON.stringify(result),
        insights: JSON.stringify(result.aiInsights || {}),
        recommendations: JSON.stringify(result.recommendation || {}),
        confidence_score: result.recommendation?.confidence || 0,
        completed_at: new Date(),
        duration_ms: Date.now() - new Date(job.timestamp).getTime()
      });
    
    // 更新产品分析状态
    if (productData.id) {
      await db('products')
        .where({ id: productData.id })
        .update({
          is_analyzed: true,
          analysis_summary: JSON.stringify({
            score: result.overallScore,
            recommendation: result.recommendation.recommendation,
            lastAnalyzed: new Date().toISOString()
          }),
          analysis_score: result.overallScore
        });
    }
    
    return result;
  } catch (error) {
    // 更新任务错误状态
    await db('analysis_results')
      .where({ id: analysisId })
      .update({
        status: 'failed',
        error_message: error.message,
        error_details: JSON.stringify({
          stack: error.stack,
          timestamp: new Date().toISOString()
        }),
        completed_at: new Date()
      });
    
    throw error;
  }
});

const analysisController = {
  /**
   * 开始分析任务
   */
  async startAnalysis(req, res) {
    try {
      const { type, config, priority = 'normal' } = req.body;
      const userId = req.user.id;
      
      // 验证分析类型
      const validTypes = ['product', 'market', 'competitor', 'trend'];
      if (!validTypes.includes(type)) {
        throw createError.badRequest('无效的分析类型', 'INVALID_ANALYSIS_TYPE');
      }
      
      // 创建分析任务记录
      const [analysisId] = await db('analysis_results').insert({
        user_id: userId,
        title: this.generateAnalysisTitle(type, config),
        type,
        status: 'pending',
        priority,
        config: JSON.stringify(config),
        input_data: JSON.stringify(config.productData || config.inputData || {}),
        gemini_model: 'gemini-pro'
      }).returning('id');
      
      // 根据分析类型处理
      let jobData = {
        analysisId,
        userId,
        type,
        config,
        priority
      };
      
      if (type === 'product') {
        if (!config.productData) {
          throw createError.badRequest('产品分析需要提供产品数据', 'MISSING_PRODUCT_DATA');
        }
        
        // 获取用户的Gemini API密钥
        const user = await db('users').where({ id: userId }).first();
        if (user && user.gemini_api_key_encrypted) {
          try {
            // 这里应该解密API密钥，暂时跳过
            jobData.apiKey = null; // decryptApiKey(user.gemini_api_key_encrypted);
          } catch (error) {
            logger.warn('解密API密钥失败:', error.message);
          }
        }
        
        jobData.productData = config.productData;
        jobData.options = {
          analysisDepth: config.analysisDepth || 'detailed',
          includeMarketAnalysis: config.includeMarketAnalysis !== false,
          includeCompetitorAnalysis: config.includeCompetitorAnalysis !== false,
          includeTrendAnalysis: config.includeTrendAnalysis !== false
        };
        
        // 添加到队列
        await analysisQueue.add('product-analysis', jobData, {
          priority: this.getPriorityValue(priority),
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000
          }
        });
      }
      
      logger.logUserActivity(userId, 'start_analysis', { analysisId, type });
      
      res.json({
        success: true,
        message: '分析任务已启动',
        data: {
          analysisId,
          type,
          status: 'pending',
          priority
        }
      });
    } catch (error) {
      logger.error('启动分析任务失败:', error);
      throw error;
    }
  },

  /**
   * 获取分析任务列表
   */
  async getAnalysisList(req, res) {
    try {
      const userId = req.user.id;
      const { 
        page = 1, 
        limit = 20, 
        status, 
        type 
      } = req.query;
      
      const offset = (page - 1) * limit;
      
      let query = db('analysis_results')
        .where({ user_id: userId })
        .orderBy('created_at', 'desc');
      
      if (status) {
        query = query.where({ status });
      }
      
      if (type) {
        query = query.where({ type });
      }
      
      const [analyses, totalCount] = await Promise.all([
        query.clone().limit(limit).offset(offset),
        query.clone().count('* as count').first()
      ]);
      
      // 解析JSON字段
      const processedAnalyses = analyses.map(analysis => ({
        ...analysis,
        config: analysis.config ? JSON.parse(analysis.config) : null,
        result: analysis.result ? JSON.parse(analysis.result) : null,
        insights: analysis.insights ? JSON.parse(analysis.insights) : null,
        recommendations: analysis.recommendations ? JSON.parse(analysis.recommendations) : null
      }));
      
      res.json({
        success: true,
        message: '获取分析列表成功',
        data: {
          analyses: processedAnalyses,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: parseInt(totalCount.count),
            pages: Math.ceil(totalCount.count / limit)
          }
        }
      });
    } catch (error) {
      logger.error('获取分析列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取分析任务详情
   */
  async getAnalysisById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const analysis = await db('analysis_results')
        .where({ id, user_id: userId })
        .first();
      
      if (!analysis) {
        throw createError.notFound('分析任务不存在', 'ANALYSIS_NOT_FOUND');
      }
      
      // 解析JSON字段
      const processedAnalysis = {
        ...analysis,
        config: analysis.config ? JSON.parse(analysis.config) : null,
        result: analysis.result ? JSON.parse(analysis.result) : null,
        insights: analysis.insights ? JSON.parse(analysis.insights) : null,
        recommendations: analysis.recommendations ? JSON.parse(analysis.recommendations) : null,
        input_data: analysis.input_data ? JSON.parse(analysis.input_data) : null
      };
      
      // 更新查看次数和时间
      await db('analysis_results')
        .where({ id })
        .update({
          view_count: (analysis.view_count || 0) + 1,
          last_viewed_at: new Date()
        });
      
      res.json({
        success: true,
        message: '获取分析详情成功',
        data: processedAnalysis
      });
    } catch (error) {
      logger.error('获取分析详情失败:', error);
      throw error;
    }
  },

  /**
   * 获取分析任务状态
   */
  async getAnalysisStatus(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const analysis = await db('analysis_results')
        .where({ id, user_id: userId })
        .select('id', 'status', 'started_at', 'completed_at', 'error_message', 'duration_ms')
        .first();
      
      if (!analysis) {
        throw createError.notFound('分析任务不存在', 'ANALYSIS_NOT_FOUND');
      }
      
      // 计算进度
      let progress = 0;
      if (analysis.status === 'completed') progress = 100;
      else if (analysis.status === 'running') progress = 50;
      else if (analysis.status === 'failed') progress = 0;
      
      res.json({
        success: true,
        message: '获取分析状态成功',
        data: {
          ...analysis,
          progress
        }
      });
    } catch (error) {
      logger.error('获取分析状态失败:', error);
      throw error;
    }
  },

  /**
   * 获取分析结果
   */
  async getAnalysisResult(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const analysis = await db('analysis_results')
        .where({ id, user_id: userId })
        .first();
      
      if (!analysis) {
        throw createError.notFound('分析任务不存在', 'ANALYSIS_NOT_FOUND');
      }
      
      if (analysis.status !== 'completed') {
        throw createError.badRequest('分析任务尚未完成', 'ANALYSIS_NOT_COMPLETED');
      }
      
      const result = {
        analysisId: id,
        status: analysis.status,
        completedAt: analysis.completed_at,
        duration: analysis.duration_ms,
        result: analysis.result ? JSON.parse(analysis.result) : null,
        insights: analysis.insights ? JSON.parse(analysis.insights) : null,
        recommendations: analysis.recommendations ? JSON.parse(analysis.recommendations) : null,
        confidenceScore: analysis.confidence_score
      };
      
      res.json({
        success: true,
        message: '获取分析结果成功',
        data: result
      });
    } catch (error) {
      logger.error('获取分析结果失败:', error);
      throw error;
    }
  },

  /**
   * 生成分析标题
   */
  generateAnalysisTitle(type, config) {
    const titles = {
      product: `产品分析 - ${config.productData?.title || '未知产品'}`,
      market: `市场分析 - ${config.category || '未知类别'}`,
      competitor: `竞争分析 - ${config.competitorName || '竞争对手'}`,
      trend: `趋势分析 - ${config.productName || '产品趋势'}`
    };
    
    return titles[type] || '未知分析';
  },

  /**
   * 取消分析任务
   */
  async cancelAnalysis(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const analysis = await db('analysis_results')
        .where({ id, user_id: userId })
        .first();

      if (!analysis) {
        throw createError.notFound('分析任务不存在', 'ANALYSIS_NOT_FOUND');
      }

      if (analysis.status !== 'pending' && analysis.status !== 'running') {
        throw createError.badRequest('任务无法取消', 'ANALYSIS_NOT_CANCELLABLE');
      }

      // 更新任务状态
      await db('analysis_results')
        .where({ id })
        .update({
          status: 'cancelled',
          completed_at: new Date()
        });

      logger.logUserActivity(userId, 'cancel_analysis', { analysisId: id });

      res.json({
        success: true,
        message: '分析任务已取消'
      });
    } catch (error) {
      logger.error('取消分析任务失败:', error);
      throw error;
    }
  },

  /**
   * 重试失败的分析任务
   */
  async retryAnalysis(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const analysis = await db('analysis_results')
        .where({ id, user_id: userId })
        .first();

      if (!analysis) {
        throw createError.notFound('分析任务不存在', 'ANALYSIS_NOT_FOUND');
      }

      if (analysis.status !== 'failed') {
        throw createError.badRequest('只能重试失败的任务', 'ANALYSIS_NOT_RETRYABLE');
      }

      // 重置任务状态
      await db('analysis_results')
        .where({ id })
        .update({
          status: 'pending',
          retry_count: (analysis.retry_count || 0) + 1,
          error_message: null,
          error_details: null,
          started_at: null,
          completed_at: null
        });

      // 重新添加到队列
      if (analysis.type === 'product') {
        const config = JSON.parse(analysis.config || '{}');
        const inputData = JSON.parse(analysis.input_data || '{}');

        await analysisQueue.add('product-analysis', {
          analysisId: id,
          userId,
          type: analysis.type,
          config,
          productData: inputData,
          options: {
            analysisDepth: config.analysisDepth || 'detailed',
            includeMarketAnalysis: config.includeMarketAnalysis !== false,
            includeCompetitorAnalysis: config.includeCompetitorAnalysis !== false,
            includeTrendAnalysis: config.includeTrendAnalysis !== false
          }
        });
      }

      logger.logUserActivity(userId, 'retry_analysis', { analysisId: id, retryCount: analysis.retry_count + 1 });

      res.json({
        success: true,
        message: '分析任务已重新启动',
        data: {
          analysisId: id,
          retryCount: analysis.retry_count + 1
        }
      });
    } catch (error) {
      logger.error('重试分析任务失败:', error);
      throw error;
    }
  },

  /**
   * 删除分析任务
   */
  async deleteAnalysis(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const analysis = await db('analysis_results')
        .where({ id, user_id: userId })
        .first();

      if (!analysis) {
        throw createError.notFound('分析任务不存在', 'ANALYSIS_NOT_FOUND');
      }

      // 删除任务
      await db('analysis_results').where({ id }).del();

      logger.logUserActivity(userId, 'delete_analysis', { analysisId: id });

      res.json({
        success: true,
        message: '分析任务已删除'
      });
    } catch (error) {
      logger.error('删除分析任务失败:', error);
      throw error;
    }
  },

  /**
   * 导出分析结果
   */
  async exportAnalysis(req, res) {
    try {
      const { id } = req.params;
      const { format = 'json' } = req.query;
      const userId = req.user.id;

      const analysis = await db('analysis_results')
        .where({ id, user_id: userId })
        .first();

      if (!analysis) {
        throw createError.notFound('分析任务不存在', 'ANALYSIS_NOT_FOUND');
      }

      if (analysis.status !== 'completed') {
        throw createError.badRequest('分析任务尚未完成', 'ANALYSIS_NOT_COMPLETED');
      }

      // 更新导出次数
      await db('analysis_results')
        .where({ id })
        .update({
          export_count: (analysis.export_count || 0) + 1
        });

      const exportData = {
        analysisId: id,
        title: analysis.title,
        type: analysis.type,
        status: analysis.status,
        createdAt: analysis.created_at,
        completedAt: analysis.completed_at,
        duration: analysis.duration_ms,
        result: analysis.result ? JSON.parse(analysis.result) : null,
        insights: analysis.insights ? JSON.parse(analysis.insights) : null,
        recommendations: analysis.recommendations ? JSON.parse(analysis.recommendations) : null,
        confidenceScore: analysis.confidence_score,
        exportedAt: new Date().toISOString()
      };

      // 根据格式返回数据
      if (format === 'json') {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="analysis_${id}.json"`);
        res.json(exportData);
      } else {
        throw createError.badRequest('不支持的导出格式', 'UNSUPPORTED_FORMAT');
      }

      logger.logUserActivity(userId, 'export_analysis', { analysisId: id, format });
    } catch (error) {
      logger.error('导出分析结果失败:', error);
      throw error;
    }
  },

  /**
   * 获取分析统计信息
   */
  async getAnalysisStatistics(req, res) {
    try {
      const userId = req.user.id;
      const { period = '30d' } = req.query;

      const periodDays = parseInt(period.replace('d', ''));
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - periodDays);

      const stats = await db('analysis_results')
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .select(
          db.raw('COUNT(*) as total_analyses'),
          db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as completed_analyses'),
          db.raw('COUNT(CASE WHEN status = \'failed\' THEN 1 END) as failed_analyses'),
          db.raw('COUNT(CASE WHEN status = \'running\' THEN 1 END) as running_analyses'),
          db.raw('AVG(duration_ms) as avg_duration'),
          db.raw('AVG(confidence_score) as avg_confidence')
        )
        .first();

      const typeStats = await db('analysis_results')
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .groupBy('type')
        .select('type', db.raw('COUNT(*) as count'))
        .orderBy('count', 'desc');

      const dailyStats = await db('analysis_results')
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .select(
          db.raw('DATE(created_at) as date'),
          db.raw('COUNT(*) as analyses'),
          db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as completed')
        )
        .groupBy(db.raw('DATE(created_at)'))
        .orderBy('date', 'asc');

      const result = {
        summary: {
          totalAnalyses: parseInt(stats.total_analyses) || 0,
          completedAnalyses: parseInt(stats.completed_analyses) || 0,
          failedAnalyses: parseInt(stats.failed_analyses) || 0,
          runningAnalyses: parseInt(stats.running_analyses) || 0,
          averageDuration: Math.round(parseFloat(stats.avg_duration) || 0),
          averageConfidence: Math.round(parseFloat(stats.avg_confidence) || 0),
          successRate: stats.total_analyses > 0
            ? Math.round((stats.completed_analyses / stats.total_analyses) * 100)
            : 0
        },
        typeStats,
        dailyStats,
        period
      };

      res.json({
        success: true,
        message: '获取统计信息成功',
        data: result
      });
    } catch (error) {
      logger.error('获取分析统计失败:', error);
      throw error;
    }
  },

  /**
   * 获取优先级数值
   */
  getPriorityValue(priority) {
    const priorities = {
      low: 1,
      normal: 5,
      high: 10
    };

    return priorities[priority] || 5;
  }
};

module.exports = analysisController;
