const redis = require('redis');
const logger = require('../utils/logger');

// Redis配置
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

// 创建Redis客户端
const client = redis.createClient({
  socket: {
    host: redisConfig.host,
    port: redisConfig.port,
    connectTimeout: redisConfig.connectTimeout,
    commandTimeout: redisConfig.commandTimeout,
  },
  password: redisConfig.password,
  database: redisConfig.db,
});

// 创建发布者客户端（用于发布消息）
const publisher = redis.createClient({
  socket: {
    host: redisConfig.host,
    port: redisConfig.port,
  },
  password: redisConfig.password,
  database: redisConfig.db,
});

// 创建订阅者客户端（用于订阅消息）
const subscriber = redis.createClient({
  socket: {
    host: redisConfig.host,
    port: redisConfig.port,
  },
  password: redisConfig.password,
  database: redisConfig.db,
});

// 错误处理
client.on('error', (err) => {
  logger.error('Redis客户端错误:', err);
});

client.on('connect', () => {
  logger.info('Redis客户端连接成功');
});

client.on('ready', () => {
  logger.info('Redis客户端就绪');
});

client.on('end', () => {
  logger.info('Redis客户端连接结束');
});

publisher.on('error', (err) => {
  logger.error('Redis发布者错误:', err);
});

subscriber.on('error', (err) => {
  logger.error('Redis订阅者错误:', err);
});

/**
 * 连接Redis
 */
async function connectRedis() {
  try {
    await client.connect();
    await publisher.connect();
    await subscriber.connect();
    
    // 测试连接
    await client.ping();
    logger.info('Redis连接测试成功');
    
    return { client, publisher, subscriber };
  } catch (error) {
    logger.error('Redis连接失败:', error);
    throw error;
  }
}

/**
 * 关闭Redis连接
 */
async function closeRedis() {
  try {
    await client.quit();
    await publisher.quit();
    await subscriber.quit();
    logger.info('Redis连接已关闭');
  } catch (error) {
    logger.error('关闭Redis连接时出错:', error);
    throw error;
  }
}

/**
 * 检查Redis连接状态
 */
async function checkRedisHealth() {
  try {
    const pong = await client.ping();
    return { 
      status: 'healthy', 
      message: 'Redis连接正常',
      response: pong 
    };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      message: error.message 
    };
  }
}

/**
 * 缓存操作封装
 */
const cache = {
  /**
   * 设置缓存
   */
  async set(key, value, ttl = 3600) {
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl > 0) {
        await client.setEx(key, ttl, serializedValue);
      } else {
        await client.set(key, serializedValue);
      }
      return true;
    } catch (error) {
      logger.error(`设置缓存失败 ${key}:`, error);
      return false;
    }
  },

  /**
   * 获取缓存
   */
  async get(key) {
    try {
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error(`获取缓存失败 ${key}:`, error);
      return null;
    }
  },

  /**
   * 删除缓存
   */
  async del(key) {
    try {
      const result = await client.del(key);
      return result > 0;
    } catch (error) {
      logger.error(`删除缓存失败 ${key}:`, error);
      return false;
    }
  },

  /**
   * 检查缓存是否存在
   */
  async exists(key) {
    try {
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error(`检查缓存存在性失败 ${key}:`, error);
      return false;
    }
  },

  /**
   * 设置缓存过期时间
   */
  async expire(key, ttl) {
    try {
      const result = await client.expire(key, ttl);
      return result === 1;
    } catch (error) {
      logger.error(`设置缓存过期时间失败 ${key}:`, error);
      return false;
    }
  },

  /**
   * 获取缓存剩余过期时间
   */
  async ttl(key) {
    try {
      return await client.ttl(key);
    } catch (error) {
      logger.error(`获取缓存TTL失败 ${key}:`, error);
      return -1;
    }
  }
};

/**
 * 发布订阅操作
 */
const pubsub = {
  /**
   * 发布消息
   */
  async publish(channel, message) {
    try {
      const serializedMessage = JSON.stringify(message);
      const result = await publisher.publish(channel, serializedMessage);
      return result;
    } catch (error) {
      logger.error(`发布消息失败 ${channel}:`, error);
      return 0;
    }
  },

  /**
   * 订阅频道
   */
  async subscribe(channel, callback) {
    try {
      await subscriber.subscribe(channel, (message) => {
        try {
          const parsedMessage = JSON.parse(message);
          callback(parsedMessage);
        } catch (error) {
          logger.error(`解析订阅消息失败 ${channel}:`, error);
          callback(message);
        }
      });
      logger.info(`订阅频道成功: ${channel}`);
    } catch (error) {
      logger.error(`订阅频道失败 ${channel}:`, error);
    }
  },

  /**
   * 取消订阅
   */
  async unsubscribe(channel) {
    try {
      await subscriber.unsubscribe(channel);
      logger.info(`取消订阅频道: ${channel}`);
    } catch (error) {
      logger.error(`取消订阅频道失败 ${channel}:`, error);
    }
  }
};

module.exports = {
  client,
  publisher,
  subscriber,
  connectRedis,
  closeRedis,
  checkRedisHealth,
  cache,
  pubsub
};
