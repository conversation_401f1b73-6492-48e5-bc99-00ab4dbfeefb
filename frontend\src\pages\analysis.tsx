import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import {
  ChartBarIcon,
  SparklesIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon,
  DocumentArrowDownIcon,
} from '@heroicons/react/24/outline';

const AnalysisPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const [selectedCategory, setSelectedCategory] = useState('all');

  if (isLoading) {
    return <LoadingScreen message="加载智能分析..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 模拟分析报告数据
  const reports = [
    {
      id: 1,
      title: 'iPhone 15 Pro Max 市场分析报告',
      category: '手机',
      status: '已完成',
      trend: 'up',
      score: 85,
      createdAt: '2024-01-15',
      summary: '该产品在高端手机市场表现优异，建议加大库存投入',
      metrics: {
        marketShare: '15.2%',
        profitMargin: '32%',
        competitorCount: 8
      }
    },
    {
      id: 2,
      title: '小米14 Ultra 竞品分析',
      category: '手机',
      status: '已完成',
      trend: 'up',
      score: 78,
      createdAt: '2024-01-12',
      summary: '性价比优势明显，在中高端市场有较强竞争力',
      metrics: {
        marketShare: '12.8%',
        profitMargin: '28%',
        competitorCount: 12
      }
    },
    {
      id: 3,
      title: 'MacBook Pro M3 销售预测',
      category: '笔记本',
      status: '进行中',
      trend: 'up',
      score: 92,
      createdAt: '2024-01-10',
      summary: '预计Q2销量将增长25%，建议提前备货',
      metrics: {
        marketShare: '8.5%',
        profitMargin: '35%',
        competitorCount: 6
      }
    },
    {
      id: 4,
      title: 'AirPods Pro 2 价格趋势分析',
      category: '耳机',
      status: '已完成',
      trend: 'down',
      score: 65,
      createdAt: '2024-01-08',
      summary: '价格竞争激烈，建议调整定价策略',
      metrics: {
        marketShare: '22.1%',
        profitMargin: '18%',
        competitorCount: 15
      }
    }
  ];

  const categories = ['all', '手机', '笔记本', '耳机'];
  const filteredReports = selectedCategory === 'all' 
    ? reports 
    : reports.filter(report => report.category === selectedCategory);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.5rem'
                }}>
                  智能分析
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  AI驱动的产品市场分析和预测
                </p>
              </div>
              <button className="btn btn-primary">
                <SparklesIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                新建分析
              </button>
            </div>
          </div>
        </div>

        {/* 统计概览 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <ChartBarIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    {reports.length}
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    分析报告
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-success-500), var(--color-success-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <TrendingUpIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    82%
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    平均准确率
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <SparklesIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    15
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    AI模型
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 分类筛选 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              {categories.map((category) => (
                <button
                  key={category}
                  className={`btn ${selectedCategory === category ? 'btn-primary' : 'btn-ghost'} btn-sm`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category === 'all' ? '全部' : category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 分析报告列表 */}
        <div className="card">
          <div className="card-body">
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {filteredReports.map((report) => (
                <div key={report.id} className="card">
                  <div className="card-body">
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      marginBottom: '1rem'
                    }}>
                      <div style={{ flex: 1 }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '0.5rem' }}>
                          <h3 style={{
                            fontSize: '1.25rem',
                            fontWeight: '600',
                            color: 'var(--color-gray-900)'
                          }}>
                            {report.title}
                          </h3>
                          <span className={`badge ${report.status === '已完成' ? 'badge-success' : 'badge-warning'}`}>
                            {report.status}
                          </span>
                          {report.trend === 'up' ? (
                            <TrendingUpIcon style={{ width: '1.25rem', height: '1.25rem', color: 'var(--color-success-500)' }} />
                          ) : (
                            <TrendingDownIcon style={{ width: '1.25rem', height: '1.25rem', color: 'var(--color-error-500)' }} />
                          )}
                        </div>
                        
                        <p style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '1rem'
                        }}>
                          {report.summary}
                        </p>
                        
                        <div style={{
                          display: 'grid',
                          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                          gap: '1rem',
                          marginBottom: '1rem'
                        }}>
                          <div>
                            <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>市场份额</span>
                            <p style={{ fontSize: '1rem', fontWeight: '600', color: 'var(--color-gray-900)' }}>
                              {report.metrics.marketShare}
                            </p>
                          </div>
                          <div>
                            <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>利润率</span>
                            <p style={{ fontSize: '1rem', fontWeight: '600', color: 'var(--color-gray-900)' }}>
                              {report.metrics.profitMargin}
                            </p>
                          </div>
                          <div>
                            <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>竞品数量</span>
                            <p style={{ fontSize: '1rem', fontWeight: '600', color: 'var(--color-gray-900)' }}>
                              {report.metrics.competitorCount}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem'
                      }}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{
                            fontSize: '1.5rem',
                            fontWeight: '700',
                            color: report.score >= 80 ? 'var(--color-success-600)' : 
                                   report.score >= 60 ? 'var(--color-warning-600)' : 'var(--color-error-600)'
                          }}>
                            {report.score}
                          </div>
                          <div style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>
                            分析评分
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span style={{ fontSize: '0.875rem', color: 'var(--color-gray-500)' }}>
                        创建时间: {report.createdAt}
                      </span>
                      
                      <div style={{ display: 'flex', gap: '0.5rem' }}>
                        <button className="btn btn-ghost btn-sm">
                          <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                          查看详情
                        </button>
                        <button className="btn btn-ghost btn-sm">
                          <DocumentArrowDownIcon style={{ width: '1rem', height: '1rem' }} />
                          导出报告
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredReports.length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: 'var(--color-gray-500)'
              }}>
                <p>暂无分析报告</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalysisPage;
