/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('system_logs', function(table) {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 关联信息
    table.uuid('user_id').nullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('SET NULL');
    
    // 日志基本信息
    table.enum('level', ['debug', 'info', 'warn', 'error', 'fatal']).notNullable();
    table.string('category', 50).notNullable(); // 日志分类：auth, crawler, analysis, api等
    table.string('action', 100).notNullable(); // 具体操作
    table.text('message').notNullable();
    
    // 详细信息
    table.json('details').nullable(); // 详细数据
    table.json('context').nullable(); // 上下文信息
    table.json('metadata').nullable(); // 元数据
    
    // 请求信息
    table.string('request_id', 100).nullable(); // 请求ID，用于追踪
    table.string('session_id', 100).nullable(); // 会话ID
    table.string('ip_address', 45).nullable();
    table.string('user_agent', 500).nullable();
    table.string('method', 10).nullable(); // HTTP方法
    table.string('url', 1000).nullable(); // 请求URL
    
    // 错误信息
    table.string('error_code', 50).nullable();
    table.text('error_message').nullable();
    table.text('stack_trace').nullable();
    
    // 性能信息
    table.integer('duration_ms').nullable(); // 执行时间
    table.integer('memory_usage').nullable(); // 内存使用量
    table.integer('cpu_usage').nullable(); // CPU使用率
    
    // 业务信息
    table.string('resource_type', 50).nullable(); // 资源类型
    table.string('resource_id', 100).nullable(); // 资源ID
    table.enum('operation_type', ['create', 'read', 'update', 'delete', 'execute']).nullable();
    
    // 时间信息
    table.timestamp('timestamp').notNullable().defaultTo(knex.fn.now());
    table.date('log_date').notNullable().defaultTo(knex.fn.now());
    table.integer('log_hour').nullable(); // 0-23
    
    // 索引
    table.index(['level']);
    table.index(['category']);
    table.index(['action']);
    table.index(['user_id']);
    table.index(['timestamp']);
    table.index(['log_date']);
    table.index(['request_id']);
    table.index(['session_id']);
    table.index(['ip_address']);
    table.index(['error_code']);
    table.index(['resource_type']);
    table.index(['resource_id']);
    
    // 复合索引
    table.index(['level', 'category']);
    table.index(['category', 'action']);
    table.index(['user_id', 'timestamp']);
    table.index(['log_date', 'log_hour']);
    table.index(['resource_type', 'resource_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('system_logs');
};
