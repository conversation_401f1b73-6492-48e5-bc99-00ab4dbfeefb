/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('import_tasks', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('title', 255).notNullable();
    table.text('description');
    table.string('file_name', 255).notNullable();
    table.string('file_path', 500).notNullable();
    table.integer('file_size').notNullable();
    table.enum('file_type', ['csv', 'xlsx', 'xls']).notNullable();
    table.enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled']).notNullable().defaultTo('pending');
    table.timestamp('started_at');
    table.timestamp('completed_at');
    table.integer('duration_ms');
    table.integer('total_rows').defaultTo(0);
    table.integer('processed_rows').defaultTo(0);
    table.integer('success_rows').defaultTo(0);
    table.integer('failed_rows').defaultTo(0);
    table.text('error_message');
    table.jsonb('error_details');
    table.jsonb('validation_errors').defaultTo('[]');
    table.jsonb('import_summary').defaultTo('{}');
    table.jsonb('config').defaultTo('{}');
    table.timestamps(true, true);

    // 索引
    table.index('user_id');
    table.index('status');
    table.index('created_at');
    table.index('file_type');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('import_tasks');
};
