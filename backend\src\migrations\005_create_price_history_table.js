/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('price_history', function(table) {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 关联产品
    table.uuid('product_id').notNullable();
    table.foreign('product_id').references('id').inTable('products').onDelete('CASCADE');
    
    // 价格信息
    table.decimal('price', 10, 2).notNullable();
    table.decimal('original_price', 10, 2).nullable();
    table.decimal('discount_amount', 10, 2).nullable();
    table.decimal('discount_percentage', 5, 2).nullable();
    table.string('currency', 3).defaultTo('CNY');
    
    // 促销信息
    table.boolean('is_promotion').defaultTo(false);
    table.string('promotion_type', 50).nullable();
    table.text('promotion_description').nullable();
    table.timestamp('promotion_start_at').nullable();
    table.timestamp('promotion_end_at').nullable();
    
    // 库存信息
    table.integer('stock_quantity').nullable();
    table.boolean('is_in_stock').defaultTo(true);
    
    // 销售信息
    table.integer('sales_count').nullable();
    table.decimal('rating', 3, 2).nullable();
    table.integer('review_count').nullable();
    
    // 数据来源
    table.enum('source', ['crawler', 'api', 'manual']).defaultTo('crawler');
    table.uuid('crawler_task_id').nullable();
    table.foreign('crawler_task_id').references('id').inTable('crawler_tasks').onDelete('SET NULL');
    
    // 记录时间
    table.timestamp('recorded_at').notNullable().defaultTo(knex.fn.now());
    table.date('record_date').notNullable();
    table.integer('record_hour').nullable(); // 0-23
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['product_id']);
    table.index(['recorded_at']);
    table.index(['record_date']);
    table.index(['price']);
    table.index(['is_promotion']);
    table.index(['is_in_stock']);
    table.index(['source']);
    table.index(['crawler_task_id']);
    
    // 复合索引
    table.index(['product_id', 'recorded_at']);
    table.index(['product_id', 'record_date']);
    table.index(['record_date', 'record_hour']);
    
    // 唯一约束（防止同一产品同一时间重复记录）
    table.unique(['product_id', 'recorded_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('price_history');
};
