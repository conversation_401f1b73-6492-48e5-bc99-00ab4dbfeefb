const crawlerService = require('../services/crawlerService');
const { createError } = require('../middleware/errorHandler');
const { db } = require('../config/database');
const logger = require('../utils/logger');
const Bull = require('bull');
const { cache } = require('../config/redis');

// 创建爬虫任务队列
const crawlerQueue = new Bull('crawler tasks', {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  }
});

// 处理爬虫任务
crawlerQueue.process('crawl-product', async (job) => {
  const { taskId, url, platform, options } = job.data;
  
  try {
    // 更新任务状态
    await db('crawler_tasks')
      .where({ id: taskId })
      .update({
        status: 'running',
        started_at: new Date()
      });
    
    // 执行爬取
    const result = await crawlerService.crawlProduct(url, platform, options);
    
    // 更新任务结果
    await db('crawler_tasks')
      .where({ id: taskId })
      .update({
        status: 'completed',
        completed_at: new Date(),
        crawled_data: JSON.stringify(result),
        success_items: 1,
        total_items: 1
      });
    
    return result;
  } catch (error) {
    // 更新任务错误状态
    await db('crawler_tasks')
      .where({ id: taskId })
      .update({
        status: 'failed',
        completed_at: new Date(),
        error_message: error.message,
        failed_items: 1,
        total_items: 1
      });
    
    throw error;
  }
});

crawlerQueue.process('crawl-search', async (job) => {
  const { taskId, keyword, platform, options } = job.data;
  
  try {
    await db('crawler_tasks')
      .where({ id: taskId })
      .update({
        status: 'running',
        started_at: new Date()
      });
    
    const results = await crawlerService.crawlSearch(keyword, platform, options);
    
    await db('crawler_tasks')
      .where({ id: taskId })
      .update({
        status: 'completed',
        completed_at: new Date(),
        crawled_data: JSON.stringify(results),
        success_items: results.length,
        total_items: results.length
      });
    
    return results;
  } catch (error) {
    await db('crawler_tasks')
      .where({ id: taskId })
      .update({
        status: 'failed',
        completed_at: new Date(),
        error_message: error.message,
        failed_items: 1,
        total_items: 1
      });
    
    throw error;
  }
});

const crawlerController = {
  /**
   * 爬取单个产品
   */
  async crawlProduct(req, res) {
    try {
      const { url, platform, options = {} } = req.body;
      const userId = req.user.id;
      
      // 创建爬虫任务记录
      const [taskId] = await db('crawler_tasks').insert({
        user_id: userId,
        title: `产品爬取 - ${platform}`,
        type: 'product',
        platform,
        target_url: url,
        config: JSON.stringify(options),
        status: 'pending'
      }).returning('id');
      
      // 添加到队列
      const job = await crawlerQueue.add('crawl-product', {
        taskId,
        url,
        platform,
        options
      }, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      });
      
      logger.logUserActivity(userId, 'crawl_product_start', { taskId, platform, url });
      
      res.json({
        success: true,
        message: '爬取任务已启动',
        data: {
          taskId,
          jobId: job.id,
          status: 'pending'
        }
      });
    } catch (error) {
      logger.error('启动产品爬取失败:', error);
      throw error;
    }
  },

  /**
   * 爬取搜索结果
   */
  async crawlSearch(req, res) {
    try {
      const { keyword, platform, maxPages = 3, filters = {} } = req.body;
      const userId = req.user.id;
      
      const [taskId] = await db('crawler_tasks').insert({
        user_id: userId,
        title: `搜索爬取 - ${keyword}`,
        type: 'search',
        platform,
        keyword,
        config: JSON.stringify({ maxPages, filters }),
        status: 'pending'
      }).returning('id');
      
      const job = await crawlerQueue.add('crawl-search', {
        taskId,
        keyword,
        platform,
        options: { maxPages, ...filters }
      }, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      });
      
      logger.logUserActivity(userId, 'crawl_search_start', { taskId, platform, keyword });
      
      res.json({
        success: true,
        message: '搜索爬取任务已启动',
        data: {
          taskId,
          jobId: job.id,
          status: 'pending'
        }
      });
    } catch (error) {
      logger.error('启动搜索爬取失败:', error);
      throw error;
    }
  },

  /**
   * 爬取类别商品
   */
  async crawlCategory(req, res) {
    try {
      const { category, platform, maxProducts = 100 } = req.body;
      const userId = req.user.id;
      
      // 根据类别生成搜索关键词
      const categoryKeywords = {
        '手机数码': ['手机', '数码', '电子产品'],
        '服装鞋包': ['服装', '鞋子', '包包'],
        '家用电器': ['家电', '电器', '厨具'],
        '美妆护肤': ['化妆品', '护肤品', '美妆'],
        '食品饮料': ['食品', '零食', '饮料']
      };
      
      const keywords = categoryKeywords[category] || [category];
      const keyword = keywords[0]; // 使用第一个关键词
      
      const [taskId] = await db('crawler_tasks').insert({
        user_id: userId,
        title: `类别爬取 - ${category}`,
        type: 'category',
        platform,
        category,
        keyword,
        config: JSON.stringify({ maxProducts, keywords }),
        status: 'pending'
      }).returning('id');
      
      const job = await crawlerQueue.add('crawl-search', {
        taskId,
        keyword,
        platform,
        options: { maxItems: maxProducts }
      });
      
      logger.logUserActivity(userId, 'crawl_category_start', { taskId, platform, category });
      
      res.json({
        success: true,
        message: '类别爬取任务已启动',
        data: {
          taskId,
          jobId: job.id,
          status: 'pending'
        }
      });
    } catch (error) {
      logger.error('启动类别爬取失败:', error);
      throw error;
    }
  },

  /**
   * 获取爬虫任务列表
   */
  async getCrawlerTasks(req, res) {
    try {
      const userId = req.user.id;
      const { 
        page = 1, 
        limit = 20, 
        status, 
        platform, 
        type 
      } = req.query;
      
      const offset = (page - 1) * limit;
      
      let query = db('crawler_tasks')
        .where({ user_id: userId })
        .orderBy('created_at', 'desc');
      
      if (status) {
        query = query.where({ status });
      }
      
      if (platform) {
        query = query.where({ platform });
      }
      
      if (type) {
        query = query.where({ type });
      }
      
      const [tasks, totalCount] = await Promise.all([
        query.clone().limit(limit).offset(offset),
        query.clone().count('* as count').first()
      ]);
      
      res.json({
        success: true,
        message: '获取任务列表成功',
        data: {
          tasks,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: parseInt(totalCount.count),
            pages: Math.ceil(totalCount.count / limit)
          }
        }
      });
    } catch (error) {
      logger.error('获取爬虫任务列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取爬虫任务详情
   */
  async getCrawlerTaskById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const task = await db('crawler_tasks')
        .where({ id, user_id: userId })
        .first();
      
      if (!task) {
        throw createError.notFound('爬虫任务不存在', 'TASK_NOT_FOUND');
      }
      
      // 解析JSON字段
      if (task.config) {
        task.config = JSON.parse(task.config);
      }
      if (task.crawled_data) {
        task.crawled_data = JSON.parse(task.crawled_data);
      }
      if (task.error_details) {
        task.error_details = JSON.parse(task.error_details);
      }
      
      res.json({
        success: true,
        message: '获取任务详情成功',
        data: task
      });
    } catch (error) {
      logger.error('获取爬虫任务详情失败:', error);
      throw error;
    }
  },

  /**
   * 取消爬虫任务
   */
  async cancelCrawlerTask(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const task = await db('crawler_tasks')
        .where({ id, user_id: userId })
        .first();

      if (!task) {
        throw createError.notFound('爬虫任务不存在', 'TASK_NOT_FOUND');
      }

      if (task.status !== 'pending' && task.status !== 'running') {
        throw createError.badRequest('任务无法取消', 'TASK_NOT_CANCELLABLE');
      }

      // 更新任务状态
      await db('crawler_tasks')
        .where({ id })
        .update({
          status: 'cancelled',
          completed_at: new Date()
        });

      logger.logUserActivity(userId, 'cancel_crawler_task', { taskId: id });

      res.json({
        success: true,
        message: '任务已取消'
      });
    } catch (error) {
      logger.error('取消爬虫任务失败:', error);
      throw error;
    }
  },

  /**
   * 重试失败的爬虫任务
   */
  async retryCrawlerTask(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const task = await db('crawler_tasks')
        .where({ id, user_id: userId })
        .first();

      if (!task) {
        throw createError.notFound('爬虫任务不存在', 'TASK_NOT_FOUND');
      }

      if (task.status !== 'failed') {
        throw createError.badRequest('只能重试失败的任务', 'TASK_NOT_RETRYABLE');
      }

      // 重置任务状态
      await db('crawler_tasks')
        .where({ id })
        .update({
          status: 'pending',
          retry_count: (task.retry_count || 0) + 1,
          error_message: null,
          error_details: null,
          started_at: null,
          completed_at: null
        });

      // 重新添加到队列
      const config = JSON.parse(task.config || '{}');
      let job;

      if (task.type === 'product') {
        job = await crawlerQueue.add('crawl-product', {
          taskId: id,
          url: task.target_url,
          platform: task.platform,
          options: config
        });
      } else if (task.type === 'search' || task.type === 'category') {
        job = await crawlerQueue.add('crawl-search', {
          taskId: id,
          keyword: task.keyword,
          platform: task.platform,
          options: config
        });
      }

      logger.logUserActivity(userId, 'retry_crawler_task', { taskId: id, retryCount: task.retry_count + 1 });

      res.json({
        success: true,
        message: '任务已重新启动',
        data: {
          taskId: id,
          jobId: job?.id,
          retryCount: task.retry_count + 1
        }
      });
    } catch (error) {
      logger.error('重试爬虫任务失败:', error);
      throw error;
    }
  },

  /**
   * 删除爬虫任务
   */
  async deleteCrawlerTask(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const task = await db('crawler_tasks')
        .where({ id, user_id: userId })
        .first();

      if (!task) {
        throw createError.notFound('爬虫任务不存在', 'TASK_NOT_FOUND');
      }

      // 删除任务
      await db('crawler_tasks').where({ id }).del();

      logger.logUserActivity(userId, 'delete_crawler_task', { taskId: id });

      res.json({
        success: true,
        message: '任务已删除'
      });
    } catch (error) {
      logger.error('删除爬虫任务失败:', error);
      throw error;
    }
  },

  /**
   * 获取代理状态
   */
  async getProxyStatus(req, res) {
    try {
      // 这里可以实现代理池状态检查
      const proxyStatus = {
        totalProxies: 0,
        activeProxies: 0,
        failedProxies: 0,
        lastChecked: new Date().toISOString(),
        proxies: []
      };

      res.json({
        success: true,
        message: '获取代理状态成功',
        data: proxyStatus
      });
    } catch (error) {
      logger.error('获取代理状态失败:', error);
      throw error;
    }
  },

  /**
   * 测试代理连接
   */
  async testProxy(req, res) {
    try {
      const { proxy } = req.body;

      // 这里可以实现代理测试逻辑
      const testResult = {
        proxy,
        status: 'active',
        responseTime: Math.floor(Math.random() * 1000) + 100,
        location: 'Unknown',
        testedAt: new Date().toISOString()
      };

      res.json({
        success: true,
        message: '代理测试完成',
        data: testResult
      });
    } catch (error) {
      logger.error('测试代理失败:', error);
      throw error;
    }
  },

  /**
   * 获取爬虫统计信息
   */
  async getCrawlerStatistics(req, res) {
    try {
      const userId = req.user.id;
      const { period = '7d' } = req.query;

      const periodDays = parseInt(period.replace('d', ''));
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - periodDays);

      const stats = await db('crawler_tasks')
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .select(
          db.raw('COUNT(*) as total_tasks'),
          db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as completed_tasks'),
          db.raw('COUNT(CASE WHEN status = \'failed\' THEN 1 END) as failed_tasks'),
          db.raw('COUNT(CASE WHEN status = \'running\' THEN 1 END) as running_tasks'),
          db.raw('SUM(success_items) as total_items'),
          db.raw('AVG(duration_ms) as avg_duration')
        )
        .first();

      const platformStats = await db('crawler_tasks')
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .groupBy('platform')
        .select('platform', db.raw('COUNT(*) as count'))
        .orderBy('count', 'desc');

      const dailyStats = await db('crawler_tasks')
        .where({ user_id: userId })
        .where('created_at', '>=', startDate)
        .select(
          db.raw('DATE(created_at) as date'),
          db.raw('COUNT(*) as tasks'),
          db.raw('SUM(success_items) as items')
        )
        .groupBy(db.raw('DATE(created_at)'))
        .orderBy('date', 'asc');

      const result = {
        summary: {
          totalTasks: parseInt(stats.total_tasks) || 0,
          completedTasks: parseInt(stats.completed_tasks) || 0,
          failedTasks: parseInt(stats.failed_tasks) || 0,
          runningTasks: parseInt(stats.running_tasks) || 0,
          totalItems: parseInt(stats.total_items) || 0,
          averageDuration: Math.round(parseFloat(stats.avg_duration) || 0),
          successRate: stats.total_tasks > 0
            ? Math.round((stats.completed_tasks / stats.total_tasks) * 100)
            : 0
        },
        platformStats,
        dailyStats,
        period
      };

      res.json({
        success: true,
        message: '获取统计信息成功',
        data: result
      });
    } catch (error) {
      logger.error('获取爬虫统计失败:', error);
      throw error;
    }
  },

  /**
   * 获取爬虫服务健康状态
   */
  async getCrawlerHealth(req, res) {
    try {
      const health = await crawlerService.getHealthStatus();

      // 获取队列状态
      const queueStats = await crawlerQueue.getJobCounts();

      const result = {
        ...health,
        queue: {
          waiting: queueStats.waiting || 0,
          active: queueStats.active || 0,
          completed: queueStats.completed || 0,
          failed: queueStats.failed || 0
        }
      };

      res.json({
        success: true,
        message: '获取健康状态成功',
        data: result
      });
    } catch (error) {
      logger.error('获取爬虫健康状态失败:', error);
      throw error;
    }
  }
};

module.exports = crawlerController;
