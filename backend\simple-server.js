const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const { prisma } = require('./src/config/database');

const app = express();
const PORT = process.env.PORT || 8000;

// 中间件
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 健康检查
app.get('/api/v1/health', async (req, res) => {
  try {
    // 测试数据库连接
    await prisma.$queryRaw`SELECT 1`;
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: 'connected',
      version: '1.0.0'
    });
  } catch (error) {
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    });
  }
});

// 基础路由
app.get('/', (req, res) => {
  res.json({
    message: 'SmartPick API Server',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// 用户路由
app.get('/api/v1/users', async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        createdAt: true
      }
    });
    
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 产品路由
app.get('/api/v1/products', async (req, res) => {
  try {
    const products = await prisma.product.findMany({
      include: {
        user: {
          select: {
            id: true,
            username: true
          }
        }
      },
      take: 10
    });
    
    res.json({
      success: true,
      data: products
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 导入任务路由
app.get('/api/v1/import/tasks', async (req, res) => {
  try {
    const tasks = await prisma.importTask.findMany({
      include: {
        user: {
          select: {
            id: true,
            username: true
          }
        }
      },
      take: 10,
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    res.json({
      success: true,
      data: tasks
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 启动服务器
async function startServer() {
  try {
    // 连接数据库
    await prisma.$connect();
    console.log('✓ 数据库连接成功');
    
    // 启动服务器
    app.listen(PORT, () => {
      console.log(`✓ 服务器启动成功，端口: ${PORT}`);
      console.log(`✓ 健康检查: http://localhost:${PORT}/api/v1/health`);
      console.log(`✓ API文档: http://localhost:${PORT}/api/v1`);
    });
    
  } catch (error) {
    console.error('✗ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('收到SIGTERM信号，开始优雅关闭...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('收到SIGINT信号，开始优雅关闭...');
  await prisma.$disconnect();
  process.exit(0);
});

startServer();

module.exports = app;
