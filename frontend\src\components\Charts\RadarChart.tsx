import React from 'react';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  <PERSON>ltip,
  Legend,
} from 'chart.js';
import { Radar } from 'react-chartjs-2';
import { useTheme } from '@/contexts/ThemeContext';

// 注册Chart.js组件
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

interface RadarChartProps {
  data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string;
      borderColor?: string;
      borderWidth?: number;
    }>;
  };
  title?: string;
  height?: number;
  className?: string;
  maxValue?: number;
}

const RadarChart: React.FC<RadarChartProps> = ({
  data,
  title = '分析评分',
  height = 300,
  className = '',
  maxValue = 100,
}) => {
  const { actualTheme } = useTheme();

  // 主题相关的颜色配置
  const themeColors = {
    light: {
      text: '#374151',
      grid: '#e5e7eb',
      border: '#d1d5db',
    },
    dark: {
      text: '#f3f4f6',
      grid: '#374151',
      border: '#4b5563',
    },
  };

  const colors = themeColors[actualTheme];

  // 默认颜色配置
  const defaultColors = [
    {
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      borderColor: 'rgb(59, 130, 246)',
    },
    {
      backgroundColor: 'rgba(16, 185, 129, 0.2)',
      borderColor: 'rgb(16, 185, 129)',
    },
    {
      backgroundColor: 'rgba(245, 158, 11, 0.2)',
      borderColor: 'rgb(245, 158, 11)',
    },
    {
      backgroundColor: 'rgba(239, 68, 68, 0.2)',
      borderColor: 'rgb(239, 68, 68)',
    },
  ];

  // 处理数据集，添加默认颜色
  const processedData = {
    ...data,
    datasets: data.datasets.map((dataset, index) => ({
      ...dataset,
      backgroundColor: dataset.backgroundColor || defaultColors[index % defaultColors.length].backgroundColor,
      borderColor: dataset.borderColor || defaultColors[index % defaultColors.length].borderColor,
      borderWidth: dataset.borderWidth || 2,
      pointBackgroundColor: dataset.borderColor || defaultColors[index % defaultColors.length].borderColor,
      pointBorderColor: '#ffffff',
      pointBorderWidth: 2,
      pointRadius: 4,
      pointHoverRadius: 6,
    })),
  };

  // 图表配置
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: colors.text,
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: actualTheme === 'dark' ? '#374151' : '#ffffff',
        titleColor: colors.text,
        bodyColor: colors.text,
        borderColor: colors.border,
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || '';
            const value = context.parsed.r;
            return `${label}: ${value}分`;
          },
        },
      },
    },
    scales: {
      r: {
        beginAtZero: true,
        max: maxValue,
        min: 0,
        ticks: {
          stepSize: maxValue / 5,
          color: colors.text,
          font: {
            size: 10,
          },
          callback: function(value: any) {
            return value + '分';
          },
        },
        grid: {
          color: colors.grid,
        },
        angleLines: {
          color: colors.grid,
        },
        pointLabels: {
          color: colors.text,
          font: {
            size: 12,
          },
        },
      },
    },
    elements: {
      line: {
        borderWidth: 2,
      },
      point: {
        radius: 4,
        hoverRadius: 6,
      },
    },
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {title && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h3>
        </div>
      )}
      
      <div className="p-4">
        <div style={{ height: `${height}px` }}>
          <Radar data={processedData} options={options} />
        </div>
      </div>
    </div>
  );
};

// 预设的分析评分雷达图
export const AnalysisRadarChart: React.FC<{
  scores: {
    price: number;
    sales: number;
    rating: number;
    competition: number;
    trend: number;
    profit: number;
  };
  title?: string;
  height?: number;
  className?: string;
}> = ({ scores, title = '产品分析评分', height = 300, className = '' }) => {
  const data = {
    labels: ['价格竞争力', '销售表现', '用户评价', '竞争环境', '趋势预测', '利润空间'],
    datasets: [
      {
        label: '评分',
        data: [
          scores.price,
          scores.sales,
          scores.rating,
          scores.competition,
          scores.trend,
          scores.profit,
        ],
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
      },
    ],
  };

  return (
    <RadarChart
      data={data}
      title={title}
      height={height}
      className={className}
      maxValue={100}
    />
  );
};

// 产品对比雷达图
export const ComparisonRadarChart: React.FC<{
  products: Array<{
    name: string;
    scores: {
      price: number;
      sales: number;
      rating: number;
      competition: number;
      trend: number;
      profit: number;
    };
  }>;
  title?: string;
  height?: number;
  className?: string;
}> = ({ products, title = '产品对比分析', height = 300, className = '' }) => {
  const data = {
    labels: ['价格竞争力', '销售表现', '用户评价', '竞争环境', '趋势预测', '利润空间'],
    datasets: products.map((product, index) => ({
      label: product.name,
      data: [
        product.scores.price,
        product.scores.sales,
        product.scores.rating,
        product.scores.competition,
        product.scores.trend,
        product.scores.profit,
      ],
    })),
  };

  return (
    <RadarChart
      data={data}
      title={title}
      height={height}
      className={className}
      maxValue={100}
    />
  );
};

export default RadarChart;
