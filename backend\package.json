{"name": "smartpick-backend", "version": "1.0.0", "description": "AI选品工具后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "knex": "^3.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "bull": "^4.12.2", "axios": "^1.6.2", "xlsx": "^0.18.5", "node-cron": "^3.0.3", "winston": "^3.11.0", "joi": "^17.11.0", "lodash": "^4.17.21", "moment": "^2.29.4", "csv-parser": "^3.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "nodemailer": "^6.9.7", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "@types/node": "^20.9.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}