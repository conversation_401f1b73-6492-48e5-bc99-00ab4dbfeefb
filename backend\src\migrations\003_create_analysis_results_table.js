/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('analysis_results', function(table) {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 关联信息
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.uuid('product_id').nullable();
    table.foreign('product_id').references('id').inTable('products').onDelete('SET NULL');
    
    // 分析基本信息
    table.string('title', 200).notNullable();
    table.text('description').nullable();
    table.enum('type', ['product', 'market', 'competitor', 'trend']).notNullable();
    table.enum('status', ['pending', 'running', 'completed', 'failed', 'cancelled']).defaultTo('pending');
    table.enum('priority', ['low', 'normal', 'high']).defaultTo('normal');
    
    // 分析配置
    table.json('config').notNullable();
    table.json('input_data').nullable();
    table.string('gemini_model', 50).nullable();
    
    // 分析结果
    table.json('result').nullable();
    table.json('insights').nullable();
    table.json('recommendations').nullable();
    table.json('charts_data').nullable();
    table.decimal('confidence_score', 5, 2).nullable();
    
    // 执行信息
    table.timestamp('started_at').nullable();
    table.timestamp('completed_at').nullable();
    table.integer('duration_ms').nullable();
    table.text('error_message').nullable();
    table.json('error_details').nullable();
    table.integer('retry_count').defaultTo(0);
    
    // 使用统计
    table.integer('view_count').defaultTo(0);
    table.integer('share_count').defaultTo(0);
    table.integer('export_count').defaultTo(0);
    table.timestamp('last_viewed_at').nullable();
    
    // 分享信息
    table.string('share_token', 100).nullable().unique();
    table.enum('share_type', ['private', 'public']).defaultTo('private');
    table.timestamp('share_expires_at').nullable();
    
    // 标签和分类
    table.json('tags').nullable();
    table.string('category', 100).nullable();
    table.boolean('is_favorite').defaultTo(false);
    table.boolean('is_archived').defaultTo(false);
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['user_id']);
    table.index(['product_id']);
    table.index(['type']);
    table.index(['status']);
    table.index(['priority']);
    table.index(['category']);
    table.index(['is_favorite']);
    table.index(['is_archived']);
    table.index(['created_at']);
    table.index(['completed_at']);
    table.index(['share_token']);
    
    // 复合索引
    table.index(['user_id', 'type']);
    table.index(['user_id', 'status']);
    table.index(['type', 'status']);
    table.index(['user_id', 'created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('analysis_results');
};
