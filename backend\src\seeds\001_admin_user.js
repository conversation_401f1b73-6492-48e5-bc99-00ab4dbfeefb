const bcrypt = require('bcryptjs');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  // 删除现有数据
  await knex('users').del();
  
  // 创建管理员用户
  const adminPassword = await bcrypt.hash('Admin123!', 12);
  const testPassword = await bcrypt.hash('Test123!', 12);
  
  await knex('users').insert([
    {
      id: knex.raw('gen_random_uuid()'),
      username: 'admin',
      email: '<EMAIL>',
      password_hash: adminPassword,
      role: 'admin',
      status: 'active',
      email_verified: true,
      preferences: JSON.stringify({
        theme: 'light',
        notifications: {
          email: true,
          push: true,
          analysis_complete: true,
          price_alert: true
        },
        dashboard: {
          default_view: 'overview',
          items_per_page: 20
        }
      }),
      timezone: 'Asia/Shanghai',
      language: 'zh-CN',
      created_at: knex.fn.now(),
      updated_at: knex.fn.now()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      username: 'testuser',
      email: '<EMAIL>',
      password_hash: testPassword,
      role: 'user',
      status: 'active',
      email_verified: true,
      bio: '这是一个测试用户账户',
      preferences: JSON.stringify({
        theme: 'light',
        notifications: {
          email: true,
          push: false,
          analysis_complete: true,
          price_alert: false
        },
        dashboard: {
          default_view: 'products',
          items_per_page: 10
        }
      }),
      timezone: 'Asia/Shanghai',
      language: 'zh-CN',
      created_at: knex.fn.now(),
      updated_at: knex.fn.now()
    }
  ]);
  
  console.log('✅ 管理员用户创建成功');
  console.log('📧 管理员邮箱: <EMAIL>');
  console.log('🔑 管理员密码: Admin123!');
  console.log('📧 测试用户邮箱: <EMAIL>');
  console.log('🔑 测试用户密码: Test123!');
};
