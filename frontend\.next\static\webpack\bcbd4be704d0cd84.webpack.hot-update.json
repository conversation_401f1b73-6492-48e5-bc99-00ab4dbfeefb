{"c": ["webpack"], "r": ["/_error", "pages/import", "pages/products", "pages/analysis"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!", "./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js", "./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js", "./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js", "./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "./node_modules/attr-accept/dist/es/index.js", "./node_modules/file-selector/dist/es2015/file-selector.js", "./node_modules/file-selector/dist/es2015/file.js", "./node_modules/file-selector/dist/es2015/index.js", "./node_modules/next/dist/build/polyfills/object-assign.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cimport%5Cindex.tsx&page=%2Fimport!", "./node_modules/prop-types/checkPropTypes.js", "./node_modules/prop-types/factoryWithTypeCheckers.js", "./node_modules/prop-types/index.js", "./node_modules/prop-types/lib/ReactPropTypesSecret.js", "./node_modules/prop-types/lib/has.js", "./node_modules/react-dropzone/dist/es/index.js", "./node_modules/react-dropzone/dist/es/utils/index.js", "./node_modules/react-is/cjs/react-is.development.js", "./node_modules/react-is/index.js", "./node_modules/tslib/tslib.es6.mjs", "./src/pages/import/index.tsx", "__barrel_optimize__?names=CheckCircleIcon,ClockIcon,CloudArrowUpIcon,DocumentArrowDownIcon,InformationCircleIcon,XCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts!", "./src/pages/products.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis!", "./src/pages/analysis.tsx"]}