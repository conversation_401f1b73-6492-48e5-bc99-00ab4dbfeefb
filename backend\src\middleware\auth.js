const jwt = require('jsonwebtoken');
const { createError, asyncHandler } = require('./errorHandler');
const { cache } = require('../config/redis');
const logger = require('../utils/logger');

/**
 * 生成JWT令牌
 */
const generateTokens = (payload) => {
  const accessToken = jwt.sign(
    payload,
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
  
  const refreshToken = jwt.sign(
    payload,
    process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d' }
  );
  
  return { accessToken, refreshToken };
};

/**
 * 验证JWT令牌
 */
const verifyToken = (token, secret = process.env.JWT_SECRET) => {
  try {
    return jwt.verify(token, secret);
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw createError.unauthorized('访问令牌已过期', 'TOKEN_EXPIRED');
    } else if (error.name === 'JsonWebTokenError') {
      throw createError.unauthorized('无效的访问令牌', 'INVALID_TOKEN');
    } else {
      throw createError.unauthorized('令牌验证失败', 'TOKEN_ERROR');
    }
  }
};

/**
 * 认证中间件
 */
const authenticate = asyncHandler(async (req, res, next) => {
  let token;
  
  // 从请求头获取令牌
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  
  if (!token) {
    throw createError.unauthorized('访问令牌缺失', 'TOKEN_MISSING');
  }
  
  try {
    // 验证令牌
    const decoded = verifyToken(token);
    
    // 检查令牌是否在黑名单中
    const isBlacklisted = await cache.get(`blacklist:${token}`);
    if (isBlacklisted) {
      throw createError.unauthorized('访问令牌已失效', 'TOKEN_BLACKLISTED');
    }
    
    // 检查用户是否存在且状态正常
    const { db } = require('../config/database');
    const user = await db('users')
      .where({ id: decoded.userId, status: 'active' })
      .first();
    
    if (!user) {
      throw createError.unauthorized('用户不存在或已被禁用', 'USER_NOT_FOUND');
    }
    
    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
      status: user.status
    };
    
    // 记录用户活动
    logger.logUserActivity(user.id, 'api_access', {
      endpoint: req.originalUrl,
      method: req.method,
      ip: req.ip
    });
    
    next();
  } catch (error) {
    next(error);
  }
});

/**
 * 授权中间件
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(createError.unauthorized('用户未认证', 'USER_NOT_AUTHENTICATED'));
    }
    
    if (!roles.includes(req.user.role)) {
      return next(createError.forbidden('权限不足', 'INSUFFICIENT_PERMISSIONS'));
    }
    
    next();
  };
};

/**
 * 可选认证中间件（不强制要求认证）
 */
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;
  
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  
  if (token) {
    try {
      const decoded = verifyToken(token);
      
      // 检查令牌是否在黑名单中
      const isBlacklisted = await cache.get(`blacklist:${token}`);
      if (!isBlacklisted) {
        // 获取用户信息
        const { db } = require('../config/database');
        const user = await db('users')
          .where({ id: decoded.userId, status: 'active' })
          .first();
        
        if (user) {
          req.user = {
            id: user.id,
            email: user.email,
            username: user.username,
            role: user.role,
            status: user.status
          };
        }
      }
    } catch (error) {
      // 可选认证失败时不抛出错误，继续执行
      logger.warn('可选认证失败:', error.message);
    }
  }
  
  next();
});

/**
 * 检查用户状态中间件
 */
const checkUserStatus = (requiredStatus = 'active') => {
  return (req, res, next) => {
    if (!req.user) {
      return next(createError.unauthorized('用户未认证', 'USER_NOT_AUTHENTICATED'));
    }
    
    if (req.user.status !== requiredStatus) {
      return next(createError.forbidden('用户状态不正确', 'INVALID_USER_STATUS'));
    }
    
    next();
  };
};

/**
 * 限制用户访问自己的资源
 */
const restrictToOwner = (userIdField = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return next(createError.unauthorized('用户未认证', 'USER_NOT_AUTHENTICATED'));
    }
    
    const resourceUserId = req.params[userIdField] || req.body[userIdField] || req.query[userIdField];
    
    if (req.user.role !== 'admin' && req.user.id !== resourceUserId) {
      return next(createError.forbidden('只能访问自己的资源', 'ACCESS_DENIED'));
    }
    
    next();
  };
};

/**
 * API密钥认证中间件
 */
const authenticateApiKey = asyncHandler(async (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    throw createError.unauthorized('API密钥缺失', 'API_KEY_MISSING');
  }
  
  // 验证API密钥
  const { db } = require('../config/database');
  const keyRecord = await db('api_keys')
    .where({ key: apiKey, status: 'active' })
    .first();
  
  if (!keyRecord) {
    throw createError.unauthorized('无效的API密钥', 'INVALID_API_KEY');
  }
  
  // 检查API密钥是否过期
  if (keyRecord.expires_at && new Date() > new Date(keyRecord.expires_at)) {
    throw createError.unauthorized('API密钥已过期', 'API_KEY_EXPIRED');
  }
  
  // 更新最后使用时间
  await db('api_keys')
    .where({ id: keyRecord.id })
    .update({ last_used_at: new Date() });
  
  // 获取用户信息
  const user = await db('users')
    .where({ id: keyRecord.user_id, status: 'active' })
    .first();
  
  if (!user) {
    throw createError.unauthorized('API密钥关联的用户不存在', 'USER_NOT_FOUND');
  }
  
  req.user = {
    id: user.id,
    email: user.email,
    username: user.username,
    role: user.role,
    status: user.status,
    apiKey: keyRecord
  };
  
  next();
});

/**
 * 刷新令牌验证
 */
const verifyRefreshToken = (token) => {
  const secret = process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET;
  return verifyToken(token, secret);
};

/**
 * 将令牌加入黑名单
 */
const blacklistToken = async (token) => {
  try {
    const decoded = jwt.decode(token);
    if (decoded && decoded.exp) {
      const ttl = decoded.exp - Math.floor(Date.now() / 1000);
      if (ttl > 0) {
        await cache.set(`blacklist:${token}`, true, ttl);
      }
    }
  } catch (error) {
    logger.error('将令牌加入黑名单失败:', error);
  }
};

module.exports = {
  generateTokens,
  verifyToken,
  authenticate,
  authorize,
  optionalAuth,
  checkUserStatus,
  restrictToOwner,
  authenticateApiKey,
  verifyRefreshToken,
  blacklistToken
};
