import React from 'react';
import BaseChart from './BaseChart';
import { PriceHistory } from '@/types';

interface PriceTrendChartProps {
  data: PriceHistory[];
  title?: string;
  height?: number;
  showPrediction?: boolean;
  predictionData?: Array<{ date: string; price: number; isPrediction: boolean }>;
  className?: string;
}

const PriceTrendChart: React.FC<PriceTrendChartProps> = ({
  data,
  title = '价格趋势',
  height = 300,
  showPrediction = false,
  predictionData = [],
  className = '',
}) => {
  // 处理历史数据
  const processHistoricalData = () => {
    const sortedData = [...data].sort((a, b) => 
      new Date(a.recorded_at).getTime() - new Date(b.recorded_at).getTime()
    );

    return {
      labels: sortedData.map(item => 
        new Date(item.recorded_at).toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric',
        })
      ),
      prices: sortedData.map(item => item.price),
      originalPrices: sortedData.map(item => item.original_price || item.price),
      promotions: sortedData.map(item => item.is_promotion),
    };
  };

  // 处理预测数据
  const processPredictionData = () => {
    if (!showPrediction || predictionData.length === 0) return null;

    const historicalPart = predictionData.filter(item => !item.isPrediction);
    const predictionPart = predictionData.filter(item => item.isPrediction);

    return {
      labels: predictionData.map(item => 
        new Date(item.date).toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric',
        })
      ),
      historical: predictionData.map(item => item.isPrediction ? null : item.price),
      prediction: predictionData.map(item => item.isPrediction ? item.price : null),
    };
  };

  const historicalData = processHistoricalData();
  const prediction = processPredictionData();

  // 构建图表数据
  const chartData = {
    labels: prediction ? prediction.labels : historicalData.labels,
    datasets: [
      // 当前价格线
      {
        label: '当前价格',
        data: prediction ? prediction.historical : historicalData.prices,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        tension: 0.4,
      },
      // 原价线（如果有折扣）
      ...(historicalData.originalPrices.some((price, index) => price !== historicalData.prices[index]) ? [{
        label: '原价',
        data: prediction ? null : historicalData.originalPrices,
        borderColor: 'rgb(156, 163, 175)',
        backgroundColor: 'rgba(156, 163, 175, 0.1)',
        borderWidth: 1,
        borderDash: [5, 5],
        pointRadius: 0,
        pointHoverRadius: 4,
        tension: 0.4,
      }] : []),
      // 预测价格线
      ...(prediction ? [{
        label: '价格预测',
        data: prediction.prediction,
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        borderWidth: 2,
        borderDash: [10, 5],
        pointBackgroundColor: 'rgb(245, 158, 11)',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        tension: 0.4,
      }] : []),
    ],
  };

  // 图表配置
  const options = {
    plugins: {
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            const dataIndex = context.dataIndex;
            
            let tooltipLabel = `${label}: ¥${value?.toFixed(2)}`;
            
            // 如果是促销价格，添加标识
            if (!prediction && historicalData.promotions[dataIndex]) {
              tooltipLabel += ' (促销)';
            }
            
            return tooltipLabel;
          },
          afterLabel: function(context: any) {
            const dataIndex = context.dataIndex;
            
            if (!prediction && historicalData.promotions[dataIndex]) {
              const originalPrice = historicalData.originalPrices[dataIndex];
              const currentPrice = historicalData.prices[dataIndex];
              const discount = ((originalPrice - currentPrice) / originalPrice * 100).toFixed(1);
              return `折扣: ${discount}%`;
            }
            
            return '';
          },
        },
      },
      legend: {
        display: true,
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: false,
        ticks: {
          callback: function(value: any) {
            return '¥' + value.toFixed(2);
          },
        },
      },
    },
    elements: {
      point: {
        radius: function(context: any) {
          const dataIndex = context.dataIndex;
          // 促销点显示更大
          if (!prediction && historicalData.promotions[dataIndex]) {
            return 6;
          }
          return 4;
        },
        backgroundColor: function(context: any) {
          const dataIndex = context.dataIndex;
          // 促销点显示红色
          if (!prediction && historicalData.promotions[dataIndex]) {
            return 'rgb(239, 68, 68)';
          }
          return context.dataset.pointBackgroundColor;
        },
      },
    },
  };

  // 计算统计信息
  const stats = React.useMemo(() => {
    if (data.length === 0) return null;

    const prices = data.map(item => item.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const currentPrice = prices[prices.length - 1];
    const previousPrice = prices.length > 1 ? prices[prices.length - 2] : currentPrice;
    const priceChange = currentPrice - previousPrice;
    const priceChangePercent = previousPrice !== 0 ? (priceChange / previousPrice) * 100 : 0;

    return {
      minPrice,
      maxPrice,
      avgPrice,
      currentPrice,
      priceChange,
      priceChangePercent,
    };
  }, [data]);

  return (
    <div className={className}>
      <BaseChart
        type="line"
        data={chartData}
        options={options}
        height={height}
        title={title}
      />
      
      {/* 价格统计信息 */}
      {stats && (
        <div className="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="text-xs text-gray-500 dark:text-gray-400">当前价格</div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              ¥{stats.currentPrice.toFixed(2)}
            </div>
            <div className={`text-xs ${
              stats.priceChange >= 0 
                ? 'text-red-600 dark:text-red-400' 
                : 'text-green-600 dark:text-green-400'
            }`}>
              {stats.priceChange >= 0 ? '+' : ''}
              {stats.priceChangePercent.toFixed(1)}%
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="text-xs text-gray-500 dark:text-gray-400">平均价格</div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              ¥{stats.avgPrice.toFixed(2)}
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="text-xs text-gray-500 dark:text-gray-400">最低价格</div>
            <div className="text-lg font-semibold text-green-600 dark:text-green-400">
              ¥{stats.minPrice.toFixed(2)}
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="text-xs text-gray-500 dark:text-gray-400">最高价格</div>
            <div className="text-lg font-semibold text-red-600 dark:text-red-400">
              ¥{stats.maxPrice.toFixed(2)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PriceTrendChart;
