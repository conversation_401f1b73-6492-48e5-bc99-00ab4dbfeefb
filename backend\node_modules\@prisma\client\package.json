{"name": "@prisma/client", "version": "5.22.0", "description": "Prisma Client is an auto-generated, type-safe and modern JavaScript/TypeScript ORM for Node.js that's tailored to your data. Supports PostgreSQL, CockroachDB, MySQL, MariaDB, SQL Server, SQLite & MongoDB databases.", "keywords": ["ORM", "Prisma", "prisma2", "Prisma Client", "client", "query", "query-builder", "database", "db", "JavaScript", "JS", "TypeScript", "TS", "SQL", "SQLite", "pg", "Postgres", "PostgreSQL", "CockroachDB", "MySQL", "MariaDB", "MSSQL", "SQL Server", "SQLServer", "MongoDB", "react-native"], "main": "default.js", "types": "default.d.ts", "browser": "index-browser.js", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./default.d.ts", "node": "./default.js", "edge-light": "./default.js", "workerd": "./default.js", "worker": "./default.js", "browser": "./index-browser.js"}, "import": {"types": "./default.d.ts", "node": "./default.js", "edge-light": "./default.js", "workerd": "./default.js", "worker": "./default.js", "browser": "./index-browser.js"}, "default": "./default.js"}, "./edge": {"types": "./edge.d.ts", "require": "./edge.js", "import": "./edge.js", "default": "./edge.js"}, "./react-native": {"types": "./react-native.d.ts", "require": "./react-native.js", "import": "./react-native.js", "default": "./react-native.js"}, "./extension": {"types": "./extension.d.ts", "require": "./extension.js", "import": "./extension.js", "default": "./extension.js"}, "./index-browser": {"types": "./index.d.ts", "require": "./index-browser.js", "import": "./index-browser.js", "default": "./index-browser.js"}, "./index": {"types": "./index.d.ts", "require": "./index.js", "import": "./index.js", "default": "./index.js"}, "./wasm": {"types": "./wasm.d.ts", "require": "./wasm.js", "import": "./wasm.js", "default": "./wasm.js"}, "./runtime/library": {"types": "./runtime/library.d.ts", "require": "./runtime/library.js", "import": "./runtime/library.js", "default": "./runtime/library.js"}, "./runtime/binary": {"types": "./runtime/binary.d.ts", "require": "./runtime/binary.js", "import": "./runtime/binary.js", "default": "./runtime/binary.js"}, "./generator-build": {"require": "./generator-build/index.js", "import": "./generator-build/index.js", "default": "./generator-build/index.js"}, "./sql": {"require": {"types": "./sql.d.ts", "node": "./sql.js", "default": "./sql.js"}, "import": {"types": "./sql.d.ts", "node": "./sql.mjs", "default": "./sql.mjs"}, "default": "./sql.js"}, "./*": "./*"}, "license": "Apache-2.0", "engines": {"node": ">=16.13"}, "homepage": "https://www.prisma.io", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/client"}, "author": "<PERSON> <suchane<PERSON>@prisma.io>", "bugs": "https://github.com/prisma/prisma/issues", "files": ["README.md", "runtime", "!runtime/*.map", "scripts", "generator-build", "edge.js", "edge.d.ts", "wasm.js", "wasm.d.ts", "index.js", "index.d.ts", "react-native.js", "react-native.d.ts", "default.js", "default.d.ts", "index-browser.js", "extension.js", "extension.d.ts", "sql.d.ts", "sql.js", "sql.mjs"], "devDependencies": {"@cloudflare/workers-types": "4.20240614.0", "@codspeed/benchmark.js-plugin": "3.1.1", "@faker-js/faker": "8.4.1", "@fast-check/jest": "1.8.2", "@inquirer/prompts": "5.0.5", "@jest/create-cache-key-function": "29.7.0", "@jest/globals": "29.7.0", "@jest/test-sequencer": "29.7.0", "@libsql/client": "0.8.0", "@neondatabase/serverless": "0.9.3", "@opentelemetry/api": "1.9.0", "@opentelemetry/context-async-hooks": "1.25.1", "@opentelemetry/instrumentation": "0.52.1", "@opentelemetry/resources": "1.25.1", "@opentelemetry/sdk-trace-base": "1.25.1", "@opentelemetry/semantic-conventions": "1.25.1", "@planetscale/database": "1.18.0", "@prisma/engines-version": "5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2", "@prisma/mini-proxy": "0.9.5", "@prisma/query-engine-wasm": "5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2", "@snaplet/copycat": "0.17.3", "@swc-node/register": "1.10.9", "@swc/core": "1.6.13", "@swc/jest": "0.2.36", "@timsuchanek/copy": "1.4.5", "@types/debug": "4.1.12", "@types/fs-extra": "9.0.13", "@types/jest": "29.5.12", "@types/js-levenshtein": "1.1.3", "@types/mssql": "9.1.5", "@types/node": "18.19.31", "@types/pg": "8.11.6", "arg": "5.0.2", "benchmark": "2.1.4", "ci-info": "4.0.0", "decimal.js": "10.4.3", "detect-runtime": "1.0.4", "env-paths": "2.2.1", "esbuild": "0.23.0", "execa": "5.1.1", "expect-type": "0.19.0", "flat-map-polyfill": "0.3.8", "fs-extra": "11.1.1", "get-stream": "6.0.1", "globby": "11.1.0", "indent-string": "4.0.0", "jest": "29.7.0", "jest-extended": "4.0.2", "jest-junit": "16.0.0", "jest-serializer-ansi-escapes": "3.0.0", "jest-snapshot": "29.7.0", "js-levenshtein": "1.1.6", "kleur": "4.1.5", "klona": "2.0.6", "mariadb": "3.3.1", "memfs": "4.9.3", "mssql": "11.0.1", "new-github-issue-url": "0.2.1", "node-fetch": "3.3.2", "p-retry": "4.6.2", "pg": "8.11.5", "pkg-up": "3.1.0", "pluralize": "8.0.0", "resolve": "1.22.8", "rimraf": "3.0.2", "simple-statistics": "7.8.5", "sort-keys": "4.2.0", "source-map-support": "0.5.21", "sql-template-tag": "5.2.1", "stacktrace-parser": "0.1.10", "strip-ansi": "6.0.1", "strip-indent": "3.0.0", "ts-node": "10.9.2", "ts-pattern": "5.2.0", "tsd": "0.31.1", "typescript": "5.4.5", "undici": "5.28.4", "wrangler": "3.62.0", "zx": "7.2.3", "@prisma/adapter-d1": "5.22.0", "@prisma/adapter-libsql": "5.22.0", "@prisma/adapter-neon": "5.22.0", "@prisma/adapter-pg": "5.22.0", "@prisma/adapter-planetscale": "5.22.0", "@prisma/driver-adapter-utils": "5.22.0", "@prisma/adapter-pg-worker": "5.22.0", "@prisma/debug": "5.22.0", "@prisma/engines": "5.22.0", "@prisma/fetch-engine": "5.22.0", "@prisma/generator-helper": "5.22.0", "@prisma/get-platform": "5.22.0", "@prisma/instrumentation": "5.22.0", "@prisma/internals": "5.22.0", "@prisma/migrate": "5.22.0", "@prisma/pg-worker": "5.22.0"}, "peerDependencies": {"prisma": "*"}, "peerDependenciesMeta": {"prisma": {"optional": true}}, "sideEffects": false, "scripts": {"dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "dotenv -e ../../.db.env -- jest --silent", "test:e2e": "dotenv -e ../../.db.env -- tsx tests/e2e/_utils/run.ts", "test:functional": "dotenv -e ../../.db.env -- tsx helpers/functional-test/run-tests.ts", "test:memory": "dotenv -e ../../.db.env -- tsx helpers/memory-tests.ts", "test:functional:code": "dotenv -e ../../.db.env -- tsx helpers/functional-test/run-tests.ts --no-types", "test:functional:types": "dotenv -e ../../.db.env -- tsx helpers/functional-test/run-tests.ts --types-only", "test-notypes": "dotenv -e ../../.db.env -- jest --testPathIgnorePatterns src/__tests__/types/types.test.ts", "generate": "node scripts/postinstall.js", "postinstall": "node scripts/postinstall.js", "new-test": "tsx ./helpers/new-test/new-test.ts"}}