import React, { ReactNode } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import Sidebar from './Sidebar';
import Header from './Header';
import LoadingScreen from '@/components/LoadingScreen';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  // 不需要布局的页面
  const noLayoutPages = ['/auth/login', '/auth/register', '/auth/forgot-password'];
  const isNoLayoutPage = noLayoutPages.includes(router.pathname);

  // 如果是认证页面，直接渲染内容
  if (isNoLayoutPage) {
    return <>{children}</>;
  }

  // 如果正在加载认证状态，显示加载屏幕
  if (isLoading) {
    return <LoadingScreen />;
  }

  // 如果未认证，重定向到登录页面
  if (!isAuthenticated) {
    router.push('/auth/login');
    return <LoadingScreen />;
  }

  // 渲染主布局
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 侧边栏 */}
      <Sidebar />
      
      {/* 主内容区域 */}
      <div className="lg:pl-64">
        {/* 顶部导航 */}
        <Header />
        
        {/* 页面内容 */}
        <main className="py-6">
          <div className="container-responsive">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
