const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 8000;

// 中间件 - 更宽松的CORS配置
app.use(cors({
  origin: function (origin, callback) {
    // 允许所有localhost端口
    if (!origin || origin.startsWith('http://localhost:') || origin.startsWith('http://127.0.0.1:')) {
      callback(null, true);
    } else {
      callback(null, true); // 开发环境允许所有来源
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  maxAge: 86400 // 24小时
}));

app.use(express.json());

// 处理预检请求
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin);
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});

// 模拟用户数据
const users = [
  {
    id: 1,
    email: '<EMAIL>',
    username: 'test',
    password: '$2a$12$pzpkuw5/wMawS75XdMu42.Xjenrolg1WNkYJFLRrSHCnPf2yGuo5u', // test123
    firstName: 'Test',
    lastName: 'User',
    role: 'USER',
    status: 'ACTIVE'
  },
  {
    id: 2,
    email: '<EMAIL>',
    username: 'admin',
    password: '$2a$12$pzpkuw5/wMawS75XdMu42.Xjenrolg1WNkYJFLRrSHCnPf2yGuo5u', // admin123 (same hash for simplicity)
    firstName: 'Admin',
    lastName: 'User',
    role: 'ADMIN',
    status: 'ACTIVE'
  }
];

// 健康检查
app.get('/api/v1/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    database: 'connected',
    version: '1.0.0'
  });
});

// 登录接口
app.post('/api/v1/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '邮箱和密码不能为空'
      });
    }

    // 查找用户
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }

    // 生成JWT token
    const accessToken = jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        role: user.role 
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      message: '登录成功',
      user: userWithoutPassword,
      accessToken
    });

  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取当前用户信息
app.get('/api/v1/auth/me', (req, res) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '访问令牌缺失'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    const user = users.find(u => u.id === decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    const { password: _, ...userWithoutPassword } = user;
    res.json({
      success: true,
      user: userWithoutPassword
    });

  } catch (error) {
    res.status(401).json({
      success: false,
      message: '无效的访问令牌'
    });
  }
});

// 登出接口
app.post('/api/v1/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: '登出成功'
  });
});

// 获取用户列表
app.get('/api/v1/users', (req, res) => {
  const usersWithoutPasswords = users.map(({ password, ...user }) => user);
  res.json({
    success: true,
    data: usersWithoutPasswords
  });
});

// 获取产品列表（模拟数据）
app.get('/api/v1/products', (req, res) => {
  const products = [
    {
      id: 1,
      name: 'iPhone 15 Pro Max',
      brand: 'Apple',
      category: '手机',
      price: 9999,
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
      user: { id: 1, username: 'test' }
    },
    {
      id: 2,
      name: '小米14 Ultra',
      brand: '小米',
      category: '手机',
      price: 5999,
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
      user: { id: 1, username: 'test' }
    }
  ];

  res.json({
    success: true,
    data: products
  });
});

// 获取导入任务列表（模拟数据）
app.get('/api/v1/import/tasks', (req, res) => {
  const tasks = [
    {
      id: 1,
      fileName: 'products.xlsx',
      status: 'COMPLETED',
      totalRecords: 100,
      successCount: 95,
      errorCount: 5,
      createdAt: new Date().toISOString(),
      user: { id: 1, username: 'test' }
    }
  ];

  res.json({
    success: true,
    data: tasks
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`✓ 后端服务器启动成功，端口: ${PORT}`);
  console.log(`✓ 健康检查: http://localhost:${PORT}/api/v1/health`);
  console.log(`✓ 登录接口: http://localhost:${PORT}/api/v1/auth/login`);
});

module.exports = app;
