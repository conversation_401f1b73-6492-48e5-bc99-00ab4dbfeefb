"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/modern.css":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/modern.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* 现代化UI样式系统 */\\n\\n/* CSS变量定义 */\\n:root {\\n  /* 颜色系统 */\\n  --color-primary-50: #eff6ff;\\n  --color-primary-100: #dbeafe;\\n  --color-primary-200: #bfdbfe;\\n  --color-primary-300: #93c5fd;\\n  --color-primary-400: #60a5fa;\\n  --color-primary-500: #3b82f6;\\n  --color-primary-600: #2563eb;\\n  --color-primary-700: #1d4ed8;\\n  --color-primary-800: #1e40af;\\n  --color-primary-900: #1e3a8a;\\n\\n  --color-gray-50: #f9fafb;\\n  --color-gray-100: #f3f4f6;\\n  --color-gray-200: #e5e7eb;\\n  --color-gray-300: #d1d5db;\\n  --color-gray-400: #9ca3af;\\n  --color-gray-500: #6b7280;\\n  --color-gray-600: #4b5563;\\n  --color-gray-700: #374151;\\n  --color-gray-800: #1f2937;\\n  --color-gray-900: #111827;\\n\\n  --color-success-500: #10b981;\\n  --color-success-600: #059669;\\n  --color-warning-500: #f59e0b;\\n  --color-warning-600: #d97706;\\n  --color-error-500: #ef4444;\\n  --color-error-600: #dc2626;\\n\\n  /* 间距系统 */\\n  --spacing-1: 0.25rem;\\n  --spacing-2: 0.5rem;\\n  --spacing-3: 0.75rem;\\n  --spacing-4: 1rem;\\n  --spacing-5: 1.25rem;\\n  --spacing-6: 1.5rem;\\n  --spacing-8: 2rem;\\n  --spacing-10: 2.5rem;\\n  --spacing-12: 3rem;\\n  --spacing-16: 4rem;\\n  --spacing-20: 5rem;\\n  --spacing-24: 6rem;\\n\\n  /* 圆角系统 */\\n  --radius-sm: 0.125rem;\\n  --radius-base: 0.25rem;\\n  --radius-md: 0.375rem;\\n  --radius-lg: 0.5rem;\\n  --radius-xl: 0.75rem;\\n  --radius-2xl: 1rem;\\n  --radius-full: 9999px;\\n\\n  /* 阴影系统 */\\n  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n\\n  /* 字体系统 */\\n  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\\n  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;\\n\\n  /* 图标尺寸系统 */\\n  --icon-xs: 0.75rem;\\n  --icon-sm: 1rem;\\n  --icon-base: 1.25rem;\\n  --icon-lg: 1.5rem;\\n  --icon-xl: 2rem;\\n\\n  /* 过渡动画 */\\n  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);\\n  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);\\n  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n/* 基础重置 */\\n*,\\n*::before,\\n*::after {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml {\\n  font-family: var(--font-sans);\\n  line-height: 1.6;\\n  -webkit-text-size-adjust: 100%;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  color: var(--color-gray-900);\\n  background-color: var(--color-gray-50);\\n  min-height: 100vh;\\n}\\n\\n#__next {\\n  min-height: 100vh;\\n}\\n\\n/* 现代化按钮组件 */\\n.btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--spacing-2);\\n  padding: var(--spacing-3) var(--spacing-6);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  line-height: 1.25;\\n  border-radius: var(--radius-lg);\\n  border: 1px solid transparent;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  text-decoration: none;\\n  outline: none;\\n  position: relative;\\n  min-height: 2.75rem;\\n  white-space: nowrap;\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\n\\n.btn:focus-visible {\\n  outline: 2px solid var(--color-primary-500);\\n  outline-offset: 2px;\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n\\n/* 按钮变体 */\\n.btn-primary {\\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));\\n  color: white;\\n  border-color: var(--color-primary-500);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.btn-primary:hover:not(:disabled) {\\n  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-secondary {\\n  background: white;\\n  color: var(--color-gray-700);\\n  border-color: var(--color-gray-300);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.btn-secondary:hover:not(:disabled) {\\n  background: var(--color-gray-50);\\n  border-color: var(--color-gray-400);\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-ghost {\\n  background: transparent;\\n  color: var(--color-gray-700);\\n  border-color: transparent;\\n}\\n\\n.btn-ghost:hover:not(:disabled) {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n/* 按钮尺寸 */\\n.btn-sm {\\n  padding: var(--spacing-2) var(--spacing-4);\\n  font-size: 0.75rem;\\n  min-height: 2rem;\\n}\\n\\n.btn-lg {\\n  padding: var(--spacing-4) var(--spacing-8);\\n  font-size: 1rem;\\n  min-height: 3rem;\\n}\\n\\n/* 现代化输入框组件 */\\n.input-group {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.input {\\n  display: block;\\n  width: 100%;\\n  padding: var(--spacing-3) var(--spacing-4);\\n  font-size: 0.875rem;\\n  line-height: 1.5;\\n  color: var(--color-gray-900);\\n  background: white;\\n  border: 1.5px solid var(--color-gray-300);\\n  border-radius: var(--radius-lg);\\n  transition: all var(--transition-fast);\\n  outline: none;\\n  min-height: 2.75rem;\\n}\\n\\n.input::placeholder {\\n  color: var(--color-gray-400);\\n}\\n\\n.input:focus {\\n  border-color: var(--color-primary-500);\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n\\n.input:invalid {\\n  border-color: var(--color-error-500);\\n}\\n\\n.input:invalid:focus {\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n\\n/* 浮动标签效果 */\\n.input-floating {\\n  padding-top: var(--spacing-6);\\n  padding-bottom: var(--spacing-2);\\n}\\n\\n.input-floating + .input-label {\\n  position: absolute;\\n  top: 0;\\n  left: var(--spacing-4);\\n  padding: 0 var(--spacing-1);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: var(--color-gray-600);\\n  background: white;\\n  transform: translateY(0.75rem);\\n  transition: all var(--transition-fast);\\n  pointer-events: none;\\n}\\n\\n.input-floating:focus + .input-label,\\n.input-floating:not(:placeholder-shown) + .input-label {\\n  transform: translateY(-0.5rem);\\n  font-size: 0.625rem;\\n  color: var(--color-primary-600);\\n}\\n\\n/* 现代化卡片组件 */\\n.card {\\n  background: white;\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-base);\\n  border: 1px solid var(--color-gray-200);\\n  overflow: hidden;\\n  transition: all var(--transition-base);\\n}\\n\\n.card:hover {\\n  box-shadow: var(--shadow-lg);\\n  transform: translateY(-2px);\\n}\\n\\n.card-header {\\n  padding: var(--spacing-6);\\n  border-bottom: 1px solid var(--color-gray-200);\\n  background: linear-gradient(135deg, var(--color-gray-50), white);\\n}\\n\\n.card-body {\\n  padding: var(--spacing-6);\\n}\\n\\n.card-footer {\\n  padding: var(--spacing-6);\\n  border-top: 1px solid var(--color-gray-200);\\n  background: var(--color-gray-50);\\n}\\n\\n/* 现代化徽章组件 */\\n.badge {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--spacing-1);\\n  padding: var(--spacing-1) var(--spacing-3);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border-radius: var(--radius-full);\\n  white-space: nowrap;\\n}\\n\\n.badge-primary {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-800);\\n}\\n\\n.badge-success {\\n  background: #d1fae5;\\n  color: #065f46;\\n}\\n\\n.badge-warning {\\n  background: #fef3c7;\\n  color: #92400e;\\n}\\n\\n.badge-error {\\n  background: #fee2e2;\\n  color: #991b1b;\\n}\\n\\n/* 现代化加载动画 */\\n.loading-spinner {\\n  display: inline-block;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  border: 2px solid var(--color-gray-200);\\n  border-top: 2px solid var(--color-primary-500);\\n  border-radius: 50%;\\n  animation: spin 1s linear infinite;\\n}\\n\\n@keyframes spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n/* 现代化模态框 */\\n.modal-overlay {\\n  position: fixed;\\n  inset: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  z-index: 50;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-4);\\n}\\n\\n.modal-content {\\n  background: white;\\n  border-radius: var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  max-width: 32rem;\\n  width: 100%;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  transform: scale(0.95);\\n  transition: all var(--transition-base);\\n}\\n\\n.modal-content.show {\\n  transform: scale(1);\\n}\\n\\n/* 现代化导航 */\\n.nav-link {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-3);\\n  padding: var(--spacing-3) var(--spacing-4);\\n  color: var(--color-gray-700);\\n  text-decoration: none;\\n  border-radius: var(--radius-lg);\\n  transition: all var(--transition-fast);\\n  font-weight: 500;\\n}\\n\\n.nav-link:hover {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n.nav-link.active {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-700);\\n}\\n\\n/* 现代化表格 */\\n.table {\\n  width: 100%;\\n  border-collapse: collapse;\\n  background: white;\\n  border-radius: var(--radius-lg);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-base);\\n}\\n\\n.table th {\\n  background: var(--color-gray-50);\\n  padding: var(--spacing-4);\\n  text-align: left;\\n  font-weight: 600;\\n  color: var(--color-gray-700);\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n\\n.table td {\\n  padding: var(--spacing-4);\\n  border-bottom: 1px solid var(--color-gray-100);\\n}\\n\\n.table tr:hover {\\n  background: var(--color-gray-50);\\n}\\n\\n/* 现代化表单 */\\n.form-group {\\n  margin-bottom: var(--spacing-6);\\n}\\n\\n.form-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--color-gray-700);\\n  margin-bottom: var(--spacing-2);\\n}\\n\\n.form-error {\\n  margin-top: var(--spacing-1);\\n  font-size: 0.75rem;\\n  color: var(--color-error-600);\\n}\\n\\n/* 现代化工具提示 */\\n.tooltip {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.tooltip::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: var(--color-gray-900);\\n  color: white;\\n  padding: var(--spacing-2) var(--spacing-3);\\n  border-radius: var(--radius-md);\\n  font-size: 0.75rem;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: opacity var(--transition-fast);\\n  z-index: 10;\\n}\\n\\n.tooltip:hover::after {\\n  opacity: 1;\\n}\\n\\n/* 图标尺寸类 */\\n.icon-xs {\\n  width: var(--icon-xs);\\n  height: var(--icon-xs);\\n}\\n\\n.icon-sm {\\n  width: var(--icon-sm);\\n  height: var(--icon-sm);\\n}\\n\\n.icon-base {\\n  width: var(--icon-base);\\n  height: var(--icon-base);\\n}\\n\\n.icon-lg {\\n  width: var(--icon-lg);\\n  height: var(--icon-lg);\\n}\\n\\n.icon-xl {\\n  width: var(--icon-xl);\\n  height: var(--icon-xl);\\n}\\n\\n/* 图标容器 */\\n.icon-container {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n\\n.icon-container-sm {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: var(--radius-md);\\n}\\n\\n.icon-container-md {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: var(--radius-lg);\\n}\\n\\n.icon-container-lg {\\n  width: 3rem;\\n  height: 3rem;\\n  border-radius: var(--radius-lg);\\n}\\n\\n/* Logo和品牌样式 */\\n.logo-container {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.logo-icon {\\n  width: 1rem;\\n  height: 1rem;\\n  color: white;\\n}\\n\\n/* 布局工具类 */\\n.flex {\\n  display: flex;\\n}\\n\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n\\n.items-center {\\n  align-items: center;\\n}\\n\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n\\n.font-bold {\\n  font-weight: 700;\\n}\\n\\n.text-gray-900 {\\n  color: var(--color-gray-900);\\n}\\n\\n.text-white {\\n  color: white;\\n}\\n\\n/* 链接样式 */\\n.link {\\n  text-decoration: none;\\n  color: inherit;\\n  transition: all var(--transition-fast);\\n}\\n\\n.link:hover {\\n  opacity: 0.8;\\n}\\n\\n/* 导航菜单样式 */\\n.nav-item {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1rem;\\n  margin: 0.25rem 0;\\n  border-radius: var(--radius-lg);\\n  text-decoration: none;\\n  color: var(--color-gray-600);\\n  font-weight: 500;\\n  transition: all var(--transition-fast);\\n}\\n\\n.nav-item:hover {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n.nav-item.active {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-700);\\n}\\n\\n.nav-item-icon {\\n  margin-right: 0.75rem;\\n  flex-shrink: 0;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: var(--color-gray-400);\\n  transition: color var(--transition-fast);\\n}\\n\\n.nav-item:hover .nav-item-icon {\\n  color: var(--color-gray-500);\\n}\\n\\n.nav-item.active .nav-item-icon {\\n  color: var(--color-primary-500);\\n}\\n\\n/* Header样式 */\\n.header {\\n  background: white;\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n\\n.header-content {\\n  padding: 0 1.5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  height: 4rem;\\n}\\n\\n.header-title {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: var(--color-gray-900);\\n}\\n\\n.header-actions {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.header-button {\\n  padding: 0.5rem;\\n  color: var(--color-gray-400);\\n  background: none;\\n  border: none;\\n  border-radius: var(--radius-md);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.header-button:hover {\\n  color: var(--color-gray-500);\\n  background: var(--color-gray-100);\\n}\\n\\n.header-icon {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n/* 移动端菜单 */\\n.mobile-menu-button {\\n  display: none;\\n  padding: 0.5rem;\\n  color: var(--color-gray-500);\\n  background: none;\\n  border: none;\\n  border-radius: var(--radius-md);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.mobile-menu-button:hover {\\n  color: var(--color-gray-600);\\n}\\n\\n.mobile-menu-icon {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n/* 响应式 */\\n@media (max-width: 1024px) {\\n  .mobile-menu-button {\\n    display: block;\\n  }\\n}\\n\\n/* 侧边栏样式 */\\n.sidebar {\\n  width: 16rem;\\n  background: white;\\n  border-right: 1px solid var(--color-gray-200);\\n  height: 100vh;\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  z-index: 40;\\n}\\n\\n.sidebar-header {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 4rem;\\n  padding: 0 1rem;\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n\\n.sidebar-nav {\\n  padding: 1rem;\\n  flex: 1 1;\\n  overflow-y: auto;\\n}\\n\\n.sidebar-close-button {\\n  display: none;\\n  padding: 0.5rem;\\n  color: var(--color-gray-500);\\n  background: none;\\n  border: none;\\n  border-radius: var(--radius-md);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.sidebar-close-button:hover {\\n  color: var(--color-gray-600);\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar-close-button {\\n    display: block;\\n  }\\n\\n  .sidebar {\\n    transform: translateX(-100%);\\n    transition: transform var(--transition-base);\\n  }\\n\\n  .sidebar.open {\\n    transform: translateX(0);\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/modern.css\"],\"names\":[],\"mappings\":\"AAAA,cAAc;;AAEd,YAAY;AACZ;EACE,SAAS;EACT,2BAA2B;EAC3B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;;EAE5B,wBAAwB;EACxB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;EACzB,yBAAyB;;EAEzB,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,4BAA4B;EAC5B,0BAA0B;EAC1B,0BAA0B;;EAE1B,SAAS;EACT,oBAAoB;EACpB,mBAAmB;EACnB,oBAAoB;EACpB,iBAAiB;EACjB,oBAAoB;EACpB,mBAAmB;EACnB,iBAAiB;EACjB,oBAAoB;EACpB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;;EAElB,SAAS;EACT,qBAAqB;EACrB,sBAAsB;EACtB,qBAAqB;EACrB,mBAAmB;EACnB,oBAAoB;EACpB,kBAAkB;EAClB,qBAAqB;;EAErB,SAAS;EACT,4CAA4C;EAC5C,8EAA8E;EAC9E,kFAAkF;EAClF,oFAAoF;EACpF,sFAAsF;;EAEtF,SAAS;EACT,iHAAiH;EACjH,kGAAkG;;EAElG,WAAW;EACX,kBAAkB;EAClB,eAAe;EACf,oBAAoB;EACpB,iBAAiB;EACjB,eAAe;;EAEf,SAAS;EACT,qDAAqD;EACrD,qDAAqD;EACrD,qDAAqD;AACvD;;AAEA,SAAS;AACT;;;EAGE,sBAAsB;EACtB,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,6BAA6B;EAC7B,gBAAgB;EAChB,8BAA8B;EAC9B,mCAAmC;EACnC,kCAAkC;EAClC,uBAAuB;AACzB;;AAEA;EACE,4BAA4B;EAC5B,sCAAsC;EACtC,iBAAiB;AACnB;;AAEA;EACE,iBAAiB;AACnB;;AAEA,YAAY;AACZ;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,qBAAqB;EACrB,0CAA0C;EAC1C,mBAAmB;EACnB,gBAAgB;EAChB,iBAAiB;EACjB,+BAA+B;EAC/B,6BAA6B;EAC7B,eAAe;EACf,sCAAsC;EACtC,qBAAqB;EACrB,aAAa;EACb,kBAAkB;EAClB,mBAAmB;EACnB,mBAAmB;EACnB,yBAAiB;KAAjB,sBAAiB;UAAjB,iBAAiB;AACnB;;AAEA;EACE,2CAA2C;EAC3C,mBAAmB;AACrB;;AAEA;EACE,YAAY;EACZ,mBAAmB;EACnB,oBAAoB;AACtB;;AAEA,SAAS;AACT;EACE,uFAAuF;EACvF,YAAY;EACZ,sCAAsC;EACtC,4BAA4B;AAC9B;;AAEA;EACE,uFAAuF;EACvF,4BAA4B;EAC5B,2BAA2B;AAC7B;;AAEA;EACE,iBAAiB;EACjB,4BAA4B;EAC5B,mCAAmC;EACnC,4BAA4B;AAC9B;;AAEA;EACE,gCAAgC;EAChC,mCAAmC;EACnC,4BAA4B;EAC5B,2BAA2B;AAC7B;;AAEA;EACE,uBAAuB;EACvB,4BAA4B;EAC5B,yBAAyB;AAC3B;;AAEA;EACE,iCAAiC;EACjC,4BAA4B;AAC9B;;AAEA,SAAS;AACT;EACE,0CAA0C;EAC1C,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,0CAA0C;EAC1C,eAAe;EACf,gBAAgB;AAClB;;AAEA,aAAa;AACb;EACE,kBAAkB;EAClB,WAAW;AACb;;AAEA;EACE,cAAc;EACd,WAAW;EACX,0CAA0C;EAC1C,mBAAmB;EACnB,gBAAgB;EAChB,4BAA4B;EAC5B,iBAAiB;EACjB,yCAAyC;EACzC,+BAA+B;EAC/B,sCAAsC;EACtC,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,sCAAsC;EACtC,6CAA6C;AAC/C;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,4CAA4C;AAC9C;;AAEA,WAAW;AACX;EACE,6BAA6B;EAC7B,gCAAgC;AAClC;;AAEA;EACE,kBAAkB;EAClB,MAAM;EACN,sBAAsB;EACtB,2BAA2B;EAC3B,kBAAkB;EAClB,gBAAgB;EAChB,4BAA4B;EAC5B,iBAAiB;EACjB,8BAA8B;EAC9B,sCAAsC;EACtC,oBAAoB;AACtB;;AAEA;;EAEE,8BAA8B;EAC9B,mBAAmB;EACnB,+BAA+B;AACjC;;AAEA,YAAY;AACZ;EACE,iBAAiB;EACjB,+BAA+B;EAC/B,8BAA8B;EAC9B,uCAAuC;EACvC,gBAAgB;EAChB,sCAAsC;AACxC;;AAEA;EACE,4BAA4B;EAC5B,2BAA2B;AAC7B;;AAEA;EACE,yBAAyB;EACzB,8CAA8C;EAC9C,gEAAgE;AAClE;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,2CAA2C;EAC3C,gCAAgC;AAClC;;AAEA,YAAY;AACZ;EACE,oBAAoB;EACpB,mBAAmB;EACnB,qBAAqB;EACrB,0CAA0C;EAC1C,kBAAkB;EAClB,gBAAgB;EAChB,iCAAiC;EACjC,mBAAmB;AACrB;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;AACjC;;AAEA;EACE,mBAAmB;EACnB,cAAc;AAChB;;AAEA;EACE,mBAAmB;EACnB,cAAc;AAChB;;AAEA;EACE,mBAAmB;EACnB,cAAc;AAChB;;AAEA,YAAY;AACZ;EACE,qBAAqB;EACrB,aAAa;EACb,cAAc;EACd,uCAAuC;EACvC,8CAA8C;EAC9C,kBAAkB;EAClB,kCAAkC;AACpC;;AAEA;EACE,KAAK,uBAAuB,EAAE;EAC9B,OAAO,yBAAyB,EAAE;AACpC;;AAEA,WAAW;AACX;EACE,eAAe;EACf,QAAQ;EACR,8BAA8B;EAC9B,kCAA0B;UAA1B,0BAA0B;EAC1B,WAAW;EACX,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,yBAAyB;AAC3B;;AAEA;EACE,iBAAiB;EACjB,gCAAgC;EAChC,4BAA4B;EAC5B,gBAAgB;EAChB,WAAW;EACX,gBAAgB;EAChB,gBAAgB;EAChB,sBAAsB;EACtB,sCAAsC;AACxC;;AAEA;EACE,mBAAmB;AACrB;;AAEA,UAAU;AACV;EACE,aAAa;EACb,mBAAmB;EACnB,qBAAqB;EACrB,0CAA0C;EAC1C,4BAA4B;EAC5B,qBAAqB;EACrB,+BAA+B;EAC/B,sCAAsC;EACtC,gBAAgB;AAClB;;AAEA;EACE,iCAAiC;EACjC,4BAA4B;AAC9B;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;AACjC;;AAEA,UAAU;AACV;EACE,WAAW;EACX,yBAAyB;EACzB,iBAAiB;EACjB,+BAA+B;EAC/B,gBAAgB;EAChB,8BAA8B;AAChC;;AAEA;EACE,gCAAgC;EAChC,yBAAyB;EACzB,gBAAgB;EAChB,gBAAgB;EAChB,4BAA4B;EAC5B,8CAA8C;AAChD;;AAEA;EACE,yBAAyB;EACzB,8CAA8C;AAChD;;AAEA;EACE,gCAAgC;AAClC;;AAEA,UAAU;AACV;EACE,+BAA+B;AACjC;;AAEA;EACE,cAAc;EACd,mBAAmB;EACnB,gBAAgB;EAChB,4BAA4B;EAC5B,+BAA+B;AACjC;;AAEA;EACE,4BAA4B;EAC5B,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA,YAAY;AACZ;EACE,kBAAkB;EAClB,qBAAqB;AACvB;;AAEA;EACE,2BAA2B;EAC3B,kBAAkB;EAClB,YAAY;EACZ,SAAS;EACT,2BAA2B;EAC3B,iCAAiC;EACjC,YAAY;EACZ,0CAA0C;EAC1C,+BAA+B;EAC/B,kBAAkB;EAClB,mBAAmB;EACnB,UAAU;EACV,oBAAoB;EACpB,0CAA0C;EAC1C,WAAW;AACb;;AAEA;EACE,UAAU;AACZ;;AAEA,UAAU;AACV;EACE,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA;EACE,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA;EACE,uBAAuB;EACvB,wBAAwB;AAC1B;;AAEA;EACE,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA;EACE,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA,SAAS;AACT;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,cAAc;AAChB;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,cAAc;EACd,+BAA+B;AACjC;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,+BAA+B;AACjC;;AAEA,cAAc;AACd;EACE,aAAa;EACb,cAAc;EACd,uFAAuF;EACvF,+BAA+B;EAC/B,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,4BAA4B;AAC9B;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,YAAY;AACd;;AAEA,UAAU;AACV;EACE,aAAa;AACf;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,kBAAkB;EAClB,oBAAoB;AACtB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,YAAY;AACd;;AAEA,SAAS;AACT;EACE,qBAAqB;EACrB,cAAc;EACd,sCAAsC;AACxC;;AAEA;EACE,YAAY;AACd;;AAEA,WAAW;AACX;EACE,aAAa;EACb,mBAAmB;EACnB,qBAAqB;EACrB,iBAAiB;EACjB,+BAA+B;EAC/B,qBAAqB;EACrB,4BAA4B;EAC5B,gBAAgB;EAChB,sCAAsC;AACxC;;AAEA;EACE,iCAAiC;EACjC,4BAA4B;AAC9B;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;AACjC;;AAEA;EACE,qBAAqB;EACrB,cAAc;EACd,cAAc;EACd,eAAe;EACf,4BAA4B;EAC5B,wCAAwC;AAC1C;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,+BAA+B;AACjC;;AAEA,aAAa;AACb;EACE,iBAAiB;EACjB,8CAA8C;AAChD;;AAEA;EACE,iBAAiB;EACjB,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,YAAY;AACd;;AAEA;EACE,iBAAiB;EACjB,gBAAgB;EAChB,4BAA4B;AAC9B;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,SAAS;AACX;;AAEA;EACE,eAAe;EACf,4BAA4B;EAC5B,gBAAgB;EAChB,YAAY;EACZ,+BAA+B;EAC/B,eAAe;EACf,sCAAsC;AACxC;;AAEA;EACE,4BAA4B;EAC5B,iCAAiC;AACnC;;AAEA;EACE,cAAc;EACd,eAAe;AACjB;;AAEA,UAAU;AACV;EACE,aAAa;EACb,eAAe;EACf,4BAA4B;EAC5B,gBAAgB;EAChB,YAAY;EACZ,+BAA+B;EAC/B,eAAe;EACf,sCAAsC;AACxC;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,cAAc;EACd,eAAe;AACjB;;AAEA,QAAQ;AACR;EACE;IACE,cAAc;EAChB;AACF;;AAEA,UAAU;AACV;EACE,YAAY;EACZ,iBAAiB;EACjB,6CAA6C;EAC7C,aAAa;EACb,eAAe;EACf,OAAO;EACP,MAAM;EACN,WAAW;AACb;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,YAAY;EACZ,eAAe;EACf,8CAA8C;AAChD;;AAEA;EACE,aAAa;EACb,SAAO;EACP,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,eAAe;EACf,4BAA4B;EAC5B,gBAAgB;EAChB,YAAY;EACZ,+BAA+B;EAC/B,eAAe;EACf,sCAAsC;AACxC;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE;IACE,cAAc;EAChB;;EAEA;IACE,4BAA4B;IAC5B,4CAA4C;EAC9C;;EAEA;IACE,wBAAwB;EAC1B;AACF\",\"sourcesContent\":[\"/* 现代化UI样式系统 */\\n\\n/* CSS变量定义 */\\n:root {\\n  /* 颜色系统 */\\n  --color-primary-50: #eff6ff;\\n  --color-primary-100: #dbeafe;\\n  --color-primary-200: #bfdbfe;\\n  --color-primary-300: #93c5fd;\\n  --color-primary-400: #60a5fa;\\n  --color-primary-500: #3b82f6;\\n  --color-primary-600: #2563eb;\\n  --color-primary-700: #1d4ed8;\\n  --color-primary-800: #1e40af;\\n  --color-primary-900: #1e3a8a;\\n\\n  --color-gray-50: #f9fafb;\\n  --color-gray-100: #f3f4f6;\\n  --color-gray-200: #e5e7eb;\\n  --color-gray-300: #d1d5db;\\n  --color-gray-400: #9ca3af;\\n  --color-gray-500: #6b7280;\\n  --color-gray-600: #4b5563;\\n  --color-gray-700: #374151;\\n  --color-gray-800: #1f2937;\\n  --color-gray-900: #111827;\\n\\n  --color-success-500: #10b981;\\n  --color-success-600: #059669;\\n  --color-warning-500: #f59e0b;\\n  --color-warning-600: #d97706;\\n  --color-error-500: #ef4444;\\n  --color-error-600: #dc2626;\\n\\n  /* 间距系统 */\\n  --spacing-1: 0.25rem;\\n  --spacing-2: 0.5rem;\\n  --spacing-3: 0.75rem;\\n  --spacing-4: 1rem;\\n  --spacing-5: 1.25rem;\\n  --spacing-6: 1.5rem;\\n  --spacing-8: 2rem;\\n  --spacing-10: 2.5rem;\\n  --spacing-12: 3rem;\\n  --spacing-16: 4rem;\\n  --spacing-20: 5rem;\\n  --spacing-24: 6rem;\\n\\n  /* 圆角系统 */\\n  --radius-sm: 0.125rem;\\n  --radius-base: 0.25rem;\\n  --radius-md: 0.375rem;\\n  --radius-lg: 0.5rem;\\n  --radius-xl: 0.75rem;\\n  --radius-2xl: 1rem;\\n  --radius-full: 9999px;\\n\\n  /* 阴影系统 */\\n  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n\\n  /* 字体系统 */\\n  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\\n  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;\\n\\n  /* 图标尺寸系统 */\\n  --icon-xs: 0.75rem;\\n  --icon-sm: 1rem;\\n  --icon-base: 1.25rem;\\n  --icon-lg: 1.5rem;\\n  --icon-xl: 2rem;\\n\\n  /* 过渡动画 */\\n  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);\\n  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);\\n  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n/* 基础重置 */\\n*,\\n*::before,\\n*::after {\\n  box-sizing: border-box;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nhtml {\\n  font-family: var(--font-sans);\\n  line-height: 1.6;\\n  -webkit-text-size-adjust: 100%;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  color: var(--color-gray-900);\\n  background-color: var(--color-gray-50);\\n  min-height: 100vh;\\n}\\n\\n#__next {\\n  min-height: 100vh;\\n}\\n\\n/* 现代化按钮组件 */\\n.btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--spacing-2);\\n  padding: var(--spacing-3) var(--spacing-6);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  line-height: 1.25;\\n  border-radius: var(--radius-lg);\\n  border: 1px solid transparent;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  text-decoration: none;\\n  outline: none;\\n  position: relative;\\n  min-height: 2.75rem;\\n  white-space: nowrap;\\n  user-select: none;\\n}\\n\\n.btn:focus-visible {\\n  outline: 2px solid var(--color-primary-500);\\n  outline-offset: 2px;\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n\\n/* 按钮变体 */\\n.btn-primary {\\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));\\n  color: white;\\n  border-color: var(--color-primary-500);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.btn-primary:hover:not(:disabled) {\\n  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-secondary {\\n  background: white;\\n  color: var(--color-gray-700);\\n  border-color: var(--color-gray-300);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.btn-secondary:hover:not(:disabled) {\\n  background: var(--color-gray-50);\\n  border-color: var(--color-gray-400);\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.btn-ghost {\\n  background: transparent;\\n  color: var(--color-gray-700);\\n  border-color: transparent;\\n}\\n\\n.btn-ghost:hover:not(:disabled) {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n/* 按钮尺寸 */\\n.btn-sm {\\n  padding: var(--spacing-2) var(--spacing-4);\\n  font-size: 0.75rem;\\n  min-height: 2rem;\\n}\\n\\n.btn-lg {\\n  padding: var(--spacing-4) var(--spacing-8);\\n  font-size: 1rem;\\n  min-height: 3rem;\\n}\\n\\n/* 现代化输入框组件 */\\n.input-group {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.input {\\n  display: block;\\n  width: 100%;\\n  padding: var(--spacing-3) var(--spacing-4);\\n  font-size: 0.875rem;\\n  line-height: 1.5;\\n  color: var(--color-gray-900);\\n  background: white;\\n  border: 1.5px solid var(--color-gray-300);\\n  border-radius: var(--radius-lg);\\n  transition: all var(--transition-fast);\\n  outline: none;\\n  min-height: 2.75rem;\\n}\\n\\n.input::placeholder {\\n  color: var(--color-gray-400);\\n}\\n\\n.input:focus {\\n  border-color: var(--color-primary-500);\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n\\n.input:invalid {\\n  border-color: var(--color-error-500);\\n}\\n\\n.input:invalid:focus {\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n\\n/* 浮动标签效果 */\\n.input-floating {\\n  padding-top: var(--spacing-6);\\n  padding-bottom: var(--spacing-2);\\n}\\n\\n.input-floating + .input-label {\\n  position: absolute;\\n  top: 0;\\n  left: var(--spacing-4);\\n  padding: 0 var(--spacing-1);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: var(--color-gray-600);\\n  background: white;\\n  transform: translateY(0.75rem);\\n  transition: all var(--transition-fast);\\n  pointer-events: none;\\n}\\n\\n.input-floating:focus + .input-label,\\n.input-floating:not(:placeholder-shown) + .input-label {\\n  transform: translateY(-0.5rem);\\n  font-size: 0.625rem;\\n  color: var(--color-primary-600);\\n}\\n\\n/* 现代化卡片组件 */\\n.card {\\n  background: white;\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-base);\\n  border: 1px solid var(--color-gray-200);\\n  overflow: hidden;\\n  transition: all var(--transition-base);\\n}\\n\\n.card:hover {\\n  box-shadow: var(--shadow-lg);\\n  transform: translateY(-2px);\\n}\\n\\n.card-header {\\n  padding: var(--spacing-6);\\n  border-bottom: 1px solid var(--color-gray-200);\\n  background: linear-gradient(135deg, var(--color-gray-50), white);\\n}\\n\\n.card-body {\\n  padding: var(--spacing-6);\\n}\\n\\n.card-footer {\\n  padding: var(--spacing-6);\\n  border-top: 1px solid var(--color-gray-200);\\n  background: var(--color-gray-50);\\n}\\n\\n/* 现代化徽章组件 */\\n.badge {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--spacing-1);\\n  padding: var(--spacing-1) var(--spacing-3);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border-radius: var(--radius-full);\\n  white-space: nowrap;\\n}\\n\\n.badge-primary {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-800);\\n}\\n\\n.badge-success {\\n  background: #d1fae5;\\n  color: #065f46;\\n}\\n\\n.badge-warning {\\n  background: #fef3c7;\\n  color: #92400e;\\n}\\n\\n.badge-error {\\n  background: #fee2e2;\\n  color: #991b1b;\\n}\\n\\n/* 现代化加载动画 */\\n.loading-spinner {\\n  display: inline-block;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  border: 2px solid var(--color-gray-200);\\n  border-top: 2px solid var(--color-primary-500);\\n  border-radius: 50%;\\n  animation: spin 1s linear infinite;\\n}\\n\\n@keyframes spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n/* 现代化模态框 */\\n.modal-overlay {\\n  position: fixed;\\n  inset: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  backdrop-filter: blur(4px);\\n  z-index: 50;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--spacing-4);\\n}\\n\\n.modal-content {\\n  background: white;\\n  border-radius: var(--radius-2xl);\\n  box-shadow: var(--shadow-xl);\\n  max-width: 32rem;\\n  width: 100%;\\n  max-height: 90vh;\\n  overflow-y: auto;\\n  transform: scale(0.95);\\n  transition: all var(--transition-base);\\n}\\n\\n.modal-content.show {\\n  transform: scale(1);\\n}\\n\\n/* 现代化导航 */\\n.nav-link {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--spacing-3);\\n  padding: var(--spacing-3) var(--spacing-4);\\n  color: var(--color-gray-700);\\n  text-decoration: none;\\n  border-radius: var(--radius-lg);\\n  transition: all var(--transition-fast);\\n  font-weight: 500;\\n}\\n\\n.nav-link:hover {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n.nav-link.active {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-700);\\n}\\n\\n/* 现代化表格 */\\n.table {\\n  width: 100%;\\n  border-collapse: collapse;\\n  background: white;\\n  border-radius: var(--radius-lg);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-base);\\n}\\n\\n.table th {\\n  background: var(--color-gray-50);\\n  padding: var(--spacing-4);\\n  text-align: left;\\n  font-weight: 600;\\n  color: var(--color-gray-700);\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n\\n.table td {\\n  padding: var(--spacing-4);\\n  border-bottom: 1px solid var(--color-gray-100);\\n}\\n\\n.table tr:hover {\\n  background: var(--color-gray-50);\\n}\\n\\n/* 现代化表单 */\\n.form-group {\\n  margin-bottom: var(--spacing-6);\\n}\\n\\n.form-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--color-gray-700);\\n  margin-bottom: var(--spacing-2);\\n}\\n\\n.form-error {\\n  margin-top: var(--spacing-1);\\n  font-size: 0.75rem;\\n  color: var(--color-error-600);\\n}\\n\\n/* 现代化工具提示 */\\n.tooltip {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.tooltip::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: var(--color-gray-900);\\n  color: white;\\n  padding: var(--spacing-2) var(--spacing-3);\\n  border-radius: var(--radius-md);\\n  font-size: 0.75rem;\\n  white-space: nowrap;\\n  opacity: 0;\\n  pointer-events: none;\\n  transition: opacity var(--transition-fast);\\n  z-index: 10;\\n}\\n\\n.tooltip:hover::after {\\n  opacity: 1;\\n}\\n\\n/* 图标尺寸类 */\\n.icon-xs {\\n  width: var(--icon-xs);\\n  height: var(--icon-xs);\\n}\\n\\n.icon-sm {\\n  width: var(--icon-sm);\\n  height: var(--icon-sm);\\n}\\n\\n.icon-base {\\n  width: var(--icon-base);\\n  height: var(--icon-base);\\n}\\n\\n.icon-lg {\\n  width: var(--icon-lg);\\n  height: var(--icon-lg);\\n}\\n\\n.icon-xl {\\n  width: var(--icon-xl);\\n  height: var(--icon-xl);\\n}\\n\\n/* 图标容器 */\\n.icon-container {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n\\n.icon-container-sm {\\n  width: 2rem;\\n  height: 2rem;\\n  border-radius: var(--radius-md);\\n}\\n\\n.icon-container-md {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: var(--radius-lg);\\n}\\n\\n.icon-container-lg {\\n  width: 3rem;\\n  height: 3rem;\\n  border-radius: var(--radius-lg);\\n}\\n\\n/* Logo和品牌样式 */\\n.logo-container {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.logo-icon {\\n  width: 1rem;\\n  height: 1rem;\\n  color: white;\\n}\\n\\n/* 布局工具类 */\\n.flex {\\n  display: flex;\\n}\\n\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n\\n.items-center {\\n  align-items: center;\\n}\\n\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n\\n.font-bold {\\n  font-weight: 700;\\n}\\n\\n.text-gray-900 {\\n  color: var(--color-gray-900);\\n}\\n\\n.text-white {\\n  color: white;\\n}\\n\\n/* 链接样式 */\\n.link {\\n  text-decoration: none;\\n  color: inherit;\\n  transition: all var(--transition-fast);\\n}\\n\\n.link:hover {\\n  opacity: 0.8;\\n}\\n\\n/* 导航菜单样式 */\\n.nav-item {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.75rem 1rem;\\n  margin: 0.25rem 0;\\n  border-radius: var(--radius-lg);\\n  text-decoration: none;\\n  color: var(--color-gray-600);\\n  font-weight: 500;\\n  transition: all var(--transition-fast);\\n}\\n\\n.nav-item:hover {\\n  background: var(--color-gray-100);\\n  color: var(--color-gray-900);\\n}\\n\\n.nav-item.active {\\n  background: var(--color-primary-100);\\n  color: var(--color-primary-700);\\n}\\n\\n.nav-item-icon {\\n  margin-right: 0.75rem;\\n  flex-shrink: 0;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: var(--color-gray-400);\\n  transition: color var(--transition-fast);\\n}\\n\\n.nav-item:hover .nav-item-icon {\\n  color: var(--color-gray-500);\\n}\\n\\n.nav-item.active .nav-item-icon {\\n  color: var(--color-primary-500);\\n}\\n\\n/* Header样式 */\\n.header {\\n  background: white;\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n\\n.header-content {\\n  padding: 0 1.5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  height: 4rem;\\n}\\n\\n.header-title {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: var(--color-gray-900);\\n}\\n\\n.header-actions {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.header-button {\\n  padding: 0.5rem;\\n  color: var(--color-gray-400);\\n  background: none;\\n  border: none;\\n  border-radius: var(--radius-md);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.header-button:hover {\\n  color: var(--color-gray-500);\\n  background: var(--color-gray-100);\\n}\\n\\n.header-icon {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n/* 移动端菜单 */\\n.mobile-menu-button {\\n  display: none;\\n  padding: 0.5rem;\\n  color: var(--color-gray-500);\\n  background: none;\\n  border: none;\\n  border-radius: var(--radius-md);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.mobile-menu-button:hover {\\n  color: var(--color-gray-600);\\n}\\n\\n.mobile-menu-icon {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n/* 响应式 */\\n@media (max-width: 1024px) {\\n  .mobile-menu-button {\\n    display: block;\\n  }\\n}\\n\\n/* 侧边栏样式 */\\n.sidebar {\\n  width: 16rem;\\n  background: white;\\n  border-right: 1px solid var(--color-gray-200);\\n  height: 100vh;\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  z-index: 40;\\n}\\n\\n.sidebar-header {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 4rem;\\n  padding: 0 1rem;\\n  border-bottom: 1px solid var(--color-gray-200);\\n}\\n\\n.sidebar-nav {\\n  padding: 1rem;\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n\\n.sidebar-close-button {\\n  display: none;\\n  padding: 0.5rem;\\n  color: var(--color-gray-500);\\n  background: none;\\n  border: none;\\n  border-radius: var(--radius-md);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.sidebar-close-button:hover {\\n  color: var(--color-gray-600);\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar-close-button {\\n    display: block;\\n  }\\n\\n  .sidebar {\\n    transform: translateX(-100%);\\n    transition: transform var(--transition-base);\\n  }\\n\\n  .sidebar.open {\\n    transform: translateX(0);\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/modern.css\n"));

/***/ })

});