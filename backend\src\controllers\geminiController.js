const geminiService = require('../services/geminiService');
const { createError } = require('../middleware/errorHandler');
const { db } = require('../config/database');
const logger = require('../utils/logger');
const crypto = require('crypto');

/**
 * 加密API密钥
 */
const encryptApiKey = (apiKey) => {
  const algorithm = 'aes-256-gcm';
  const secretKey = process.env.ENCRYPTION_KEY || 'your-secret-key-change-this';
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipher(algorithm, secretKey);
  let encrypted = cipher.update(apiKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return {
    encrypted,
    iv: iv.toString('hex')
  };
};

/**
 * 解密API密钥
 */
const decryptApiKey = (encryptedData, iv) => {
  const algorithm = 'aes-256-gcm';
  const secretKey = process.env.ENCRYPTION_KEY || 'your-secret-key-change-this';
  
  const decipher = crypto.createDecipher(algorithm, secretKey);
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};

/**
 * 获取用户的Gemini API密钥
 */
const getUserApiKey = async (userId, providedApiKey = null) => {
  if (providedApiKey) {
    return providedApiKey;
  }
  
  const user = await db('users').where({ id: userId }).first();
  if (!user || !user.gemini_api_key_encrypted) {
    throw createError.badRequest('请先配置Gemini API密钥', 'API_KEY_NOT_CONFIGURED');
  }
  
  try {
    return decryptApiKey(user.gemini_api_key_encrypted, user.gemini_api_key_iv);
  } catch (error) {
    logger.error('解密API密钥失败:', error);
    throw createError.internal('API密钥解密失败', 'DECRYPTION_ERROR');
  }
};

const geminiController = {
  /**
   * 测试Gemini API连接
   */
  async testConnection(req, res) {
    try {
      const { apiKey } = req.body;
      const userId = req.user.id;
      
      const keyToTest = await getUserApiKey(userId, apiKey);
      const result = await geminiService.testConnection(keyToTest);
      
      logger.logUserActivity(userId, 'gemini_test_connection', { success: true });
      
      res.json({
        success: true,
        message: '连接测试成功',
        data: result
      });
    } catch (error) {
      logger.logUserActivity(req.user.id, 'gemini_test_connection', { 
        success: false, 
        error: error.message 
      });
      throw error;
    }
  },

  /**
   * 分析产品
   */
  async analyzeProduct(req, res) {
    try {
      const { productData, analysisType, platform } = req.body;
      const userId = req.user.id;
      
      const apiKey = await getUserApiKey(userId);
      
      // 创建分析任务记录
      const [analysisId] = await db('analysis_results').insert({
        user_id: userId,
        title: `产品分析 - ${productData.title}`,
        type: 'product',
        status: 'running',
        config: JSON.stringify({
          analysisType,
          platform,
          productData
        }),
        input_data: JSON.stringify(productData),
        gemini_model: 'gemini-pro',
        started_at: new Date()
      }).returning('id');
      
      // 异步执行分析
      setImmediate(async () => {
        try {
          const result = await geminiService.analyzeProduct(apiKey, productData, analysisType);
          
          await db('analysis_results')
            .where({ id: analysisId })
            .update({
              status: 'completed',
              result: JSON.stringify(result),
              completed_at: new Date(),
              duration_ms: Date.now() - new Date().getTime()
            });
          
          // 通过WebSocket通知前端
          const io = req.app.get('io');
          io.to(`analysis_${analysisId}`).emit('analysis_complete', {
            analysisId,
            result
          });
          
          logger.logUserActivity(userId, 'product_analysis_complete', { analysisId });
        } catch (error) {
          await db('analysis_results')
            .where({ id: analysisId })
            .update({
              status: 'failed',
              error_message: error.message,
              completed_at: new Date()
            });
          
          const io = req.app.get('io');
          io.to(`analysis_${analysisId}`).emit('analysis_error', {
            analysisId,
            error: error.message
          });
          
          logger.error('产品分析失败:', error);
        }
      });
      
      res.json({
        success: true,
        message: '分析任务已启动',
        data: {
          analysisId,
          status: 'running'
        }
      });
    } catch (error) {
      logger.error('启动产品分析失败:', error);
      throw error;
    }
  },

  /**
   * 分析市场趋势
   */
  async analyzeMarket(req, res) {
    try {
      const { category, keywords, timeRange } = req.body;
      const userId = req.user.id;
      
      const apiKey = await getUserApiKey(userId);
      const result = await geminiService.analyzeMarket(apiKey, category, keywords, timeRange);
      
      // 保存分析结果
      const [analysisId] = await db('analysis_results').insert({
        user_id: userId,
        title: `市场分析 - ${category}`,
        type: 'market',
        status: 'completed',
        config: JSON.stringify({ category, keywords, timeRange }),
        result: JSON.stringify(result),
        gemini_model: 'gemini-pro',
        started_at: new Date(),
        completed_at: new Date(),
        duration_ms: result.metadata.duration
      }).returning('id');
      
      logger.logUserActivity(userId, 'market_analysis', { analysisId, category });
      
      res.json({
        success: true,
        message: '市场分析完成',
        data: {
          analysisId,
          result
        }
      });
    } catch (error) {
      logger.error('市场分析失败:', error);
      throw error;
    }
  },

  /**
   * 比较产品
   */
  async compareProducts(req, res) {
    try {
      const { products, compareFields } = req.body;
      const userId = req.user.id;
      
      const apiKey = await getUserApiKey(userId);
      const result = await geminiService.compareProducts(apiKey, products, compareFields);
      
      // 保存比较结果
      const [analysisId] = await db('analysis_results').insert({
        user_id: userId,
        title: `产品比较 - ${products.length}个产品`,
        type: 'competitor',
        status: 'completed',
        config: JSON.stringify({ compareFields, productCount: products.length }),
        input_data: JSON.stringify(products),
        result: JSON.stringify(result),
        gemini_model: 'gemini-pro',
        started_at: new Date(),
        completed_at: new Date(),
        duration_ms: result.metadata.duration
      }).returning('id');
      
      logger.logUserActivity(userId, 'product_comparison', { analysisId, productCount: products.length });
      
      res.json({
        success: true,
        message: '产品比较完成',
        data: {
          analysisId,
          result
        }
      });
    } catch (error) {
      logger.error('产品比较失败:', error);
      throw error;
    }
  },

  /**
   * 预测价格趋势
   */
  async predictTrend(req, res) {
    try {
      const { productId, historicalData, predictionDays } = req.body;
      const userId = req.user.id;
      
      const apiKey = await getUserApiKey(userId);
      const result = await geminiService.predictTrend(apiKey, productId, historicalData, predictionDays);
      
      // 保存预测结果
      const [analysisId] = await db('analysis_results').insert({
        user_id: userId,
        product_id: productId,
        title: `价格趋势预测 - ${predictionDays}天`,
        type: 'trend',
        status: 'completed',
        config: JSON.stringify({ predictionDays, dataPoints: historicalData.length }),
        input_data: JSON.stringify(historicalData),
        result: JSON.stringify(result),
        gemini_model: 'gemini-pro',
        started_at: new Date(),
        completed_at: new Date(),
        duration_ms: result.metadata.duration
      }).returning('id');
      
      logger.logUserActivity(userId, 'trend_prediction', { analysisId, productId });
      
      res.json({
        success: true,
        message: '趋势预测完成',
        data: {
          analysisId,
          result
        }
      });
    } catch (error) {
      logger.error('趋势预测失败:', error);
      throw error;
    }
  },

  /**
   * 生成分析报告
   */
  async generateReport(req, res) {
    try {
      const { analysisId, reportType, includeCharts } = req.body;
      const userId = req.user.id;

      // 获取分析结果
      const analysis = await db('analysis_results')
        .where({ id: analysisId, user_id: userId })
        .first();

      if (!analysis) {
        throw createError.notFound('分析结果不存在', 'ANALYSIS_NOT_FOUND');
      }

      // 这里可以实现报告生成逻辑
      // 暂时返回基础报告结构
      const report = {
        id: analysisId,
        type: reportType,
        title: analysis.title,
        createdAt: new Date().toISOString(),
        analysis: JSON.parse(analysis.result || '{}'),
        includeCharts
      };

      logger.logUserActivity(userId, 'generate_report', { analysisId, reportType });

      res.json({
        success: true,
        message: '报告生成完成',
        data: report
      });
    } catch (error) {
      logger.error('生成报告失败:', error);
      throw error;
    }
  },

  /**
   * 优化商品标题和描述
   */
  async optimizeListing(req, res) {
    try {
      const { title, description, category, targetKeywords } = req.body;
      const userId = req.user.id;

      const apiKey = await getUserApiKey(userId);
      const result = await geminiService.optimizeListing(apiKey, title, description, category, targetKeywords);

      logger.logUserActivity(userId, 'optimize_listing', { category, keywordCount: targetKeywords.length });

      res.json({
        success: true,
        message: '商品信息优化完成',
        data: result
      });
    } catch (error) {
      logger.error('商品信息优化失败:', error);
      throw error;
    }
  },

  /**
   * 分析竞争对手
   */
  async analyzeCompetitor(req, res) {
    try {
      const { competitorUrl, analysisDepth } = req.body;
      const userId = req.user.id;

      const apiKey = await getUserApiKey(userId);
      const result = await geminiService.analyzeCompetitor(apiKey, competitorUrl, analysisDepth);

      // 保存分析结果
      const [analysisId] = await db('analysis_results').insert({
        user_id: userId,
        title: `竞争对手分析 - ${competitorUrl}`,
        type: 'competitor',
        status: 'completed',
        config: JSON.stringify({ competitorUrl, analysisDepth }),
        result: JSON.stringify(result),
        gemini_model: 'gemini-pro',
        started_at: new Date(),
        completed_at: new Date(),
        duration_ms: result.metadata.duration
      }).returning('id');

      logger.logUserActivity(userId, 'competitor_analysis', { analysisId, competitorUrl });

      res.json({
        success: true,
        message: '竞争对手分析完成',
        data: {
          analysisId,
          result
        }
      });
    } catch (error) {
      logger.error('竞争对手分析失败:', error);
      throw error;
    }
  },

  /**
   * 获取可用的Gemini模型列表
   */
  async getAvailableModels(req, res) {
    try {
      const userId = req.user.id;
      const apiKey = await getUserApiKey(userId);

      const models = await geminiService.getAvailableModels(apiKey);

      res.json({
        success: true,
        message: '获取模型列表成功',
        data: models
      });
    } catch (error) {
      logger.error('获取模型列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取API使用统计
   */
  async getUsageStats(req, res) {
    try {
      const userId = req.user.id;
      const { period = '30d' } = req.query;

      const stats = await geminiService.getUsageStats(userId, period);

      // 从数据库获取实际统计数据
      const dbStats = await db('analysis_results')
        .where({ user_id: userId })
        .where('created_at', '>=', db.raw(`NOW() - INTERVAL '${period.replace('d', ' days')}'`))
        .select(
          db.raw('COUNT(*) as total_requests'),
          db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as successful_requests'),
          db.raw('AVG(duration_ms) as avg_duration'),
          db.raw('SUM(CASE WHEN status = \'completed\' THEN 1 ELSE 0 END)::float / COUNT(*) * 100 as success_rate')
        )
        .first();

      const combinedStats = {
        ...stats,
        totalRequests: parseInt(dbStats.total_requests) || 0,
        successfulRequests: parseInt(dbStats.successful_requests) || 0,
        averageResponseTime: Math.round(parseFloat(dbStats.avg_duration) || 0),
        successRate: Math.round(parseFloat(dbStats.success_rate) || 100)
      };

      res.json({
        success: true,
        message: '获取使用统计成功',
        data: combinedStats
      });
    } catch (error) {
      logger.error('获取使用统计失败:', error);
      throw error;
    }
  }
};

module.exports = geminiController;
