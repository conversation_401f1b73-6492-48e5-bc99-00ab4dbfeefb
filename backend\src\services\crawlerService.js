const puppeteer = require('puppeteer');
const cheerio = require('cheerio');
const axios = require('axios');
const { createError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { cache } = require('../config/redis');

class CrawlerService {
  constructor() {
    this.browser = null;
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ];
    this.config = {
      delayMin: parseInt(process.env.CRAWLER_DELAY_MIN) || 1000,
      delayMax: parseInt(process.env.CRAWLER_DELAY_MAX) || 3000,
      concurrentLimit: parseInt(process.env.CRAWLER_CONCURRENT_LIMIT) || 5,
      retryAttempts: parseInt(process.env.CRAWLER_RETRY_ATTEMPTS) || 3,
      timeout: 30000
    };
  }

  /**
   * 初始化浏览器
   */
  async initBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ],
        executablePath: process.env.PUPPETEER_EXECUTABLE_PATH
      });
      
      logger.info('浏览器初始化成功');
    }
    return this.browser;
  }

  /**
   * 关闭浏览器
   */
  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      logger.info('浏览器已关闭');
    }
  }

  /**
   * 获取随机用户代理
   */
  getRandomUserAgent() {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  /**
   * 随机延迟
   */
  async randomDelay() {
    const delay = Math.floor(Math.random() * (this.config.delayMax - this.config.delayMin + 1)) + this.config.delayMin;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * 创建新页面
   */
  async createPage() {
    const browser = await this.initBrowser();
    const page = await browser.newPage();
    
    // 设置用户代理
    await page.setUserAgent(this.getRandomUserAgent());
    
    // 设置视口
    await page.setViewport({ width: 1366, height: 768 });
    
    // 设置请求拦截
    await page.setRequestInterception(true);
    page.on('request', (request) => {
      const resourceType = request.resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
        request.abort();
      } else {
        request.continue();
      }
    });
    
    return page;
  }

  /**
   * 爬取单个产品信息
   */
  async crawlProduct(url, platform, options = {}) {
    const cacheKey = `product:${platform}:${Buffer.from(url).toString('base64')}`;
    
    // 检查缓存
    if (!options.forceRefresh) {
      const cached = await cache.get(cacheKey);
      if (cached) {
        logger.info('从缓存获取产品信息:', url);
        return cached;
      }
    }

    let page = null;
    try {
      page = await this.createPage();
      
      // 根据平台选择爬取策略
      let productData;
      switch (platform) {
        case 'taobao':
          productData = await this.crawlTaobaoProduct(page, url);
          break;
        case 'tmall':
          productData = await this.crawlTmallProduct(page, url);
          break;
        case 'jd':
          productData = await this.crawlJDProduct(page, url);
          break;
        case 'pdd':
          productData = await this.crawlPDDProduct(page, url);
          break;
        default:
          throw createError.badRequest('不支持的平台', 'UNSUPPORTED_PLATFORM');
      }
      
      // 数据标准化
      const standardizedData = this.standardizeProductData(productData, platform);
      
      // 缓存结果（缓存1小时）
      await cache.set(cacheKey, standardizedData, 3600);
      
      logger.logCrawler(platform, 'crawl_product', { url, success: true });
      
      return standardizedData;
    } catch (error) {
      logger.error(`爬取产品失败 [${platform}]:`, error);
      logger.logCrawler(platform, 'crawl_product', { url, success: false, error: error.message });
      throw error;
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  /**
   * 爬取淘宝产品
   */
  async crawlTaobaoProduct(page, url) {
    await page.goto(url, { waitUntil: 'networkidle2', timeout: this.config.timeout });
    await this.randomDelay();

    const productData = await page.evaluate(() => {
      const title = document.querySelector('.tb-main-title')?.textContent?.trim() || '';
      const price = document.querySelector('.tb-rmb-num')?.textContent?.trim() || '';
      const originalPrice = document.querySelector('.tb-original-price .tb-rmb-num')?.textContent?.trim() || '';
      const sales = document.querySelector('.tb-count')?.textContent?.trim() || '';
      const rating = document.querySelector('.tb-rate-score')?.textContent?.trim() || '';
      const reviewCount = document.querySelector('.tb-rate-counter')?.textContent?.trim() || '';
      const shopName = document.querySelector('.tb-shop-name')?.textContent?.trim() || '';
      const images = Array.from(document.querySelectorAll('.tb-thumb img')).map(img => img.src);
      
      return {
        title,
        price: parseFloat(price) || 0,
        originalPrice: parseFloat(originalPrice) || 0,
        sales: parseInt(sales.replace(/[^\d]/g, '')) || 0,
        rating: parseFloat(rating) || 0,
        reviewCount: parseInt(reviewCount.replace(/[^\d]/g, '')) || 0,
        shopName,
        images,
        description: '',
        attributes: {}
      };
    });

    return productData;
  }

  /**
   * 爬取天猫产品
   */
  async crawlTmallProduct(page, url) {
    await page.goto(url, { waitUntil: 'networkidle2', timeout: this.config.timeout });
    await this.randomDelay();

    const productData = await page.evaluate(() => {
      const title = document.querySelector('.tb-detail-hd h1')?.textContent?.trim() || '';
      const price = document.querySelector('.tm-price-current')?.textContent?.trim() || '';
      const originalPrice = document.querySelector('.tm-price-original')?.textContent?.trim() || '';
      const sales = document.querySelector('.tm-ind-sellCount .tm-count')?.textContent?.trim() || '';
      const rating = document.querySelector('.tm-rate-score')?.textContent?.trim() || '';
      const reviewCount = document.querySelector('.tm-rate-counter')?.textContent?.trim() || '';
      const shopName = document.querySelector('.tm-shop-name')?.textContent?.trim() || '';
      const images = Array.from(document.querySelectorAll('.tm-img img')).map(img => img.src);
      
      return {
        title,
        price: parseFloat(price.replace(/[^\d.]/g, '')) || 0,
        originalPrice: parseFloat(originalPrice.replace(/[^\d.]/g, '')) || 0,
        sales: parseInt(sales.replace(/[^\d]/g, '')) || 0,
        rating: parseFloat(rating) || 0,
        reviewCount: parseInt(reviewCount.replace(/[^\d]/g, '')) || 0,
        shopName,
        images,
        description: '',
        attributes: {}
      };
    });

    return productData;
  }

  /**
   * 爬取京东产品
   */
  async crawlJDProduct(page, url) {
    await page.goto(url, { waitUntil: 'networkidle2', timeout: this.config.timeout });
    await this.randomDelay();

    const productData = await page.evaluate(() => {
      const title = document.querySelector('.sku-name')?.textContent?.trim() || '';
      const price = document.querySelector('.price')?.textContent?.trim() || '';
      const originalPrice = document.querySelector('.p-price .price')?.textContent?.trim() || '';
      const sales = document.querySelector('.p-commit strong')?.textContent?.trim() || '';
      const rating = document.querySelector('.percent-con')?.textContent?.trim() || '';
      const reviewCount = document.querySelector('.p-commit a')?.textContent?.trim() || '';
      const shopName = document.querySelector('.J-hove-wrap .name a')?.textContent?.trim() || '';
      const images = Array.from(document.querySelectorAll('#spec-list img')).map(img => img.src);
      
      return {
        title,
        price: parseFloat(price.replace(/[^\d.]/g, '')) || 0,
        originalPrice: parseFloat(originalPrice.replace(/[^\d.]/g, '')) || 0,
        sales: parseInt(sales.replace(/[^\d]/g, '')) || 0,
        rating: parseFloat(rating.replace(/[^\d.]/g, '')) || 0,
        reviewCount: parseInt(reviewCount.replace(/[^\d]/g, '')) || 0,
        shopName,
        images,
        description: '',
        attributes: {}
      };
    });

    return productData;
  }

  /**
   * 爬取拼多多产品
   */
  async crawlPDDProduct(page, url) {
    await page.goto(url, { waitUntil: 'networkidle2', timeout: this.config.timeout });
    await this.randomDelay();

    const productData = await page.evaluate(() => {
      const title = document.querySelector('.goods-name')?.textContent?.trim() || '';
      const price = document.querySelector('.goods-price')?.textContent?.trim() || '';
      const originalPrice = document.querySelector('.market-price')?.textContent?.trim() || '';
      const sales = document.querySelector('.goods-sales')?.textContent?.trim() || '';
      const rating = '0'; // 拼多多通常不显示评分
      const reviewCount = document.querySelector('.review-num')?.textContent?.trim() || '';
      const shopName = document.querySelector('.store-name')?.textContent?.trim() || '';
      const images = Array.from(document.querySelectorAll('.goods-image img')).map(img => img.src);
      
      return {
        title,
        price: parseFloat(price.replace(/[^\d.]/g, '')) || 0,
        originalPrice: parseFloat(originalPrice.replace(/[^\d.]/g, '')) || 0,
        sales: parseInt(sales.replace(/[^\d]/g, '')) || 0,
        rating: parseFloat(rating) || 0,
        reviewCount: parseInt(reviewCount.replace(/[^\d]/g, '')) || 0,
        shopName,
        images,
        description: '',
        attributes: {}
      };
    });

    return productData;
  }
  /**
   * 数据标准化
   */
  standardizeProductData(rawData, platform) {
    const standardized = {
      title: rawData.title || '',
      description: rawData.description || '',
      currentPrice: rawData.price || 0,
      originalPrice: rawData.originalPrice || rawData.price || 0,
      salesCount: rawData.sales || 0,
      rating: rawData.rating || 0,
      reviewCount: rawData.reviewCount || 0,
      shopName: rawData.shopName || '',
      images: rawData.images || [],
      platform: platform,
      attributes: rawData.attributes || {},
      crawledAt: new Date().toISOString(),
      currency: 'CNY',
      inStock: true,
      category: this.extractCategory(rawData.title, platform),
      brand: this.extractBrand(rawData.title, rawData.attributes)
    };

    // 计算折扣信息
    if (standardized.originalPrice > standardized.currentPrice) {
      standardized.discountAmount = standardized.originalPrice - standardized.currentPrice;
      standardized.discountPercentage = Math.round((standardized.discountAmount / standardized.originalPrice) * 100);
      standardized.isPromotion = true;
    } else {
      standardized.discountAmount = 0;
      standardized.discountPercentage = 0;
      standardized.isPromotion = false;
    }

    return standardized;
  }

  /**
   * 提取商品类别
   */
  extractCategory(title, platform) {
    const categoryKeywords = {
      '手机': ['手机', 'iPhone', '华为', '小米', '三星', 'OPPO', 'vivo'],
      '电脑': ['笔记本', '台式机', '电脑', 'MacBook', 'ThinkPad'],
      '服装': ['T恤', '衬衫', '裤子', '裙子', '外套', '鞋子'],
      '家电': ['冰箱', '洗衣机', '空调', '电视', '微波炉'],
      '美妆': ['口红', '粉底', '面膜', '护肤', '化妆品'],
      '食品': ['零食', '饮料', '茶叶', '咖啡', '保健品']
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some(keyword => title.includes(keyword))) {
        return category;
      }
    }

    return '其他';
  }

  /**
   * 提取品牌信息
   */
  extractBrand(title, attributes) {
    const brands = [
      'Apple', 'iPhone', '华为', 'HUAWEI', '小米', 'Xiaomi', '三星', 'Samsung',
      'OPPO', 'vivo', '一加', 'OnePlus', '魅族', 'Meizu', 'realme',
      'Nike', 'Adidas', '李宁', '安踏', 'ANTA', '特步', 'XTEP',
      '海尔', 'Haier', '美的', 'Midea', '格力', 'GREE', 'TCL'
    ];

    // 从标题中提取品牌
    for (const brand of brands) {
      if (title.includes(brand)) {
        return brand;
      }
    }

    // 从属性中提取品牌
    if (attributes && attributes.brand) {
      return attributes.brand;
    }

    return '';
  }

  /**
   * 爬取搜索结果
   */
  async crawlSearch(keyword, platform, options = {}) {
    const { maxPages = 3, maxItems = 50 } = options;
    let page = null;

    try {
      page = await this.createPage();
      let allProducts = [];

      for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
        logger.info(`爬取搜索结果第${pageNum}页: ${keyword} [${platform}]`);

        let products;
        switch (platform) {
          case 'taobao':
            products = await this.crawlTaobaoSearch(page, keyword, pageNum);
            break;
          case 'tmall':
            products = await this.crawlTmallSearch(page, keyword, pageNum);
            break;
          case 'jd':
            products = await this.crawlJDSearch(page, keyword, pageNum);
            break;
          case 'pdd':
            products = await this.crawlPDDSearch(page, keyword, pageNum);
            break;
          default:
            throw createError.badRequest('不支持的平台', 'UNSUPPORTED_PLATFORM');
        }

        allProducts = allProducts.concat(products);

        if (allProducts.length >= maxItems) {
          allProducts = allProducts.slice(0, maxItems);
          break;
        }

        await this.randomDelay();
      }

      // 数据标准化
      const standardizedProducts = allProducts.map(product =>
        this.standardizeProductData(product, platform)
      );

      logger.logCrawler(platform, 'crawl_search', {
        keyword,
        pages: Math.min(pageNum, maxPages),
        products: standardizedProducts.length,
        success: true
      });

      return standardizedProducts;
    } catch (error) {
      logger.error(`搜索爬取失败 [${platform}]:`, error);
      logger.logCrawler(platform, 'crawl_search', {
        keyword,
        success: false,
        error: error.message
      });
      throw error;
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  /**
   * 爬取淘宝搜索结果
   */
  async crawlTaobaoSearch(page, keyword, pageNum) {
    const searchUrl = `https://s.taobao.com/search?q=${encodeURIComponent(keyword)}&s=${(pageNum - 1) * 44}`;
    await page.goto(searchUrl, { waitUntil: 'networkidle2', timeout: this.config.timeout });
    await this.randomDelay();

    const products = await page.evaluate(() => {
      const items = document.querySelectorAll('.item');
      return Array.from(items).map(item => {
        const title = item.querySelector('.title a')?.textContent?.trim() || '';
        const price = item.querySelector('.price strong')?.textContent?.trim() || '';
        const sales = item.querySelector('.deal-cnt')?.textContent?.trim() || '';
        const shopName = item.querySelector('.shop a')?.textContent?.trim() || '';
        const url = item.querySelector('.title a')?.href || '';
        const image = item.querySelector('.pic img')?.src || '';

        return {
          title,
          price: parseFloat(price) || 0,
          sales: parseInt(sales.replace(/[^\d]/g, '')) || 0,
          shopName,
          url,
          images: [image],
          rating: 0,
          reviewCount: 0
        };
      });
    });

    return products;
  }

  /**
   * 爬取天猫搜索结果
   */
  async crawlTmallSearch(page, keyword, pageNum) {
    const searchUrl = `https://list.tmall.com/search_product.htm?q=${encodeURIComponent(keyword)}&s=${(pageNum - 1) * 60}`;
    await page.goto(searchUrl, { waitUntil: 'networkidle2', timeout: this.config.timeout });
    await this.randomDelay();

    const products = await page.evaluate(() => {
      const items = document.querySelectorAll('.product');
      return Array.from(items).map(item => {
        const title = item.querySelector('.productTitle a')?.textContent?.trim() || '';
        const price = item.querySelector('.productPrice em')?.textContent?.trim() || '';
        const sales = item.querySelector('.productStatus span')?.textContent?.trim() || '';
        const shopName = item.querySelector('.productShop a')?.textContent?.trim() || '';
        const url = item.querySelector('.productTitle a')?.href || '';
        const image = item.querySelector('.productImg img')?.src || '';

        return {
          title,
          price: parseFloat(price) || 0,
          sales: parseInt(sales.replace(/[^\d]/g, '')) || 0,
          shopName,
          url,
          images: [image],
          rating: 0,
          reviewCount: 0
        };
      });
    });

    return products;
  }

  /**
   * 爬取京东搜索结果
   */
  async crawlJDSearch(page, keyword, pageNum) {
    const searchUrl = `https://search.jd.com/Search?keyword=${encodeURIComponent(keyword)}&page=${pageNum}`;
    await page.goto(searchUrl, { waitUntil: 'networkidle2', timeout: this.config.timeout });
    await this.randomDelay();

    const products = await page.evaluate(() => {
      const items = document.querySelectorAll('.gl-item');
      return Array.from(items).map(item => {
        const title = item.querySelector('.p-name em')?.textContent?.trim() || '';
        const price = item.querySelector('.p-price i')?.textContent?.trim() || '';
        const sales = item.querySelector('.p-commit strong a')?.textContent?.trim() || '';
        const shopName = item.querySelector('.p-shop span a')?.textContent?.trim() || '';
        const url = item.querySelector('.p-name a')?.href || '';
        const image = item.querySelector('.p-img img')?.src || '';

        return {
          title,
          price: parseFloat(price) || 0,
          sales: parseInt(sales.replace(/[^\d]/g, '')) || 0,
          shopName,
          url: url.startsWith('//') ? 'https:' + url : url,
          images: [image],
          rating: 0,
          reviewCount: 0
        };
      });
    });

    return products;
  }

  /**
   * 爬取拼多多搜索结果
   */
  async crawlPDDSearch(page, keyword, pageNum) {
    const searchUrl = `https://mobile.yangkeduo.com/search_result.html?search_key=${encodeURIComponent(keyword)}&page=${pageNum}`;
    await page.goto(searchUrl, { waitUntil: 'networkidle2', timeout: this.config.timeout });
    await this.randomDelay();

    const products = await page.evaluate(() => {
      const items = document.querySelectorAll('.goods-item');
      return Array.from(items).map(item => {
        const title = item.querySelector('.goods-name')?.textContent?.trim() || '';
        const price = item.querySelector('.goods-price')?.textContent?.trim() || '';
        const sales = item.querySelector('.goods-sales')?.textContent?.trim() || '';
        const shopName = ''; // 拼多多搜索页通常不显示店铺名
        const url = item.querySelector('a')?.href || '';
        const image = item.querySelector('.goods-img img')?.src || '';

        return {
          title,
          price: parseFloat(price.replace(/[^\d.]/g, '')) || 0,
          sales: parseInt(sales.replace(/[^\d]/g, '')) || 0,
          shopName,
          url,
          images: [image],
          rating: 0,
          reviewCount: 0
        };
      });
    });

    return products;
  }

  /**
   * 获取爬虫健康状态
   */
  async getHealthStatus() {
    try {
      const browser = await this.initBrowser();
      const pages = await browser.pages();

      return {
        status: 'healthy',
        browserConnected: !!this.browser,
        openPages: pages.length,
        config: this.config,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = new CrawlerService();
