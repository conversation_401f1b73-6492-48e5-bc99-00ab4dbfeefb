import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { 
  HomeIcon, 
  CubeIcon, 
  ChartBarIcon, 
  CogIcon,
  UserIcon,
  BellIcon,
  MagnifyingGlassIcon,
  SparklesIcon,
  CloudArrowDownIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { NavItem } from '@/types';

const Sidebar: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const { unreadCount } = useNotifications();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // 导航菜单项
  const navigation: NavItem[] = [
    {
      name: '仪表板',
      href: '/dashboard',
      icon: HomeIcon,
    },
    {
      name: '产品管理',
      href: '/products',
      icon: CubeIcon,
    },
    {
      name: '智能分析',
      href: '/analysis',
      icon: SparklesIcon,
    },
    {
      name: '数据导入',
      href: '/import',
      icon: CloudArrowDownIcon,
    },
    {
      name: '市场洞察',
      href: '/insights',
      icon: ChartBarIcon,
    },
    {
      name: '搜索发现',
      href: '/search',
      icon: MagnifyingGlassIcon,
    },
  ];

  // 底部菜单项
  const bottomNavigation: NavItem[] = [
    {
      name: '通知',
      href: '/notifications',
      icon: BellIcon,
      badge: unreadCount > 0 ? unreadCount : undefined,
    },
    {
      name: '个人资料',
      href: '/profile',
      icon: UserIcon,
    },
    {
      name: '设置',
      href: '/settings',
      icon: CogIcon,
    },
  ];

  // 检查是否为当前页面
  const isCurrentPage = (href: string) => {
    if (href === '/dashboard') {
      return router.pathname === '/dashboard' || router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  // 渲染导航项
  const renderNavItem = (item: NavItem) => {
    const Icon = item.icon;
    const isCurrent = isCurrentPage(item.href);

    return (
      <Link
        key={item.name}
        href={item.href}
        className={`
          group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200
          ${isCurrent
            ? 'bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100'
            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white'
          }
        `}
        onClick={() => setIsMobileMenuOpen(false)}
      >
        <Icon
          className={`
            mr-3 flex-shrink-0 h-6 w-6 transition-colors duration-200
            ${isCurrent
              ? 'text-primary-500 dark:text-primary-400'
              : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-300'
            }
          `}
        />
        {item.name}
        {item.badge && (
          <span className="ml-auto inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
            {item.badge}
          </span>
        )}
      </Link>
    );
  };

  return (
    <>
      {/* 移动端菜单按钮 */}
      <div className="lg:hidden">
        <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <button
              type="button"
              className="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={() => setIsMobileMenuOpen(true)}
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            <h1 className="ml-3 text-xl font-semibold text-gray-900 dark:text-white">
              SmartPick
            </h1>
          </div>
        </div>
      </div>

      {/* 移动端遮罩 */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <div
        className={`
          fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        `}
      >
        <div className="flex flex-col h-full">
          {/* Logo区域 */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
            <Link href="/dashboard" className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                  <SparklesIcon className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="ml-3">
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  SmartPick
                </h1>
              </div>
            </Link>
            
            {/* 移动端关闭按钮 */}
            <button
              type="button"
              className="lg:hidden text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* 用户信息 */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  {user?.avatar_url ? (
                    <img
                      src={user.avatar_url}
                      alt={user.username}
                      className="w-10 h-10 rounded-full"
                    />
                  ) : (
                    <UserIcon className="w-6 h-6 text-gray-500 dark:text-gray-400" />
                  )}
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {user?.username}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {user?.email}
                </p>
              </div>
            </div>
          </div>

          {/* 主导航 */}
          <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
            {navigation.map(renderNavItem)}
          </nav>

          {/* 底部导航 */}
          <div className="px-2 py-4 border-t border-gray-200 dark:border-gray-700 space-y-1">
            {bottomNavigation.map(renderNavItem)}
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
