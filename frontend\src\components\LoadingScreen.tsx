import React from 'react';
import { SparklesIcon } from '@heroicons/react/24/outline';

interface LoadingScreenProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  fullScreen?: boolean;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = '加载中...',
  size = 'md',
  fullScreen = true,
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const containerClasses = fullScreen
    ? 'fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50'
    : 'flex items-center justify-center p-8';

  return (
    <div className={containerClasses}>
      <div className="text-center">
        {/* Logo和加载动画 */}
        <div className="flex items-center justify-center mb-4">
          <div className="relative">
            {/* 背景圆圈 */}
            <div className={`${sizeClasses[size]} border-4 border-gray-200 dark:border-gray-700 rounded-full`} />
            
            {/* 旋转的加载圈 */}
            <div className={`
              absolute inset-0 ${sizeClasses[size]} border-4 border-transparent border-t-primary-600 rounded-full animate-spin
            `} />
            
            {/* 中心图标 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <SparklesIcon className={`${sizeClasses[size === 'lg' ? 'md' : 'sm']} text-primary-600`} />
            </div>
          </div>
        </div>

        {/* 加载文本 */}
        <p className={`${textSizeClasses[size]} text-gray-600 dark:text-gray-400 font-medium`}>
          {message}
        </p>

        {/* 进度点 */}
        <div className="flex items-center justify-center mt-4 space-x-1">
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
          <div className="w-2 h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
      </div>
    </div>
  );
};

// 简单的加载指示器
export const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg'; className?: string }> = ({
  size = 'md',
  className = '',
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div className={`${sizeClasses[size]} border-2 border-gray-200 border-t-primary-600 rounded-full animate-spin ${className}`} />
  );
};

// 骨架屏加载器
export const SkeletonLoader: React.FC<{ 
  lines?: number; 
  className?: string;
  avatar?: boolean;
}> = ({
  lines = 3,
  className = '',
  avatar = false,
}) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {avatar && (
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full" />
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/4" />
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/3" />
          </div>
        </div>
      )}
      
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`h-4 bg-gray-300 dark:bg-gray-600 rounded ${
              index === lines - 1 ? 'w-2/3' : 'w-full'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

// 卡片骨架加载器
export const CardSkeleton: React.FC<{ count?: number }> = ({ count = 1 }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="card animate-pulse">
          <div className="card-body">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-3" />
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-full mb-2" />
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mb-4" />
            <div className="flex justify-between items-center">
              <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4" />
              <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// 表格骨架加载器
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({
  rows = 5,
  columns = 4,
}) => {
  return (
    <div className="animate-pulse">
      {/* 表头 */}
      <div className="grid gap-4 mb-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <div key={index} className="h-4 bg-gray-300 dark:bg-gray-600 rounded" />
        ))}
      </div>
      
      {/* 表格行 */}
      <div className="space-y-3">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div
            key={rowIndex}
            className="grid gap-4"
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div key={colIndex} className="h-4 bg-gray-300 dark:bg-gray-600 rounded" />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default LoadingScreen;
