import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/router';
import { User, LoginForm, RegisterForm } from '@/types';
import apiService, { setAuthToken, clearAuthToken } from '@/services/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginForm) => Promise<void>;
  register: (userData: RegisterForm) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  // 初始化认证状态
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        if (token) {
          await refreshUser();
        }
      } catch (error) {
        console.error('认证初始化失败:', error);
        clearAuthToken();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // 登录
  const login = async (credentials: LoginForm) => {
    try {
      setIsLoading(true);
      const response = await apiService.post('/auth/login', credentials);
      
      const { user: userData, accessToken } = response;
      
      // 保存token和用户信息
      setAuthToken(accessToken);
      setUser(userData);
      
      toast.success('登录成功');
      
      // 重定向到仪表板
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.message || '登录失败');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 注册
  const register = async (userData: RegisterForm) => {
    try {
      setIsLoading(true);
      const response = await apiService.post('/auth/register', userData);
      
      const { user: newUser, accessToken } = response;
      
      // 保存token和用户信息
      setAuthToken(accessToken);
      setUser(newUser);
      
      toast.success('注册成功');
      
      // 重定向到仪表板
      router.push('/dashboard');
    } catch (error: any) {
      toast.error(error.message || '注册失败');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 登出
  const logout = async () => {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      // 清除本地状态
      clearAuthToken();
      setUser(null);
      
      toast.success('已退出登录');
      
      // 重定向到登录页
      router.push('/auth/login');
    }
  };

  // 刷新用户信息
  const refreshUser = async () => {
    try {
      const userData = await apiService.get('/auth/me');
      setUser(userData);
    } catch (error: any) {
      console.error('获取用户信息失败:', error);
      
      // 如果是认证错误，清除token
      if (error.status === 401) {
        clearAuthToken();
        setUser(null);
      }
      
      throw error;
    }
  };

  // 更新用户资料
  const updateProfile = async (data: Partial<User>) => {
    try {
      const updatedUser = await apiService.put('/users/profile', data);
      setUser(updatedUser);
      toast.success('资料更新成功');
    } catch (error: any) {
      toast.error(error.message || '资料更新失败');
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// 自定义Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// 路由保护Hook
export const useRequireAuth = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // 避免在登录页面重定向
    if (!isLoading && !isAuthenticated && !router.pathname.startsWith('/auth')) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, isLoading, router]);

  return { isAuthenticated, isLoading };
};

// 管理员权限Hook
export const useRequireAdmin = () => {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && (!user || user.role !== 'admin')) {
      router.push('/dashboard');
    }
  }, [user, isLoading, router]);

  return { isAdmin: user?.role === 'admin', isLoading };
};
