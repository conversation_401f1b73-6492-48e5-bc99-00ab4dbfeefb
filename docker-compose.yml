version: '3.8'

services:
  # PostgreSQL数据库
  db:
    image: postgres:14-alpine
    container_name: smartpick-db
    environment:
      POSTGRES_DB: smartpick
      POSTGRES_USER: smartpick_user
      POSTGRES_PASSWORD: smartpick_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - smartpick-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U smartpick_user -d smartpick"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: smartpick-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smartpick-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: smartpick-backend
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************************/smartpick
      - REDIS_URL=redis://redis:6379
      - PORT=8000
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - smartpick-network
    command: npm run dev

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: smartpick-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - smartpick-network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  smartpick-network:
    driver: bridge
