const express = require('express');
const { body, query, param } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const crawlerController = require('../controllers/crawlerController');
const { validateRequest } = require('../middleware/validation');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// 所有爬虫路由都需要认证
router.use(authenticate);

/**
 * @route   POST /api/v1/crawler/crawl-product
 * @desc    爬取单个产品信息
 * @access  Private
 */
router.post('/crawl-product', [
  body('url')
    .isURL()
    .withMessage('产品URL格式不正确'),
  body('platform')
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  body('options')
    .optional()
    .isObject()
    .withMessage('爬取选项必须是对象'),
  validateRequest
], asyncHandler(crawlerController.crawlProduct));

/**
 * @route   POST /api/v1/crawler/crawl-search
 * @desc    爬取搜索结果
 * @access  Private
 */
router.post('/crawl-search', [
  body('keyword')
    .notEmpty()
    .withMessage('搜索关键词不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('关键词长度应在1-100字符之间'),
  body('platform')
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  body('maxPages')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('最大页数必须在1-10之间'),
  body('filters')
    .optional()
    .isObject()
    .withMessage('过滤条件必须是对象'),
  validateRequest
], asyncHandler(crawlerController.crawlSearch));

/**
 * @route   POST /api/v1/crawler/crawl-category
 * @desc    爬取类别商品
 * @access  Private
 */
router.post('/crawl-category', [
  body('category')
    .notEmpty()
    .withMessage('商品类别不能为空'),
  body('platform')
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  body('maxProducts')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('最大商品数必须在1-1000之间'),
  validateRequest
], asyncHandler(crawlerController.crawlCategory));

/**
 * @route   GET /api/v1/crawler/tasks
 * @desc    获取爬虫任务列表
 * @access  Private
 */
router.get('/tasks', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('每页数量必须在1-50之间'),
  query('status')
    .optional()
    .isIn(['pending', 'running', 'completed', 'failed', 'cancelled'])
    .withMessage('状态不正确'),
  query('platform')
    .optional()
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  validateRequest
], asyncHandler(crawlerController.getCrawlerTasks));

/**
 * @route   GET /api/v1/crawler/tasks/:id
 * @desc    获取爬虫任务详情
 * @access  Private
 */
router.get('/tasks/:id', [
  param('id').isUUID().withMessage('任务ID格式不正确'),
  validateRequest
], asyncHandler(crawlerController.getCrawlerTaskById));

/**
 * @route   POST /api/v1/crawler/tasks/:id/cancel
 * @desc    取消爬虫任务
 * @access  Private
 */
router.post('/tasks/:id/cancel', [
  param('id').isUUID().withMessage('任务ID格式不正确'),
  validateRequest
], asyncHandler(crawlerController.cancelCrawlerTask));

/**
 * @route   POST /api/v1/crawler/tasks/:id/retry
 * @desc    重试失败的爬虫任务
 * @access  Private
 */
router.post('/tasks/:id/retry', [
  param('id').isUUID().withMessage('任务ID格式不正确'),
  validateRequest
], asyncHandler(crawlerController.retryCrawlerTask));

/**
 * @route   DELETE /api/v1/crawler/tasks/:id
 * @desc    删除爬虫任务
 * @access  Private
 */
router.delete('/tasks/:id', [
  param('id').isUUID().withMessage('任务ID格式不正确'),
  validateRequest
], asyncHandler(crawlerController.deleteCrawlerTask));

/**
 * @route   GET /api/v1/crawler/proxy-status
 * @desc    获取代理状态
 * @access  Private
 */
router.get('/proxy-status', asyncHandler(crawlerController.getProxyStatus));

/**
 * @route   POST /api/v1/crawler/test-proxy
 * @desc    测试代理连接
 * @access  Private
 */
router.post('/test-proxy', [
  body('proxy')
    .notEmpty()
    .withMessage('代理地址不能为空'),
  validateRequest
], asyncHandler(crawlerController.testProxy));

/**
 * @route   GET /api/v1/crawler/statistics
 * @desc    获取爬虫统计信息
 * @access  Private
 */
router.get('/statistics', [
  query('period')
    .optional()
    .isIn(['24h', '7d', '30d'])
    .withMessage('统计周期不正确'),
  validateRequest
], asyncHandler(crawlerController.getCrawlerStatistics));

// 管理员路由
/**
 * @route   GET /api/v1/crawler/logs
 * @desc    获取爬虫日志（管理员）
 * @access  Private/Admin
 */
router.get('/logs', [
  authorize('admin'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  query('level')
    .optional()
    .isIn(['info', 'warn', 'error'])
    .withMessage('日志级别不正确'),
  validateRequest
], asyncHandler(crawlerController.getCrawlerLogs));

/**
 * @route   POST /api/v1/crawler/config
 * @desc    更新爬虫配置（管理员）
 * @access  Private/Admin
 */
router.post('/config', [
  authorize('admin'),
  body('concurrentLimit')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('并发限制必须在1-20之间'),
  body('delayMin')
    .optional()
    .isInt({ min: 100, max: 10000 })
    .withMessage('最小延迟必须在100-10000毫秒之间'),
  body('delayMax')
    .optional()
    .isInt({ min: 100, max: 10000 })
    .withMessage('最大延迟必须在100-10000毫秒之间'),
  body('retryAttempts')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('重试次数必须在1-10之间'),
  validateRequest
], asyncHandler(crawlerController.updateCrawlerConfig));

/**
 * @route   GET /api/v1/crawler/health
 * @desc    获取爬虫服务健康状态
 * @access  Private
 */
router.get('/health', asyncHandler(crawlerController.getCrawlerHealth));

module.exports = router;
