const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('开始种子数据...');

  // 创建管理员用户
  const hashedPassword = await bcrypt.hash('admin123', 12);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      firstName: '管理员',
      lastName: '用户',
      role: 'ADMIN',
      status: 'ACTIVE',
      emailVerified: true,
      emailVerifiedAt: new Date(),
      preferences: JSON.stringify({
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        theme: 'light'
      }),
      settings: JSON.stringify({
        notifications: {
          email: true,
          push: true,
          analysis: true,
          import: true
        },
        privacy: {
          profilePublic: false,
          shareAnalytics: false
        }
      })
    }
  });

  console.log('管理员用户已创建:', adminUser.email);

  // 创建测试用户
  const testUserPassword = await bcrypt.hash('test123', 12);
  
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'testuser',
      password: testUserPassword,
      firstName: '测试',
      lastName: '用户',
      role: 'USER',
      status: 'ACTIVE',
      emailVerified: true,
      emailVerifiedAt: new Date(),
      preferences: JSON.stringify({
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        theme: 'light'
      })
    }
  });

  console.log('测试用户已创建:', testUser.email);

  // 创建示例产品
  const sampleProducts = [
    {
      title: 'iPhone 15 Pro Max 256GB 深空黑色',
      description: '苹果iPhone 15 Pro Max，搭载A17 Pro芯片，钛金属设计，支持5G网络',
      url: 'https://www.apple.com.cn/iphone-15-pro/',
      imageUrl: 'https://store.storeimages.cdn-apple.com/8756/as-images.apple.com/is/iphone-15-pro-max-naturaltitanium-select.png',
      brand: 'Apple',
      model: 'iPhone 15 Pro Max',
      platform: 'MANUAL',
      currentPrice: 8999,
      originalPrice: 9999,
      currency: 'CNY',
      salesCount: 1000,
      rating: 4.8,
      reviewCount: 500,
      stockQuantity: 100,
      category: '手机数码',
      subcategory: '智能手机',
      tags: JSON.stringify(['苹果', 'iPhone', '5G', '拍照']),
      status: 'ACTIVE',
      dataSource: 'MANUAL'
    },
    {
      title: '小米14 Ultra 16GB+512GB 黑色',
      description: '小米14 Ultra，徕卡专业摄影，骁龙8 Gen3处理器',
      url: 'https://www.mi.com/xiaomi-14-ultra',
      imageUrl: 'https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/product/xiaomi-14-ultra.png',
      brand: '小米',
      model: '小米14 Ultra',
      platform: 'MANUAL',
      currentPrice: 6499,
      originalPrice: 6999,
      currency: 'CNY',
      salesCount: 800,
      rating: 4.7,
      reviewCount: 320,
      stockQuantity: 50,
      category: '手机数码',
      subcategory: '智能手机',
      tags: JSON.stringify(['小米', '徕卡', '拍照', '5G']),
      status: 'ACTIVE',
      dataSource: 'MANUAL'
    },
    {
      title: 'MacBook Pro 14英寸 M3 Pro芯片',
      description: 'MacBook Pro 14英寸，M3 Pro芯片，18GB统一内存，512GB SSD存储',
      url: 'https://www.apple.com.cn/macbook-pro-14-and-16/',
      imageUrl: 'https://store.storeimages.cdn-apple.com/8756/as-images.apple.com/is/mbp14-spacegray-select-202310.png',
      brand: 'Apple',
      model: 'MacBook Pro 14"',
      platform: 'MANUAL',
      currentPrice: 16999,
      originalPrice: 17999,
      currency: 'CNY',
      salesCount: 200,
      rating: 4.9,
      reviewCount: 150,
      stockQuantity: 30,
      category: '电脑办公',
      subcategory: '笔记本电脑',
      tags: JSON.stringify(['苹果', 'MacBook', 'M3', '办公']),
      status: 'ACTIVE',
      dataSource: 'MANUAL'
    }
  ];

  for (const productData of sampleProducts) {
    const product = await prisma.product.create({
      data: {
        ...productData,
        userId: testUser.id,
        attributes: JSON.stringify({
          color: '黑色',
          storage: '512GB',
          warranty: '1年'
        })
      }
    });

    console.log('示例产品已创建:', product.title);
  }

  // 创建示例分析结果
  const analysisResult = await prisma.analysisResult.create({
    data: {
      userId: testUser.id,
      productId: (await prisma.product.findFirst({ where: { userId: testUser.id } })).id,
      title: 'iPhone 15 Pro Max 市场分析',
      description: '基于AI的iPhone 15 Pro Max市场竞争力分析',
      type: 'PRODUCT',
      status: 'COMPLETED',
      priority: 'HIGH',
      config: JSON.stringify({
        analysisType: 'comprehensive',
        includeCompetitors: true,
        timeRange: '30d'
      }),
      summary: JSON.stringify({
        overallScore: 85,
        competitiveness: 'high',
        marketPosition: 'premium',
        recommendation: 'recommended'
      }),
      insights: JSON.stringify([
        '产品在高端市场具有强竞争力',
        '价格定位合理，符合品牌定位',
        '用户评价较高，口碑良好',
        '库存充足，供应链稳定'
      ]),
      recommendations: JSON.stringify([
        '可以考虑适当提高价格',
        '加强营销推广',
        '关注竞品动态',
        '优化库存管理'
      ]),
      confidenceScore: 0.92,
      startedAt: new Date(Date.now() - 300000), // 5分钟前开始
      completedAt: new Date(),
      durationMs: 300000,
      category: '手机数码'
    }
  });

  console.log('示例分析结果已创建:', analysisResult.title);

  console.log('种子数据创建完成！');
}

main()
  .catch((e) => {
    console.error('种子数据创建失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
