const express = require('express');
const router = express.Router();
const importController = require('../controllers/importController');
const { authenticateToken } = require('../middleware/auth');

// 所有路由都需要认证
router.use(authenticateToken);

// 模板相关路由
router.get('/template/excel', importController.downloadExcelTemplate);
router.get('/template/csv', importController.downloadCSVTemplate);
router.get('/template/fields', importController.getTemplateFields);

// 文件上传和导入
router.post('/upload', importController.uploadAndImport);

// 导入任务管理
router.get('/tasks', importController.getImportTasks);
router.get('/tasks/:id', importController.getImportTaskStatus);
router.delete('/tasks/:id', importController.deleteImportTask);
router.post('/tasks/:id/retry', importController.retryImportTask);

// 统计信息
router.get('/statistics', importController.getImportStatistics);

module.exports = router;
