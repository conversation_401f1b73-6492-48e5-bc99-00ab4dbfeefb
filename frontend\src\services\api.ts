import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types';

// API配置 - 使用相对路径通过Next.js代理
const API_BASE_URL = '';
const API_PREFIX = '/api/v1';

// 创建axios实例
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: `${API_BASE_URL}${API_PREFIX}`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId();

      // 记录请求日志
      console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });

      return config;
    },
    (error) => {
      console.error('[API Request Error]', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // 记录响应日志
      console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });

      // 返回完整的响应对象，让调用方决定如何处理
      return response;
    },
    (error) => {
      console.error('[API Response Error]', error);

      // 处理认证错误
      if (error.response?.status === 401) {
        handleAuthError();
      }

      // 处理网络错误
      if (!error.response) {
        return Promise.reject({
          message: '网络连接失败，请检查网络设置',
          code: 'NETWORK_ERROR',
        });
      }

      // 返回标准化错误
      const errorData = error.response.data;
      return Promise.reject({
        message: errorData?.error || errorData?.message || '请求失败',
        code: errorData?.code || 'REQUEST_FAILED',
        status: error.response.status,
        details: errorData?.details,
      });
    }
  );

  return instance;
};

// 获取认证token
const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token');
};

// 设置认证token
export const setAuthToken = (token: string): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem('auth_token', token);
};

// 清除认证token
export const clearAuthToken = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('auth_token');
};

// 处理认证错误
const handleAuthError = (): void => {
  clearAuthToken();
  // 重定向到登录页面
  if (typeof window !== 'undefined') {
    window.location.href = '/auth/login';
  }
};

// 生成请求ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 创建API实例
const api = createApiInstance();

// API服务类
class ApiService {
  private instance: AxiosInstance;

  constructor() {
    this.instance = api;
  }

  // 通用请求方法
  async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.instance.request<ApiResponse<T>>(config);
      // 直接返回响应数据，因为我们的API结构是 { success, message, user, accessToken }
      return response.data as T;
    } catch (error) {
      throw error;
    }
  }

  // GET请求
  async get<T = any>(url: string, params?: any): Promise<T> {
    return this.request<T>({
      method: 'GET',
      url,
      params,
    });
  }

  // POST请求
  async post<T = any>(url: string, data?: any): Promise<T> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
    });
  }

  // PUT请求
  async put<T = any>(url: string, data?: any): Promise<T> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
    });
  }

  // PATCH请求
  async patch<T = any>(url: string, data?: any): Promise<T> {
    return this.request<T>({
      method: 'PATCH',
      url,
      data,
    });
  }

  // DELETE请求
  async delete<T = any>(url: string): Promise<T> {
    return this.request<T>({
      method: 'DELETE',
      url,
    });
  }

  // 文件上传
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request<T>({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  // 下载文件
  async download(url: string, filename?: string): Promise<void> {
    try {
      const response = await this.instance.request({
        method: 'GET',
        url,
        responseType: 'blob',
      });

      // 创建下载链接
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      throw error;
    }
  }

  // 批量请求
  async batch<T = any>(requests: AxiosRequestConfig[]): Promise<T[]> {
    try {
      const promises = requests.map(config => this.instance.request<ApiResponse<T>>(config));
      const responses = await Promise.all(promises);
      return responses.map(response => response.data.data as T);
    } catch (error) {
      throw error;
    }
  }

  // 取消请求
  createCancelToken() {
    return axios.CancelToken.source();
  }

  // 检查请求是否被取消
  isCancel(error: any): boolean {
    return axios.isCancel(error);
  }
}

// 创建API服务实例
const apiService = new ApiService();

// 导出API服务
export default apiService;

// 导出常用方法
export const { get, post, put, patch, delete: del, upload, download, batch } = apiService;

// 导出实例用于高级用法
export { api };

// 健康检查
export const healthCheck = async (): Promise<boolean> => {
  try {
    await get('/health');
    return true;
  } catch (error) {
    return false;
  }
};

// 重试机制
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        break;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }

  throw lastError;
};
