const express = require('express');
const { body, query, param } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const productController = require('../controllers/productController');
const { validateRequest } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// 所有产品路由都需要认证
router.use(authenticate);

/**
 * @route   GET /api/v1/products
 * @desc    获取产品列表
 * @access  Private
 */
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  query('platform')
    .optional()
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  query('category')
    .optional()
    .notEmpty()
    .withMessage('类别不能为空'),
  query('sortBy')
    .optional()
    .isIn(['price', 'sales', 'rating', 'created_at'])
    .withMessage('排序字段不正确'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序方向不正确'),
  validateRequest
], asyncHandler(productController.getProducts));

/**
 * @route   GET /api/v1/products/:id
 * @desc    获取产品详情
 * @access  Private
 */
router.get('/:id', [
  param('id').isUUID().withMessage('产品ID格式不正确'),
  validateRequest
], asyncHandler(productController.getProductById));

/**
 * @route   POST /api/v1/products
 * @desc    添加产品
 * @access  Private
 */
router.post('/', [
  body('title')
    .notEmpty()
    .withMessage('产品标题不能为空')
    .isLength({ max: 200 })
    .withMessage('产品标题不能超过200字'),
  body('url')
    .isURL()
    .withMessage('产品URL格式不正确'),
  body('platform')
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('价格必须是非负数'),
  body('category')
    .notEmpty()
    .withMessage('产品类别不能为空'),
  validateRequest
], asyncHandler(productController.createProduct));

/**
 * @route   PUT /api/v1/products/:id
 * @desc    更新产品信息
 * @access  Private
 */
router.put('/:id', [
  param('id').isUUID().withMessage('产品ID格式不正确'),
  body('title')
    .optional()
    .notEmpty()
    .withMessage('产品标题不能为空')
    .isLength({ max: 200 })
    .withMessage('产品标题不能超过200字'),
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('价格必须是非负数'),
  body('category')
    .optional()
    .notEmpty()
    .withMessage('产品类别不能为空'),
  validateRequest
], asyncHandler(productController.updateProduct));

/**
 * @route   DELETE /api/v1/products/:id
 * @desc    删除产品
 * @access  Private
 */
router.delete('/:id', [
  param('id').isUUID().withMessage('产品ID格式不正确'),
  validateRequest
], asyncHandler(productController.deleteProduct));

/**
 * @route   POST /api/v1/products/batch-import
 * @desc    批量导入产品
 * @access  Private
 */
router.post('/batch-import', [
  body('products')
    .isArray({ min: 1, max: 100 })
    .withMessage('产品列表应包含1-100个产品'),
  body('products.*.title')
    .notEmpty()
    .withMessage('产品标题不能为空'),
  body('products.*.url')
    .isURL()
    .withMessage('产品URL格式不正确'),
  body('products.*.platform')
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  validateRequest
], asyncHandler(productController.batchImportProducts));

/**
 * @route   GET /api/v1/products/:id/price-history
 * @desc    获取产品价格历史
 * @access  Private
 */
router.get('/:id/price-history', [
  param('id').isUUID().withMessage('产品ID格式不正确'),
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('天数必须在1-365之间'),
  validateRequest
], asyncHandler(productController.getPriceHistory));

/**
 * @route   GET /api/v1/products/:id/competitors
 * @desc    获取产品竞争对手
 * @access  Private
 */
router.get('/:id/competitors', [
  param('id').isUUID().withMessage('产品ID格式不正确'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('限制数量必须在1-20之间'),
  validateRequest
], asyncHandler(productController.getCompetitors));

/**
 * @route   POST /api/v1/products/:id/track
 * @desc    添加产品到跟踪列表
 * @access  Private
 */
router.post('/:id/track', [
  param('id').isUUID().withMessage('产品ID格式不正确'),
  validateRequest
], asyncHandler(productController.trackProduct));

/**
 * @route   DELETE /api/v1/products/:id/track
 * @desc    从跟踪列表移除产品
 * @access  Private
 */
router.delete('/:id/track', [
  param('id').isUUID().withMessage('产品ID格式不正确'),
  validateRequest
], asyncHandler(productController.untrackProduct));

/**
 * @route   GET /api/v1/products/tracked
 * @desc    获取跟踪的产品列表
 * @access  Private
 */
router.get('/tracked', asyncHandler(productController.getTrackedProducts));

/**
 * @route   GET /api/v1/products/categories
 * @desc    获取产品类别列表
 * @access  Private
 */
router.get('/categories', asyncHandler(productController.getCategories));

/**
 * @route   GET /api/v1/products/search
 * @desc    搜索产品
 * @access  Private
 */
router.get('/search', [
  query('q')
    .notEmpty()
    .withMessage('搜索关键词不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('搜索关键词长度应在1-100字符之间'),
  query('platform')
    .optional()
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最低价格必须是非负数'),
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最高价格必须是非负数'),
  validateRequest
], asyncHandler(productController.searchProducts));

module.exports = router;
