/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dashboard";
exports.ids = ["pages/dashboard"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,Cog6ToothIcon,ComputerDesktopIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,Cog6ToothIcon,ComputerDesktopIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ComputerDesktopIcon: () => (/* reexport safe */ _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MagnifyingGlassIcon: () => (/* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   MoonIcon: () => (/* reexport safe */ _MoonIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   SunIcon: () => (/* reexport safe */ _SunIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   UserIcon: () => (/* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ComputerDesktopIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _MoonIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MoonIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n/* harmony import */ var _SunIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SunIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJlbGxJY29uLENvZzZUb290aEljb24sQ29tcHV0ZXJEZXNrdG9wSWNvbixNYWduaWZ5aW5nR2xhc3NJY29uLE1vb25JY29uLFN1bkljb24sVXNlckljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3FGO0FBQ2xDO0FBQ1U7QUFDWTtBQUNBO0FBQ3RCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydHBpY2stZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9lMzQ0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uIH0gZnJvbSBcIi4vQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJlbGxJY29uIH0gZnJvbSBcIi4vQmVsbEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2c2VG9vdGhJY29uIH0gZnJvbSBcIi4vQ29nNlRvb3RoSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENvbXB1dGVyRGVza3RvcEljb24gfSBmcm9tIFwiLi9Db21wdXRlckRlc2t0b3BJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWFnbmlmeWluZ0dsYXNzSWNvbiB9IGZyb20gXCIuL01hZ25pZnlpbmdHbGFzc0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNb29uSWNvbiB9IGZyb20gXCIuL01vb25JY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3VuSWNvbiB9IGZyb20gXCIuL1N1bkljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VySWNvbiB9IGZyb20gXCIuL1VzZXJJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,Cog6ToothIcon,ComputerDesktopIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Bars3Icon,BellIcon,ChartBarIcon,CloudArrowDownIcon,CogIcon,CubeIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bars3Icon,BellIcon,ChartBarIcon,CloudArrowDownIcon,CogIcon,CubeIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   CloudArrowDownIcon: () => (/* reexport safe */ _CloudArrowDownIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   CogIcon: () => (/* reexport safe */ _CogIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CubeIcon: () => (/* reexport safe */ _CubeIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   MagnifyingGlassIcon: () => (/* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   SparklesIcon: () => (/* reexport safe */ _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   UserIcon: () => (/* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _CloudArrowDownIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CloudArrowDownIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _CogIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CogIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _CubeIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CubeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SparklesIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJzM0ljb24sQmVsbEljb24sQ2hhcnRCYXJJY29uLENsb3VkQXJyb3dEb3duSWNvbixDb2dJY29uLEN1YmVJY29uLEhvbWVJY29uLE1hZ25pZnlpbmdHbGFzc0ljb24sU3BhcmtsZXNJY29uLFVzZXJJY29uLFhNYXJrSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDcUQ7QUFDRjtBQUNRO0FBQ1k7QUFDdEI7QUFDRTtBQUNBO0FBQ3NCO0FBQ2Q7QUFDUiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtYXJ0cGljay1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzPzAyNDciXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhcnMzSWNvbiB9IGZyb20gXCIuL0JhcnMzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJlbGxJY29uIH0gZnJvbSBcIi4vQmVsbEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGFydEJhckljb24gfSBmcm9tIFwiLi9DaGFydEJhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDbG91ZEFycm93RG93bkljb24gfSBmcm9tIFwiLi9DbG91ZEFycm93RG93bkljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2dJY29uIH0gZnJvbSBcIi4vQ29nSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEN1YmVJY29uIH0gZnJvbSBcIi4vQ3ViZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIb21lSWNvbiB9IGZyb20gXCIuL0hvbWVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWFnbmlmeWluZ0dsYXNzSWNvbiB9IGZyb20gXCIuL01hZ25pZnlpbmdHbGFzc0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTcGFya2xlc0ljb24gfSBmcm9tIFwiLi9TcGFya2xlc0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VySWNvbiB9IGZyb20gXCIuL1VzZXJJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWE1hcmtJY29uIH0gZnJvbSBcIi4vWE1hcmtJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bars3Icon,BellIcon,ChartBarIcon,CloudArrowDownIcon,CogIcon,CubeIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,ShoppingBagIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,ShoppingBagIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CloudArrowUpIcon: () => (/* reexport safe */ _CloudArrowUpIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ShoppingBagIcon: () => (/* reexport safe */ _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   UserGroupIcon: () => (/* reexport safe */ _UserGroupIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _CloudArrowUpIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CloudArrowUpIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ShoppingBagIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _UserGroupIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UserGroupIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGFydEJhckljb24sQ2xvdWRBcnJvd1VwSWNvbixTaG9wcGluZ0JhZ0ljb24sVXNlckdyb3VwSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQzJEO0FBQ1E7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3NtYXJ0cGljay1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzP2E4MzAiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoYXJ0QmFySWNvbiB9IGZyb20gXCIuL0NoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENsb3VkQXJyb3dVcEljb24gfSBmcm9tIFwiLi9DbG91ZEFycm93VXBJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hvcHBpbmdCYWdJY29uIH0gZnJvbSBcIi4vU2hvcHBpbmdCYWdJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckdyb3VwSWNvbiB9IGZyb20gXCIuL1VzZXJHcm91cEljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,ShoppingBagIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* reexport safe */ D_ai_product_select_frontend_node_modules_headlessui_react_dist_components_dialog_dialog_js__WEBPACK_IMPORTED_MODULE_0__.Dialog),\n/* harmony export */   Transition: () => (/* reexport safe */ D_ai_product_select_frontend_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__.Transition)\n/* harmony export */ });\n/* harmony import */ var D_ai_product_select_frontend_node_modules_headlessui_react_dist_components_dialog_dialog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/dialog/dialog.js */ \"./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var D_ai_product_select_frontend_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/transitions/transition.js */ \"./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1EaWFsb2csVHJhbnNpdGlvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZWFkbGVzc3VpL3JlYWN0L2Rpc3QvaGVhZGxlc3N1aS5lc20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDK0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydHBpY2stZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9oZWFkbGVzc3VpLmVzbS5qcz9lYWY0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgRGlhbG9nIH0gZnJvbSBcIkQ6XFxcXGFpLXByb2R1Y3Qtc2VsZWN0XFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxAaGVhZGxlc3N1aVxcXFxyZWFjdFxcXFxkaXN0XFxcXGNvbXBvbmVudHNcXFxcZGlhbG9nXFxcXGRpYWxvZy5qc1wiXG5leHBvcnQgeyBUcmFuc2l0aW9uIH0gZnJvbSBcIkQ6XFxcXGFpLXByb2R1Y3Qtc2VsZWN0XFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxAaGVhZGxlc3N1aVxcXFxyZWFjdFxcXFxkaXN0XFxcXGNvbXBvbmVudHNcXFxcdHJhbnNpdGlvbnNcXFxcdHJhbnNpdGlvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MagnifyingGlassIcon: () => (/* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NYWduaWZ5aW5nR2xhc3NJY29uLFhNYXJrSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDeUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydHBpY2stZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz8wMTRmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYWduaWZ5aW5nR2xhc3NJY29uIH0gZnJvbSBcIi4vTWFnbmlmeWluZ0dsYXNzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js":
/*!***********************************************************************************************************!*\
  !*** __barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Menu: () => (/* reexport safe */ D_ai_product_select_frontend_node_modules_headlessui_react_dist_components_menu_menu_js__WEBPACK_IMPORTED_MODULE_0__.Menu),\n/* harmony export */   Transition: () => (/* reexport safe */ D_ai_product_select_frontend_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__.Transition)\n/* harmony export */ });\n/* harmony import */ var D_ai_product_select_frontend_node_modules_headlessui_react_dist_components_menu_menu_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/menu/menu.js */ \"./node_modules/@headlessui/react/dist/components/menu/menu.js\");\n/* harmony import */ var D_ai_product_select_frontend_node_modules_headlessui_react_dist_components_transitions_transition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@headlessui/react/dist/components/transitions/transition.js */ \"./node_modules/@headlessui/react/dist/components/transitions/transition.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NZW51LFRyYW5zaXRpb24hPSEuL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hlYWRsZXNzdWkuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQ3lIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21hcnRwaWNrLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0BoZWFkbGVzc3VpL3JlYWN0L2Rpc3QvaGVhZGxlc3N1aS5lc20uanM/ZGU3ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IE1lbnUgfSBmcm9tIFwiRDpcXFxcYWktcHJvZHVjdC1zZWxlY3RcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXEBoZWFkbGVzc3VpXFxcXHJlYWN0XFxcXGRpc3RcXFxcY29tcG9uZW50c1xcXFxtZW51XFxcXG1lbnUuanNcIlxuZXhwb3J0IHsgVHJhbnNpdGlvbiB9IGZyb20gXCJEOlxcXFxhaS1wcm9kdWN0LXNlbGVjdFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQGhlYWRsZXNzdWlcXFxccmVhY3RcXFxcZGlzdFxcXFxjb21wb25lbnRzXFxcXHRyYW5zaXRpb25zXFxcXHRyYW5zaXRpb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=SparklesIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************!*\
  !*** __barrel_optimize__?names=SparklesIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SparklesIcon: () => (/* reexport safe */ _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SparklesIcon.js */ "./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js");



/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\dashboard.tsx */ \"./src/pages/dashboard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dashboard\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/Layout/Header.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,Cog6ToothIcon,ComputerDesktopIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,BellIcon,Cog6ToothIcon,ComputerDesktopIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _components_UI_Dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/UI/Dropdown */ \"./src/components/UI/Dropdown.tsx\");\n/* harmony import */ var _components_SearchModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/SearchModal */ \"./src/components/SearchModal.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst Header = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { theme, setTheme, toggleTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const { notifications, unreadCount, markAsRead } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__.useNotifications)();\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 主题选项\n    const themeOptions = [\n        {\n            value: \"light\",\n            label: \"浅色\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SunIcon\n        },\n        {\n            value: \"dark\",\n            label: \"深色\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MoonIcon\n        },\n        {\n            value: \"auto\",\n            label: \"跟随系统\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ComputerDesktopIcon\n        }\n    ];\n    // 用户菜单选项\n    const userMenuItems = [\n        {\n            label: \"个人资料\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.UserIcon\n        },\n        {\n            label: \"设置\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.Cog6ToothIcon\n        },\n        {\n            type: \"divider\"\n        },\n        {\n            label: \"退出登录\",\n            onClick: logout,\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowRightOnRectangleIcon,\n            className: \"text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300\"\n        }\n    ];\n    // 获取页面标题\n    const getPageTitle = ()=>{\n        const titles = {\n            \"/dashboard\": \"仪表板\",\n            \"/products\": \"产品管理\",\n            \"/analysis\": \"智能分析\",\n            \"/crawler\": \"数据爬取\",\n            \"/insights\": \"市场洞察\",\n            \"/search\": \"搜索发现\",\n            \"/notifications\": \"通知中心\",\n            \"/profile\": \"个人资料\",\n            \"/settings\": \"设置\"\n        };\n        return titles[router.pathname] || \"智能选品系统\";\n    };\n    // 处理搜索\n    const handleSearch = (query)=>{\n        router.push(`/search?q=${encodeURIComponent(query)}`);\n        setIsSearchOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-gray-900 dark:text-white\",\n                                    children: getPageTitle()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"p-2 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors duration-200\",\n                                        onClick: ()=>setIsSearchOpen(true),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MagnifyingGlassIcon, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"p-2 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors duration-200\",\n                                            children: [\n                                                theme === \"light\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.SunIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 43\n                                                }, void 0),\n                                                theme === \"dark\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MoonIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 42\n                                                }, void 0),\n                                                theme === \"auto\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ComputerDesktopIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        items: themeOptions.map((option)=>({\n                                                label: option.label,\n                                                icon: option.icon,\n                                                onClick: ()=>setTheme(option.value),\n                                                active: theme === option.value\n                                            })),\n                                        align: \"right\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"relative p-2 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.BellIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full\",\n                                                    children: unreadCount > 99 ? \"99+\" : unreadCount\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        items: [\n                                            {\n                                                type: \"header\",\n                                                label: `通知 (${unreadCount} 条未读)`\n                                            },\n                                            ...notifications.slice(0, 5).map((notification)=>({\n                                                    label: notification.title,\n                                                    description: notification.message,\n                                                    timestamp: notification.timestamp,\n                                                    unread: !notification.read,\n                                                    onClick: ()=>{\n                                                        markAsRead(notification.id);\n                                                        if (notification.action) {\n                                                            router.push(notification.action.url);\n                                                        }\n                                                    }\n                                                })),\n                                            {\n                                                type: \"divider\"\n                                            },\n                                            {\n                                                label: \"查看全部通知\",\n                                                href: \"/notifications\"\n                                            }\n                                        ],\n                                        align: \"right\",\n                                        className: \"w-80\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UI_Dropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"flex items-center p-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\",\n                                                children: user?.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: user.avatar_url,\n                                                    alt: user?.username,\n                                                    className: \"w-8 h-8 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 25\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_BellIcon_Cog6ToothIcon_ComputerDesktopIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.UserIcon, {\n                                                    className: \"w-5 h-5 text-gray-500 dark:text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        items: [\n                                            {\n                                                type: \"header\",\n                                                label: user?.username || \"用户\",\n                                                description: user?.email\n                                            },\n                                            {\n                                                type: \"divider\"\n                                            },\n                                            ...userMenuItems\n                                        ],\n                                        align: \"right\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isSearchOpen,\n                onClose: ()=>setIsSearchOpen(false),\n                onSearch: handleSearch\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout/Header.tsx\n");

/***/ }),

/***/ "./src/components/Layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/Layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChartBarIcon,CloudArrowDownIcon,CogIcon,CubeIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=Bars3Icon,BellIcon,ChartBarIcon,CloudArrowDownIcon,CogIcon,CubeIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Sidebar = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { unreadCount } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__.useNotifications)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 导航菜单项\n    const navigation = [\n        {\n            name: \"仪表板\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.HomeIcon\n        },\n        {\n            name: \"产品管理\",\n            href: \"/products\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CubeIcon\n        },\n        {\n            name: \"智能分析\",\n            href: \"/analysis\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.SparklesIcon\n        },\n        {\n            name: \"数据导入\",\n            href: \"/import\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CloudArrowDownIcon\n        },\n        {\n            name: \"市场洞察\",\n            href: \"/insights\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ChartBarIcon\n        },\n        {\n            name: \"搜索发现\",\n            href: \"/search\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.MagnifyingGlassIcon\n        }\n    ];\n    // 底部菜单项\n    const bottomNavigation = [\n        {\n            name: \"通知\",\n            href: \"/notifications\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon,\n            badge: unreadCount > 0 ? unreadCount : undefined\n        },\n        {\n            name: \"个人资料\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserIcon\n        },\n        {\n            name: \"设置\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CogIcon\n        }\n    ];\n    // 检查是否为当前页面\n    const isCurrentPage = (href)=>{\n        if (href === \"/dashboard\") {\n            return router.pathname === \"/dashboard\" || router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    // 渲染导航项\n    const renderNavItem = (item)=>{\n        const Icon = item.icon;\n        const isCurrent = isCurrentPage(item.href);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n            href: item.href,\n            className: `\n          group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n          ${isCurrent ? \"bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"}\n        `,\n            onClick: ()=>setIsMobileMenuOpen(false),\n            children: [\n                Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: `\n              mr-3 flex-shrink-0 h-6 w-6 transition-colors duration-200\n              ${isCurrent ? \"text-primary-500 dark:text-primary-400\" : \"text-gray-400 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-300\"}\n            `\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, undefined),\n                item.name,\n                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-auto inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full\",\n                    children: item.badge\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, item.name, true, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\",\n                                onClick: ()=>setIsMobileMenuOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"ml-3 text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"SmartPick\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setIsMobileMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n          ${isMobileMenuOpen ? \"translate-x-0\" : \"-translate-x-full\"}\n        `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard\",\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.SparklesIcon, {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                children: \"SmartPick\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"lg:hidden text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\",\n                                            children: user?.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: user.avatar_url,\n                                                alt: user.username,\n                                                className: \"w-10 h-10 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChartBarIcon_CloudArrowDownIcon_CogIcon_CubeIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserIcon, {\n                                                className: \"w-6 h-6 text-gray-500 dark:text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                children: user?.username\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: user?.email\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",\n                            children: navigation.map(renderNavItem)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2 py-4 border-t border-gray-200 dark:border-gray-700 space-y-1\",\n                            children: bottomNavigation.map(renderNavItem)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout/Sidebar.tsx\n");

/***/ }),

/***/ "./src/components/Layout/index.tsx":
/*!*****************************************!*\
  !*** ./src/components/Layout/index.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"./src/components/Layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"./src/components/Layout/Header.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _Sidebar__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _Sidebar__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Layout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // 不需要布局的页面\n    const noLayoutPages = [\n        \"/auth/login\",\n        \"/auth/register\",\n        \"/auth/forgot-password\"\n    ];\n    const isNoLayoutPage = noLayoutPages.includes(router.pathname);\n    // 如果是认证页面，直接渲染内容\n    if (isNoLayoutPage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // 如果正在加载认证状态，显示加载屏幕\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.tsx\",\n            lineNumber: 27,\n            columnNumber: 12\n        }, undefined);\n    }\n    // 如果未认证，重定向到登录页面\n    if (!isAuthenticated) {\n        router.push(\"/auth/login\");\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.tsx\",\n            lineNumber: 33,\n            columnNumber: 12\n        }, undefined);\n    }\n    // 渲染主布局\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"py-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container-responsive\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\Layout\\\\index.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout/index.tsx\n");

/***/ }),

/***/ "./src/components/LoadingScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/LoadingScreen.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CardSkeleton: () => (/* binding */ CardSkeleton),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   SkeletonLoader: () => (/* binding */ SkeletonLoader),\n/* harmony export */   TableSkeleton: () => (/* binding */ TableSkeleton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=SparklesIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\nconst LoadingScreen = ({ message = \"加载中...\", size = \"md\", fullScreen = true })=>{\n    const sizeClasses = {\n        sm: \"w-6 h-6\",\n        md: \"w-8 h-8\",\n        lg: \"w-12 h-12\"\n    };\n    const textSizeClasses = {\n        sm: \"text-sm\",\n        md: \"text-base\",\n        lg: \"text-lg\"\n    };\n    const containerClasses = fullScreen ? \"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50\" : \"flex items-center justify-center p-8\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: containerClasses,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${sizeClasses[size]} border-4 border-gray-200 dark:border-gray-700 rounded-full`\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `\n              absolute inset-0 ${sizeClasses[size]} border-4 border-transparent border-t-primary-600 rounded-full animate-spin\n            `\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.SparklesIcon, {\n                                    className: `${sizeClasses[size === \"lg\" ? \"md\" : \"sm\"]} text-primary-600`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: `${textSizeClasses[size]} text-gray-600 dark:text-gray-400 font-medium`,\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mt-4 space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-primary-600 rounded-full animate-bounce\",\n                            style: {\n                                animationDelay: \"0ms\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-primary-600 rounded-full animate-bounce\",\n                            style: {\n                                animationDelay: \"150ms\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-primary-600 rounded-full animate-bounce\",\n                            style: {\n                                animationDelay: \"300ms\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n// 简单的加载指示器\nconst LoadingSpinner = ({ size = \"md\", className = \"\" })=>{\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-6 h-6\",\n        lg: \"w-8 h-8\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${sizeClasses[size]} border-2 border-gray-200 border-t-primary-600 rounded-full animate-spin ${className}`\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n// 骨架屏加载器\nconst SkeletonLoader = ({ lines = 3, className = \"\", avatar = false })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `animate-pulse ${className}`,\n        children: [\n            avatar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: Array.from({\n                    length: lines\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `h-4 bg-gray-300 dark:bg-gray-600 rounded ${index === lines - 1 ? \"w-2/3\" : \"w-full\"}`\n                    }, index, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n// 卡片骨架加载器\nconst CardSkeleton = ({ count = 1 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: Array.from({\n            length: count\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-body\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-3 bg-gray-300 dark:bg-gray-600 rounded w-full mb-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, undefined)\n            }, index, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n// 表格骨架加载器\nconst TableSkeleton = ({ rows = 5, columns = 4 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 mb-4\",\n                style: {\n                    gridTemplateColumns: `repeat(${columns}, 1fr)`\n                },\n                children: Array.from({\n                    length: columns\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 dark:bg-gray-600 rounded\"\n                    }, index, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: Array.from({\n                    length: rows\n                }).map((_, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        style: {\n                            gridTemplateColumns: `repeat(${columns}, 1fr)`\n                        },\n                        children: Array.from({\n                            length: columns\n                        }).map((_, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-300 dark:bg-gray-600 rounded\"\n                            }, colIndex, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, undefined))\n                    }, rowIndex, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\LoadingScreen.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingScreen);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/LoadingScreen.tsx\n");

/***/ }),

/***/ "./src/components/SearchModal.tsx":
/*!****************************************!*\
  !*** ./src/components/SearchModal.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\nconst SearchModal = ({ isOpen, onClose, onSearch })=>{\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (onSearch) {\n            onSearch(searchQuery);\n        } else {\n            console.log(\"搜索:\", searchQuery);\n        }\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Transition, {\n        appear: true,\n        show: isOpen,\n        as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n            as: \"div\",\n            className: \"relative z-50\",\n            onClose: onClose,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Transition.Child, {\n                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                    enter: \"ease-out duration-300\",\n                    enterFrom: \"opacity-0\",\n                    enterTo: \"opacity-100\",\n                    leave: \"ease-in duration-200\",\n                    leaveFrom: \"opacity-100\",\n                    leaveTo: \"opacity-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-25\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex min-h-full items-start justify-center p-4 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0 scale-95\",\n                            enterTo: \"opacity-100 scale-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100 scale-100\",\n                            leaveTo: \"opacity-0 scale-95\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog.Panel, {\n                                className: \"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all dark:bg-gray-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog.Title, {\n                                                as: \"h3\",\n                                                className: \"text-lg font-medium leading-6 text-gray-900 dark:text-white\",\n                                                children: \"搜索产品\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onClose,\n                                                className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.XMarkIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearch,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.MagnifyingGlassIcon, {\n                                                            className: \"h-5 w-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\",\n                                                        placeholder: \"搜索产品名称、品牌或类别...\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 flex justify-end space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: onClose,\n                                                        className: \"btn btn-secondary\",\n                                                        children: \"取消\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        className: \"btn btn-primary\",\n                                                        disabled: !searchQuery.trim(),\n                                                        children: \"搜索\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-white mb-2\",\n                                                children: \"热门搜索\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    \"iPhone\",\n                                                    \"小米手机\",\n                                                    \"MacBook\",\n                                                    \"耳机\",\n                                                    \"充电器\"\n                                                ].map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSearchQuery(tag),\n                                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600\",\n                                                        children: tag\n                                                    }, tag, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\SearchModal.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SearchModal.tsx\n");

/***/ }),

/***/ "./src/components/UI/Dropdown.tsx":
/*!****************************************!*\
  !*** ./src/components/UI/Dropdown.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Menu,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n\n\n\nconst Dropdown = ({ trigger, items, align = \"right\", className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Menu, {\n        as: \"div\",\n        className: `relative inline-block text-left ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Menu.Button, {\n                    className: \"inline-flex w-full justify-center items-center\",\n                    children: trigger\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Transition, {\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                enter: \"transition ease-out duration-100\",\n                enterFrom: \"transform opacity-0 scale-95\",\n                enterTo: \"transform opacity-100 scale-100\",\n                leave: \"transition ease-in duration-75\",\n                leaveFrom: \"transform opacity-100 scale-100\",\n                leaveTo: \"transform opacity-0 scale-95\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Menu.Items, {\n                    className: `absolute z-10 mt-2 w-56 origin-top-${align} rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-1\",\n                        children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Menu.Item, {\n                                disabled: item.disabled,\n                                children: ({ active })=>item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: item.href,\n                                        className: `${active ? \"bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white\" : \"text-gray-700 dark:text-gray-300\"} group flex items-center px-4 py-2 text-sm ${item.disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            item.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: item.onClick,\n                                        className: `${active ? \"bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white\" : \"text-gray-700 dark:text-gray-300\"} group flex w-full items-center px-4 py-2 text-sm ${item.disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                        disabled: item.disabled,\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            item.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 21\n                                    }, undefined)\n                            }, index, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\components\\\\UI\\\\Dropdown.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dropdown);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/UI/Dropdown.tsx\n");

/***/ }),

/***/ "./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRequireAdmin: () => (/* binding */ useRequireAdmin),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"./src/services/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_api__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([_services_api__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user;\n    // 初始化认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"auth_token\");\n                if (token) {\n                    await refreshUser();\n                }\n            } catch (error) {\n                console.error(\"认证初始化失败:\", error);\n                (0,_services_api__WEBPACK_IMPORTED_MODULE_3__.clearAuthToken)();\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    // 登录\n    const login = async (credentials)=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/auth/login\", credentials);\n            const { user: userData, accessToken } = response;\n            // 保存token和用户信息\n            (0,_services_api__WEBPACK_IMPORTED_MODULE_3__.setAuthToken)(accessToken);\n            setUser(userData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"登录成功\");\n            // 重定向到仪表板\n            router.push(\"/dashboard\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"登录失败\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 注册\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/auth/register\", userData);\n            const { user: newUser, accessToken } = response;\n            // 保存token和用户信息\n            (0,_services_api__WEBPACK_IMPORTED_MODULE_3__.setAuthToken)(accessToken);\n            setUser(newUser);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"注册成功\");\n            // 重定向到仪表板\n            router.push(\"/dashboard\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"注册失败\");\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 登出\n    const logout = async ()=>{\n        try {\n            await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/auth/logout\");\n        } catch (error) {\n            console.error(\"登出请求失败:\", error);\n        } finally{\n            // 清除本地状态\n            (0,_services_api__WEBPACK_IMPORTED_MODULE_3__.clearAuthToken)();\n            setUser(null);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"已退出登录\");\n            // 重定向到登录页\n            router.push(\"/auth/login\");\n        }\n    };\n    // 刷新用户信息\n    const refreshUser = async ()=>{\n        try {\n            const userData = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/auth/me\");\n            setUser(userData);\n        } catch (error) {\n            console.error(\"获取用户信息失败:\", error);\n            // 如果是认证错误，清除token\n            if (error.status === 401) {\n                (0,_services_api__WEBPACK_IMPORTED_MODULE_3__.clearAuthToken)();\n                setUser(null);\n            }\n            throw error;\n        }\n    };\n    // 更新用户资料\n    const updateProfile = async (data)=>{\n        try {\n            const updatedUser = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(\"/users/profile\", data);\n            setUser(updatedUser);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"资料更新成功\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(error.message || \"资料更新失败\");\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        register,\n        logout,\n        refreshUser,\n        updateProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, undefined);\n};\n// 自定义Hook\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// 路由保护Hook\nconst useRequireAuth = ()=>{\n    const { isAuthenticated, isLoading } = useAuth();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 避免在登录页面重定向\n        if (!isLoading && !isAuthenticated && !router.pathname.startsWith(\"/auth\")) {\n            router.push(\"/auth/login\");\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    return {\n        isAuthenticated,\n        isLoading\n    };\n};\n// 管理员权限Hook\nconst useRequireAdmin = ()=>{\n    const { user, isLoading } = useAuth();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && (!user || user.role !== \"admin\")) {\n            router.push(\"/dashboard\");\n        }\n    }, [\n        user,\n        isLoading,\n        router\n    ]);\n    return {\n        isAdmin: user?.role === \"admin\",\n        isLoading\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "./src/contexts/NotificationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/NotificationContext.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications),\n/* harmony export */   useNotify: () => (/* binding */ useNotify)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"./src/services/api.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"./src/contexts/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_api__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_services_api__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst NotificationProvider = ({ children })=>{\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    // 添加通知\n    const addNotification = (notification)=>{\n        const newNotification = {\n            ...notification,\n            id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: new Date().toISOString(),\n            read: false\n        };\n        setNotifications((prev)=>[\n                newNotification,\n                ...prev\n            ]);\n        // 如果是重要通知，显示浏览器通知\n        if (notification.type === \"error\" || notification.type === \"warning\") {\n            showBrowserNotification(newNotification);\n        }\n    };\n    // 显示浏览器通知\n    const showBrowserNotification = (notification)=>{\n        if (true) return;\n        if (Notification.permission === \"granted\") {\n            new Notification(notification.title, {\n                body: notification.message,\n                icon: \"/favicon.ico\",\n                tag: notification.id\n            });\n        } else if (Notification.permission !== \"denied\") {\n            Notification.requestPermission().then((permission)=>{\n                if (permission === \"granted\") {\n                    new Notification(notification.title, {\n                        body: notification.message,\n                        icon: \"/favicon.ico\",\n                        tag: notification.id\n                    });\n                }\n            });\n        }\n    };\n    // 标记为已读\n    const markAsRead = (id)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === id ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    // 标记全部为已读\n    const markAllAsRead = ()=>{\n        setNotifications((prev)=>prev.map((notification)=>({\n                    ...notification,\n                    read: true\n                })));\n    };\n    // 移除通知\n    const removeNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((notification)=>notification.id !== id));\n    };\n    // 清空所有通知\n    const clearAll = ()=>{\n        setNotifications([]);\n    };\n    // 刷新通知列表\n    const refreshNotifications = async ()=>{\n        if (!isAuthenticated) return;\n        try {\n            setIsLoading(true);\n            const data = await _services_api__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/notifications\");\n            setNotifications(data.notifications || []);\n        } catch (error) {\n            console.error(\"获取通知失败:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 初始化通知\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated) {\n            refreshNotifications();\n        } else {\n            setNotifications([]);\n        }\n    }, [\n        isAuthenticated\n    ]);\n    // 定期刷新通知\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) return;\n        const interval = setInterval(()=>{\n            refreshNotifications();\n        }, 30000); // 30秒刷新一次\n        return ()=>clearInterval(interval);\n    }, [\n        isAuthenticated\n    ]);\n    // 请求通知权限\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, []);\n    const value = {\n        notifications,\n        unreadCount,\n        isLoading,\n        addNotification,\n        markAsRead,\n        markAllAsRead,\n        removeNotification,\n        clearAll,\n        refreshNotifications\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n// 自定义Hook\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (context === undefined) {\n        throw new Error(\"useNotifications must be used within a NotificationProvider\");\n    }\n    return context;\n};\n// 便捷的通知Hook\nconst useNotify = ()=>{\n    const { addNotification } = useNotifications();\n    return {\n        success: (title, message, action)=>addNotification({\n                type: \"success\",\n                title,\n                message,\n                action\n            }),\n        error: (title, message, action)=>addNotification({\n                type: \"error\",\n                title,\n                message,\n                action\n            }),\n        warning: (title, message, action)=>addNotification({\n                type: \"warning\",\n                title,\n                message,\n                action\n            }),\n        info: (title, message, action)=>addNotification({\n                type: \"info\",\n                title,\n                message,\n                action\n            })\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/NotificationContext.tsx\n");

/***/ }),

/***/ "./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ThemeProvider = ({ children })=>{\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [actualTheme, setActualTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    // 获取系统主题\n    const getSystemTheme = ()=>{\n        if (true) return \"light\";\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    };\n    // 计算实际主题\n    const calculateActualTheme = (currentTheme)=>{\n        if (currentTheme === \"auto\") {\n            return getSystemTheme();\n        }\n        return currentTheme;\n    };\n    // 应用主题到DOM\n    const applyTheme = (themeToApply)=>{\n        if (true) return;\n        const root = window.document.documentElement;\n        if (themeToApply === \"dark\") {\n            root.classList.add(\"dark\");\n        } else {\n            root.classList.remove(\"dark\");\n        }\n        // 更新meta标签\n        const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n        if (metaThemeColor) {\n            metaThemeColor.setAttribute(\"content\", themeToApply === \"dark\" ? \"#1f2937\" : \"#ffffff\");\n        }\n    };\n    // 设置主题\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        // 保存到localStorage\n        if (false) {}\n        const newActualTheme = calculateActualTheme(newTheme);\n        setActualTheme(newActualTheme);\n        applyTheme(newActualTheme);\n    };\n    // 切换主题\n    const toggleTheme = ()=>{\n        const newTheme = actualTheme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n    };\n    // 初始化主题\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) return;\n        // 从localStorage获取保存的主题\n        const savedTheme = localStorage.getItem(\"theme\");\n        const initialTheme = savedTheme || \"light\";\n        setThemeState(initialTheme);\n        const initialActualTheme = calculateActualTheme(initialTheme);\n        setActualTheme(initialActualTheme);\n        applyTheme(initialActualTheme);\n        // 监听系统主题变化\n        const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        const handleSystemThemeChange = ()=>{\n            if (theme === \"auto\") {\n                const newActualTheme = getSystemTheme();\n                setActualTheme(newActualTheme);\n                applyTheme(newActualTheme);\n            }\n        };\n        mediaQuery.addEventListener(\"change\", handleSystemThemeChange);\n        return ()=>{\n            mediaQuery.removeEventListener(\"change\", handleSystemThemeChange);\n        };\n    }, [\n        theme\n    ]);\n    const value = {\n        theme,\n        actualTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n// 自定义Hook\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout/index.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_6__, _components_Layout__WEBPACK_IMPORTED_MODULE_7__]);\n([_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_6__, _components_Layout__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';\n\n\n\n\n\n\n\n// 创建React Query客户端\nconst createQueryClient = ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n        defaultOptions: {\n            queries: {\n                retry: 3,\n                retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n                staleTime: 5 * 60 * 1000,\n                gcTime: 10 * 60 * 1000,\n                refetchOnWindowFocus: false,\n                refetchOnReconnect: true\n            },\n            mutations: {\n                retry: 1\n            }\n        }\n    });\nfunction App({ Component, pageProps }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>createQueryClient());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 模拟应用初始化\n        const initializeApp = async ()=>{\n            try {\n                // 这里可以添加应用初始化逻辑\n                // 例如：检查认证状态、加载用户配置等\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            } catch (error) {\n                console.error(\"应用初始化失败:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeApp();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 52,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_6__.NotificationProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                ...pageProps\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"14px\"\n                                },\n                                success: {\n                                    style: {\n                                        background: \"#10b981\"\n                                    }\n                                },\n                                error: {\n                                    style: {\n                                        background: \"#ef4444\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/dashboard.tsx":
/*!*********************************!*\
  !*** ./src/pages/dashboard.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout/index.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,ShoppingBagIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,CloudArrowUpIcon,ShoppingBagIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _components_Layout__WEBPACK_IMPORTED_MODULE_4__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _components_Layout__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst DashboardPage = ()=>{\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__.LoadingScreen, {\n            message: \"加载仪表板...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n            lineNumber: 16,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null; // useRequireAuth会处理重定向\n    }\n    const stats = [\n        {\n            name: \"总产品数\",\n            value: \"12\",\n            icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ShoppingBagIcon,\n            change: \"+4.75%\",\n            changeType: \"positive\"\n        },\n        {\n            name: \"分析报告\",\n            value: \"8\",\n            icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon,\n            change: \"+54.02%\",\n            changeType: \"positive\"\n        },\n        {\n            name: \"导入任务\",\n            value: \"3\",\n            icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CloudArrowUpIcon,\n            change: \"-1.39%\",\n            changeType: \"negative\"\n        },\n        {\n            name: \"活跃用户\",\n            value: \"2\",\n            icon: _barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserGroupIcon,\n            change: \"+10.18%\",\n            changeType: \"positive\"\n        }\n    ];\n    const recentActivities = [\n        {\n            id: 1,\n            type: \"import\",\n            title: \"数据导入完成\",\n            description: \"成功导入 15 个产品数据\",\n            time: \"2 小时前\",\n            status: \"success\"\n        },\n        {\n            id: 2,\n            type: \"analysis\",\n            title: \"AI分析完成\",\n            description: \"iPhone 15 Pro Max 市场分析报告已生成\",\n            time: \"4 小时前\",\n            status: \"success\"\n        },\n        {\n            id: 3,\n            type: \"product\",\n            title: \"新产品添加\",\n            description: \"小米14 Ultra 已添加到产品库\",\n            time: \"1 天前\",\n            status: \"info\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                            children: \"仪表板\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"欢迎回来！这里是您的数据概览。\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                    children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative bg-white dark:bg-gray-800 pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bg-primary-500 rounded-md p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"h-6 w-6 text-white\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-16 text-sm font-medium text-gray-500 dark:text-gray-400 truncate\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                    className: \"ml-16 pb-6 flex items-baseline sm:pb-7\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-semibold text-gray-900 dark:text-white\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `ml-2 flex items-baseline text-sm font-semibold ${stat.changeType === \"positive\" ? \"text-green-600 dark:text-green-400\" : \"text-red-600 dark:text-red-400\"}`,\n                                            children: stat.change\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, stat.name, true, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-5 sm:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg leading-6 font-medium text-gray-900 dark:text-white\",\n                                children: \"最近活动\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flow-root\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"-mb-8\",\n                                    children: recentActivities.map((activity, activityIdx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative pb-8\",\n                                                children: [\n                                                    activityIdx !== recentActivities.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 25\n                                                    }, undefined) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800 ${activity.status === \"success\" ? \"bg-green-500\" : activity.status === \"info\" ? \"bg-blue-500\" : \"bg-gray-500\"}`,\n                                                                    children: [\n                                                                        activity.type === \"import\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CloudArrowUpIcon, {\n                                                                            className: \"h-5 w-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                            lineNumber: 154,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        activity.type === \"analysis\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon, {\n                                                                            className: \"h-5 w-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                            lineNumber: 157,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        activity.type === \"product\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ShoppingBagIcon, {\n                                                                            className: \"h-5 w-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                            lineNumber: 160,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-900 dark:text-white\",\n                                                                                children: activity.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                                lineNumber: 166,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                                children: activity.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                                lineNumber: 169,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                                            children: activity.time\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, activity.id, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 shadow rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-5 sm:p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg leading-6 font-medium text-gray-900 dark:text-white\",\n                                children: \"快速操作\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"relative group bg-white dark:bg-gray-700 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-primary-500 dark:hover:border-primary-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"rounded-lg inline-flex p-3 bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400 ring-4 ring-white dark:ring-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CloudArrowUpIcon, {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                        children: \"导入数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-2 text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"上传Excel或CSV文件批量导入产品数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"relative group bg-white dark:bg-gray-700 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-primary-500 dark:hover:border-primary-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"rounded-lg inline-flex p-3 bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400 ring-4 ring-white dark:ring-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon, {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                        children: \"AI分析\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-2 text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"使用AI分析产品市场竞争力和选品建议\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"relative group bg-white dark:bg-gray-700 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-primary-500 dark:hover:border-primary-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"rounded-lg inline-flex p-3 bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400 ring-4 ring-white dark:ring-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_CloudArrowUpIcon_ShoppingBagIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ShoppingBagIcon, {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                        children: \"产品管理\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-2 text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"查看和管理您的产品库存和信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\dashboard.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardPage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/dashboard.tsx\n");

/***/ }),

/***/ "./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   batch: () => (/* binding */ batch),\n/* harmony export */   clearAuthToken: () => (/* binding */ clearAuthToken),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   del: () => (/* binding */ del),\n/* harmony export */   download: () => (/* binding */ download),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   healthCheck: () => (/* binding */ healthCheck),\n/* harmony export */   patch: () => (/* binding */ patch),\n/* harmony export */   post: () => (/* binding */ post),\n/* harmony export */   put: () => (/* binding */ put),\n/* harmony export */   setAuthToken: () => (/* binding */ setAuthToken),\n/* harmony export */   upload: () => (/* binding */ upload),\n/* harmony export */   withRetry: () => (/* binding */ withRetry)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// API配置\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_PREFIX = \"/api/v1\";\n// 创建axios实例\nconst createApiInstance = ()=>{\n    const instance = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: `${API_BASE_URL}${API_PREFIX}`,\n        timeout: 30000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // 请求拦截器\n    instance.interceptors.request.use((config)=>{\n        // 添加认证token\n        const token = getAuthToken();\n        if (token) {\n            config.headers.Authorization = `Bearer ${token}`;\n        }\n        // 添加请求ID用于追踪\n        config.headers[\"X-Request-ID\"] = generateRequestId();\n        // 记录请求日志\n        console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {\n            params: config.params,\n            data: config.data\n        });\n        return config;\n    }, (error)=>{\n        console.error(\"[API Request Error]\", error);\n        return Promise.reject(error);\n    });\n    // 响应拦截器\n    instance.interceptors.response.use((response)=>{\n        // 记录响应日志\n        console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {\n            status: response.status,\n            data: response.data\n        });\n        return response;\n    }, (error)=>{\n        console.error(\"[API Response Error]\", error);\n        // 处理认证错误\n        if (error.response?.status === 401) {\n            handleAuthError();\n        }\n        // 处理网络错误\n        if (!error.response) {\n            return Promise.reject({\n                message: \"网络连接失败，请检查网络设置\",\n                code: \"NETWORK_ERROR\"\n            });\n        }\n        // 返回标准化错误\n        const errorData = error.response.data;\n        return Promise.reject({\n            message: errorData?.error || errorData?.message || \"请求失败\",\n            code: errorData?.code || \"REQUEST_FAILED\",\n            status: error.response.status,\n            details: errorData?.details\n        });\n    });\n    return instance;\n};\n// 获取认证token\nconst getAuthToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"auth_token\");\n};\n// 设置认证token\nconst setAuthToken = (token)=>{\n    if (true) return;\n    localStorage.setItem(\"auth_token\", token);\n};\n// 清除认证token\nconst clearAuthToken = ()=>{\n    if (true) return;\n    localStorage.removeItem(\"auth_token\");\n};\n// 处理认证错误\nconst handleAuthError = ()=>{\n    clearAuthToken();\n    // 重定向到登录页面\n    if (false) {}\n};\n// 生成请求ID\nconst generateRequestId = ()=>{\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n};\n// 创建API实例\nconst api = createApiInstance();\n// API服务类\nclass ApiService {\n    constructor(){\n        this.instance = api;\n    }\n    // 通用请求方法\n    async request(config) {\n        try {\n            const response = await this.instance.request(config);\n            return response.data.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // GET请求\n    async get(url, params) {\n        return this.request({\n            method: \"GET\",\n            url,\n            params\n        });\n    }\n    // POST请求\n    async post(url, data) {\n        return this.request({\n            method: \"POST\",\n            url,\n            data\n        });\n    }\n    // PUT请求\n    async put(url, data) {\n        return this.request({\n            method: \"PUT\",\n            url,\n            data\n        });\n    }\n    // PATCH请求\n    async patch(url, data) {\n        return this.request({\n            method: \"PATCH\",\n            url,\n            data\n        });\n    }\n    // DELETE请求\n    async delete(url) {\n        return this.request({\n            method: \"DELETE\",\n            url\n        });\n    }\n    // 文件上传\n    async upload(url, file, onProgress) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return this.request({\n            method: \"POST\",\n            url,\n            data: formData,\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            },\n            onUploadProgress: (progressEvent)=>{\n                if (onProgress && progressEvent.total) {\n                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    onProgress(progress);\n                }\n            }\n        });\n    }\n    // 下载文件\n    async download(url, filename) {\n        try {\n            const response = await this.instance.request({\n                method: \"GET\",\n                url,\n                responseType: \"blob\"\n            });\n            // 创建下载链接\n            const blob = new Blob([\n                response.data\n            ]);\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const link = document.createElement(\"a\");\n            link.href = downloadUrl;\n            link.download = filename || \"download\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(downloadUrl);\n        } catch (error) {\n            throw error;\n        }\n    }\n    // 批量请求\n    async batch(requests) {\n        try {\n            const promises = requests.map((config)=>this.instance.request(config));\n            const responses = await Promise.all(promises);\n            return responses.map((response)=>response.data.data);\n        } catch (error) {\n            throw error;\n        }\n    }\n    // 取消请求\n    createCancelToken() {\n        return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].CancelToken.source();\n    }\n    // 检查请求是否被取消\n    isCancel(error) {\n        return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isCancel(error);\n    }\n}\n// 创建API服务实例\nconst apiService = new ApiService();\n// 导出API服务\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiService);\n// 导出常用方法\nconst { get, post, put, patch, delete: del, upload, download, batch } = apiService;\n// 导出实例用于高级用法\n\n// 健康检查\nconst healthCheck = async ()=>{\n    try {\n        await get(\"/health\");\n        return true;\n    } catch (error) {\n        return false;\n    }\n};\n// 重试机制\nconst withRetry = async (fn, maxRetries = 3, delay = 1000)=>{\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (i === maxRetries) {\n                break;\n            }\n            // 等待后重试\n            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n        }\n    }\n    throw lastError;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/api.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@headlessui","vendor-chunks/@heroicons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();