/* 现代化UI样式系统 */

/* CSS变量定义 */
:root {
  /* 颜色系统 */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;

  /* 间距系统 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;

  /* 圆角系统 */
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 字体系统 */
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* 图标尺寸系统 */
  --icon-xs: 0.75rem;
  --icon-sm: 1rem;
  --icon-base: 1.25rem;
  --icon-lg: 1.5rem;
  --icon-xl: 2rem;

  /* 过渡动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

body {
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
  min-height: 100vh;
}

#__next {
  min-height: 100vh;
}

/* 现代化按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25;
  border-radius: var(--radius-lg);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  outline: none;
  position: relative;
  min-height: 2.75rem;
  white-space: nowrap;
  user-select: none;
}

.btn:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮变体 */
.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: white;
  border-color: var(--color-primary-500);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background: white;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: var(--color-gray-700);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-gray-100);
  color: var(--color-gray-900);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: 0.75rem;
  min-height: 2rem;
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: 1rem;
  min-height: 3rem;
}

/* 现代化输入框组件 */
.input-group {
  position: relative;
  width: 100%;
}

.input {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-gray-900);
  background: white;
  border: 1.5px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  outline: none;
  min-height: 2.75rem;
}

.input::placeholder {
  color: var(--color-gray-400);
}

.input:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:invalid {
  border-color: var(--color-error-500);
}

.input:invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 浮动标签效果 */
.input-floating {
  padding-top: var(--spacing-6);
  padding-bottom: var(--spacing-2);
}

.input-floating + .input-label {
  position: absolute;
  top: 0;
  left: var(--spacing-4);
  padding: 0 var(--spacing-1);
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600);
  background: white;
  transform: translateY(0.75rem);
  transition: all var(--transition-fast);
  pointer-events: none;
}

.input-floating:focus + .input-label,
.input-floating:not(:placeholder-shown) + .input-label {
  transform: translateY(-0.5rem);
  font-size: 0.625rem;
  color: var(--color-primary-600);
}

/* 现代化卡片组件 */
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  transition: all var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: linear-gradient(135deg, var(--color-gray-50), white);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
}

/* 现代化徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-3);
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  background: var(--color-primary-100);
  color: var(--color-primary-800);
}

.badge-success {
  background: #d1fae5;
  color: #065f46;
}

.badge-warning {
  background: #fef3c7;
  color: #92400e;
}

.badge-error {
  background: #fee2e2;
  color: #991b1b;
}

/* 现代化加载动画 */
.loading-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--color-gray-200);
  border-top: 2px solid var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 现代化模态框 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.modal-content {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  max-width: 32rem;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.95);
  transition: all var(--transition-base);
}

.modal-content.show {
  transform: scale(1);
}

/* 现代化导航 */
.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--color-gray-700);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-weight: 500;
}

.nav-link:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-900);
}

.nav-link.active {
  background: var(--color-primary-100);
  color: var(--color-primary-700);
}

/* 现代化表格 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-base);
}

.table th {
  background: var(--color-gray-50);
  padding: var(--spacing-4);
  text-align: left;
  font-weight: 600;
  color: var(--color-gray-700);
  border-bottom: 1px solid var(--color-gray-200);
}

.table td {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--color-gray-100);
}

.table tr:hover {
  background: var(--color-gray-50);
}

/* 现代化表单 */
.form-group {
  margin-bottom: var(--spacing-6);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.form-error {
  margin-top: var(--spacing-1);
  font-size: 0.75rem;
  color: var(--color-error-600);
}

/* 现代化工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-gray-900);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-fast);
  z-index: 10;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 图标尺寸类 */
.icon-xs {
  width: var(--icon-xs);
  height: var(--icon-xs);
}

.icon-sm {
  width: var(--icon-sm);
  height: var(--icon-sm);
}

.icon-base {
  width: var(--icon-base);
  height: var(--icon-base);
}

.icon-lg {
  width: var(--icon-lg);
  height: var(--icon-lg);
}

.icon-xl {
  width: var(--icon-xl);
  height: var(--icon-xl);
}

/* 图标容器 */
.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-container-sm {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-md);
}

.icon-container-md {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
}

.icon-container-lg {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
}
