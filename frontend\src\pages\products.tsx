import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

const ProductsPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const [searchTerm, setSearchTerm] = useState('');

  if (isLoading) {
    return <LoadingScreen message="加载产品管理..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 模拟产品数据
  const products = [
    {
      id: 1,
      name: 'iPhone 15 Pro Max',
      brand: 'Apple',
      category: '手机',
      price: 9999,
      stock: 50,
      status: '在售',
      image: '/api/placeholder/100/100',
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      name: '小米14 Ultra',
      brand: '小米',
      category: '手机',
      price: 5999,
      stock: 30,
      status: '在售',
      image: '/api/placeholder/100/100',
      createdAt: '2024-01-10'
    },
    {
      id: 3,
      name: 'MacBook Pro M3',
      brand: 'Apple',
      category: '笔记本',
      price: 14999,
      stock: 15,
      status: '在售',
      image: '/api/placeholder/100/100',
      createdAt: '2024-01-08'
    },
    {
      id: 4,
      name: 'AirPods Pro 2',
      brand: 'Apple',
      category: '耳机',
      price: 1899,
      stock: 0,
      status: '缺货',
      image: '/api/placeholder/100/100',
      createdAt: '2024-01-05'
    }
  ];

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.brand.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.5rem'
                }}>
                  产品管理
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  管理您的产品库存和信息
                </p>
              </div>
              <button className="btn btn-primary">
                <PlusIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                添加产品
              </button>
            </div>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              gap: '1rem',
              alignItems: 'center'
            }}>
              <div style={{ flex: 1, position: 'relative' }}>
                <MagnifyingGlassIcon style={{
                  position: 'absolute',
                  left: '0.75rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: '1.25rem',
                  height: '1.25rem',
                  color: 'var(--color-gray-400)'
                }} />
                <input
                  type="text"
                  className="input"
                  placeholder="搜索产品名称或品牌..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ paddingLeft: '2.5rem' }}
                />
              </div>
              <button className="btn btn-secondary">
                <FunnelIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                筛选
              </button>
            </div>
          </div>
        </div>

        {/* 产品列表 */}
        <div className="card">
          <div className="card-body">
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
              gap: '1.5rem'
            }}>
              {filteredProducts.map((product) => (
                <div key={product.id} className="card">
                  <div className="card-body">
                    <div style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: '1rem'
                    }}>
                      <div style={{
                        width: '4rem',
                        height: '4rem',
                        background: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-lg)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.75rem',
                        color: 'var(--color-gray-500)'
                      }}>
                        图片
                      </div>
                      
                      <div style={{ flex: 1 }}>
                        <h3 style={{
                          fontSize: '1.125rem',
                          fontWeight: '600',
                          color: 'var(--color-gray-900)',
                          marginBottom: '0.25rem'
                        }}>
                          {product.name}
                        </h3>
                        
                        <p style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '0.5rem'
                        }}>
                          {product.brand} · {product.category}
                        </p>
                        
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '1rem'
                        }}>
                          <span style={{
                            fontSize: '1.25rem',
                            fontWeight: '700',
                            color: 'var(--color-primary-600)'
                          }}>
                            ¥{product.price.toLocaleString()}
                          </span>
                          
                          <span className={`badge ${product.status === '在售' ? 'badge-success' : 'badge-error'}`}>
                            {product.status}
                          </span>
                        </div>
                        
                        <div style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '1rem'
                        }}>
                          库存: {product.stock} 件
                        </div>
                        
                        <div style={{
                          display: 'flex',
                          gap: '0.5rem'
                        }}>
                          <button className="btn btn-ghost btn-sm">
                            <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                            查看
                          </button>
                          <button className="btn btn-ghost btn-sm">
                            <PencilIcon style={{ width: '1rem', height: '1rem' }} />
                            编辑
                          </button>
                          <button className="btn btn-ghost btn-sm" style={{ color: 'var(--color-error-600)' }}>
                            <TrashIcon style={{ width: '1rem', height: '1rem' }} />
                            删除
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredProducts.length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: 'var(--color-gray-500)'
              }}>
                <p>没有找到匹配的产品</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;
