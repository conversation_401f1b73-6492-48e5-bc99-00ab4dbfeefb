const XLSX = require('xlsx');
const csv = require('csv-parser');
const fs = require('fs').promises;
const path = require('path');
const { createError } = require('../middleware/errorHandler');
const { prisma } = require('../config/database');
const logger = require('../utils/logger');
const templateService = require('./templateService');

class ImportService {
  constructor() {
    this.supportedFormats = ['xlsx', 'xls', 'csv'];
    this.maxFileSize = 50 * 1024 * 1024; // 50MB
    this.maxRows = 10000; // 最大行数
  }

  /**
   * 验证上传文件
   */
  validateFile(file) {
    const errors = [];
    
    // 检查文件大小
    if (file.size > this.maxFileSize) {
      errors.push(`文件大小不能超过${this.maxFileSize / 1024 / 1024}MB`);
    }
    
    // 检查文件格式
    const ext = path.extname(file.originalname).toLowerCase().slice(1);
    if (!this.supportedFormats.includes(ext)) {
      errors.push(`不支持的文件格式，请使用：${this.supportedFormats.join(', ')}`);
    }
    
    return errors;
  }

  /**
   * 解析Excel文件
   */
  async parseExcelFile(filePath) {
    try {
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      // 转换为JSON
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (data.length === 0) {
        throw createError.badRequest('文件为空', 'EMPTY_FILE');
      }
      
      const headers = data[0];
      const rows = data.slice(1);
      
      return { headers, rows };
    } catch (error) {
      logger.error('解析Excel文件失败:', error);
      throw createError.badRequest('文件格式错误或文件损坏', 'INVALID_FILE_FORMAT');
    }
  }

  /**
   * 解析CSV文件
   */
  async parseCSVFile(filePath) {
    try {
      const fileContent = await fs.readFile(filePath, 'utf8');
      const lines = fileContent.split('\n').filter(line => line.trim());
      
      if (lines.length === 0) {
        throw createError.badRequest('文件为空', 'EMPTY_FILE');
      }
      
      // 解析CSV头部
      const headers = this.parseCSVLine(lines[0]);
      const rows = lines.slice(1).map(line => this.parseCSVLine(line));
      
      return { headers, rows };
    } catch (error) {
      logger.error('解析CSV文件失败:', error);
      throw createError.badRequest('CSV文件格式错误', 'INVALID_CSV_FORMAT');
    }
  }

  /**
   * 解析CSV行
   */
  parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  }

  /**
   * 映射字段
   */
  mapFields(headers) {
    const templateFields = templateService.getTemplateFields();
    const allFields = [...templateFields.required, ...templateFields.optional];
    
    const fieldMapping = {};
    const unmappedHeaders = [];
    
    headers.forEach((header, index) => {
      const cleanHeader = header.replace('*', '').trim();
      const field = allFields.find(f => 
        f.name === cleanHeader || 
        f.key === cleanHeader ||
        f.name.toLowerCase() === cleanHeader.toLowerCase()
      );
      
      if (field) {
        fieldMapping[index] = field;
      } else {
        unmappedHeaders.push({ index, header });
      }
    });
    
    return { fieldMapping, unmappedHeaders };
  }

  /**
   * 验证数据行
   */
  validateRow(row, fieldMapping, rowIndex) {
    const errors = [];
    const validatedData = {};
    const templateFields = templateService.getTemplateFields();
    
    // 验证映射的字段
    Object.entries(fieldMapping).forEach(([columnIndex, field]) => {
      const value = row[columnIndex];
      const fieldErrors = templateService.validateFieldValue(field, value);
      
      if (fieldErrors.length > 0) {
        errors.push(...fieldErrors.map(error => `第${rowIndex + 2}行，${error}`));
      } else {
        validatedData[field.key] = this.convertValue(value, field);
      }
    });
    
    // 检查必填字段
    templateFields.required.forEach(field => {
      if (!(field.key in validatedData)) {
        errors.push(`第${rowIndex + 2}行，缺少必填字段：${field.name}`);
      }
    });
    
    // 设置默认值
    templateFields.optional.forEach(field => {
      if (!(field.key in validatedData) && field.default !== undefined) {
        validatedData[field.key] = field.default;
      }
    });
    
    return { errors, data: validatedData };
  }

  /**
   * 转换值类型
   */
  convertValue(value, field) {
    if (value === null || value === undefined || value === '') {
      return null;
    }
    
    switch (field.type) {
      case 'number':
        return parseFloat(value);
      case 'integer':
        return parseInt(value);
      case 'string':
      case 'url':
      case 'enum':
      default:
        return String(value).trim();
    }
  }

  /**
   * 处理导入任务
   */
  async processImport(importTaskId, filePath, userId) {
    try {
      // 更新任务状态
      await prisma.importTask.update({
        where: { id: importTaskId },
        data: {
          status: 'PROCESSING',
          startedAt: new Date()
        }
      });
      
      // 获取文件扩展名
      const ext = path.extname(filePath).toLowerCase().slice(1);
      
      // 解析文件
      let headers, rows;
      if (ext === 'csv') {
        ({ headers, rows } = await this.parseCSVFile(filePath));
      } else {
        ({ headers, rows } = await this.parseExcelFile(filePath));
      }
      
      // 检查行数限制
      if (rows.length > this.maxRows) {
        throw createError.badRequest(`数据行数不能超过${this.maxRows}行`, 'TOO_MANY_ROWS');
      }
      
      // 映射字段
      const { fieldMapping, unmappedHeaders } = this.mapFields(headers);
      
      // 验证数据
      const validationResults = [];
      const validRows = [];
      const allErrors = [];
      
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        
        // 跳过空行
        if (row.every(cell => !cell || cell.toString().trim() === '')) {
          continue;
        }
        
        const { errors, data } = this.validateRow(row, fieldMapping, i);
        
        if (errors.length > 0) {
          allErrors.push(...errors);
        } else {
          // 转换平台枚举值
          const platformMap = {
            'taobao': 'TAOBAO',
            'tmall': 'TMALL',
            'jd': 'JD',
            'pdd': 'PDD',
            'manual': 'MANUAL',
            'import': 'IMPORT'
          };
          
          validRows.push({
            ...data,
            userId: userId,
            importTaskId: importTaskId,
            dataSource: 'IMPORT',
            platform: platformMap[data.platform] || 'MANUAL',
            tags: data.tags ? data.tags.split(',').map(tag => tag.trim()) : []
          });
        }
        
        validationResults.push({
          rowIndex: i,
          errors,
          data: errors.length === 0 ? data : null
        });
      }
      
      // 如果有验证错误，记录但继续处理有效数据
      if (allErrors.length > 0) {
        logger.warn(`导入任务 ${importTaskId} 存在验证错误:`, allErrors);
      }
      
      // 插入有效数据
      let insertedCount = 0;
      if (validRows.length > 0) {
        // 批量插入产品数据
        const insertedProducts = await prisma.product.createMany({
          data: validRows,
          skipDuplicates: true
        });
        insertedCount = insertedProducts.count;
      }
      
      // 更新任务状态
      await prisma.importTask.update({
        where: { id: importTaskId },
        data: {
          status: allErrors.length > 0 && validRows.length === 0 ? 'FAILED' : 'COMPLETED',
          completedAt: new Date(),
          totalRows: rows.length,
          processedRows: validationResults.length,
          successRows: insertedCount,
          failedRows: rows.length - insertedCount,
          validationErrors: allErrors,
          importSummary: {
            totalRows: rows.length,
            validRows: validRows.length,
            insertedRows: insertedCount,
            errorRows: rows.length - insertedCount,
            unmappedHeaders,
            fieldMapping: Object.keys(fieldMapping).length
          }
        }
      });
      
      // 清理临时文件
      try {
        await fs.unlink(filePath);
      } catch (error) {
        logger.warn('清理临时文件失败:', error);
      }
      
      return {
        success: true,
        totalRows: rows.length,
        successRows: insertedCount,
        failedRows: rows.length - insertedCount,
        errors: allErrors
      };
      
    } catch (error) {
      // 更新任务失败状态
      await prisma.importTask.update({
        where: { id: importTaskId },
        data: {
          status: 'FAILED',
          completedAt: new Date(),
          errorMessage: error.message,
          errorDetails: {
            stack: error.stack,
            timestamp: new Date().toISOString()
          }
        }
      });
      
      logger.error('数据导入失败:', error);
      throw error;
    }
  }

  /**
   * 获取导入任务状态
   */
  async getImportTaskStatus(taskId, userId) {
    const task = await prisma.importTask.findFirst({
      where: { 
        id: taskId, 
        userId: userId 
      }
    });
    
    if (!task) {
      throw createError.notFound('导入任务不存在', 'TASK_NOT_FOUND');
    }
    
    return task;
  }

  /**
   * 获取用户的导入任务列表
   */
  async getUserImportTasks(userId, options = {}) {
    const { page = 1, limit = 20, status } = options;
    const skip = (page - 1) * limit;
    
    const where = { userId };
    if (status) {
      where.status = status.toUpperCase();
    }
    
    const [tasks, totalCount] = await Promise.all([
      prisma.importTask.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.importTask.count({ where })
    ]);
    
    return {
      tasks,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    };
  }
}

module.exports = new ImportService();
