/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/analysis"],{

/***/ "__barrel_optimize__?names=ChartBarIcon,DocumentArrowDownIcon,EyeIcon,SparklesIcon,TrendingDownIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChartBarIcon,DocumentArrowDownIcon,EyeIcon,SparklesIcon,TrendingDownIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChartBarIcon: function() { return /* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   DocumentArrowDownIcon: function() { return /* reexport safe */ _DocumentArrowDownIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   EyeIcon: function() { return /* reexport safe */ _EyeIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   SparklesIcon: function() { return /* reexport safe */ _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _DocumentArrowDownIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DocumentArrowDownIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\");\n/* harmony import */ var _EyeIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EyeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _SparklesIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SparklesIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGFydEJhckljb24sRG9jdW1lbnRBcnJvd0Rvd25JY29uLEV5ZUljb24sU3BhcmtsZXNJY29uLFRyZW5kaW5nRG93bkljb24sVHJlbmRpbmdVcEljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUMyRDtBQUNrQjtBQUM1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz84YTdlIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGFydEJhckljb24gfSBmcm9tIFwiLi9DaGFydEJhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBEb2N1bWVudEFycm93RG93bkljb24gfSBmcm9tIFwiLi9Eb2N1bWVudEFycm93RG93bkljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeWVJY29uIH0gZnJvbSBcIi4vRXllSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNwYXJrbGVzSWNvbiB9IGZyb20gXCIuL1NwYXJrbGVzSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChartBarIcon,DocumentArrowDownIcon,EyeIcon,SparklesIcon,TrendingDownIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/analysis\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/analysis.tsx */ \"./src/pages/analysis.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/analysis\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDYWktcHJvZHVjdC1zZWxlY3QlNUNmcm9udGVuZCU1Q3NyYyU1Q3BhZ2VzJTVDYW5hbHlzaXMudHN4JnBhZ2U9JTJGYW5hbHlzaXMhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMERBQTBCO0FBQ2pEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz83ZjgxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvYW5hbHlzaXNcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9hbmFseXNpcy50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2FuYWx5c2lzXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis!\n"));

/***/ }),

/***/ "./src/pages/analysis.tsx":
/*!********************************!*\
  !*** ./src/pages/analysis.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChartBarIcon,DocumentArrowDownIcon,EyeIcon,SparklesIcon,TrendingDownIcon,TrendingUpIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ChartBarIcon,DocumentArrowDownIcon,EyeIcon,SparklesIcon,TrendingDownIcon,TrendingUpIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst AnalysisPage = ()=>{\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            message: \"加载智能分析...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n            lineNumber: 18,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    // 模拟分析报告数据\n    const reports = [\n        {\n            id: 1,\n            title: \"iPhone 15 Pro Max 市场分析报告\",\n            category: \"手机\",\n            status: \"已完成\",\n            trend: \"up\",\n            score: 85,\n            createdAt: \"2024-01-15\",\n            summary: \"该产品在高端手机市场表现优异，建议加大库存投入\",\n            metrics: {\n                marketShare: \"15.2%\",\n                profitMargin: \"32%\",\n                competitorCount: 8\n            }\n        },\n        {\n            id: 2,\n            title: \"小米14 Ultra 竞品分析\",\n            category: \"手机\",\n            status: \"已完成\",\n            trend: \"up\",\n            score: 78,\n            createdAt: \"2024-01-12\",\n            summary: \"性价比优势明显，在中高端市场有较强竞争力\",\n            metrics: {\n                marketShare: \"12.8%\",\n                profitMargin: \"28%\",\n                competitorCount: 12\n            }\n        },\n        {\n            id: 3,\n            title: \"MacBook Pro M3 销售预测\",\n            category: \"笔记本\",\n            status: \"进行中\",\n            trend: \"up\",\n            score: 92,\n            createdAt: \"2024-01-10\",\n            summary: \"预计Q2销量将增长25%，建议提前备货\",\n            metrics: {\n                marketShare: \"8.5%\",\n                profitMargin: \"35%\",\n                competitorCount: 6\n            }\n        },\n        {\n            id: 4,\n            title: \"AirPods Pro 2 价格趋势分析\",\n            category: \"耳机\",\n            status: \"已完成\",\n            trend: \"down\",\n            score: 65,\n            createdAt: \"2024-01-08\",\n            summary: \"价格竞争激烈，建议调整定价策略\",\n            metrics: {\n                marketShare: \"22.1%\",\n                profitMargin: \"18%\",\n                competitorCount: 15\n            }\n        }\n    ];\n    const categories = [\n        \"all\",\n        \"手机\",\n        \"笔记本\",\n        \"耳机\"\n    ];\n    const filteredReports = selectedCategory === \"all\" ? reports : reports.filter((report)=>report.category === selectedCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            background: \"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)\",\n            padding: \"2rem 0\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: \"1200px\",\n                margin: \"0 auto\",\n                padding: \"0 1rem\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    style: {\n                        marginBottom: \"2rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                fontSize: \"2rem\",\n                                                fontWeight: \"700\",\n                                                color: \"var(--color-gray-900)\",\n                                                marginBottom: \"0.5rem\"\n                                            },\n                                            children: \"智能分析\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: \"1rem\",\n                                                color: \"var(--color-gray-600)\"\n                                            },\n                                            children: \"AI驱动的产品市场分析和预测\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.SparklesIcon, {\n                                            style: {\n                                                width: \"1.25rem\",\n                                                height: \"1.25rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"新建分析\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"grid\",\n                        gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                        gap: \"1.5rem\",\n                        marginBottom: \"2rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"1rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"3rem\",\n                                                height: \"3rem\",\n                                                background: \"linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))\",\n                                                borderRadius: \"var(--radius-lg)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChartBarIcon, {\n                                                style: {\n                                                    width: \"1.5rem\",\n                                                    height: \"1.5rem\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"2rem\",\n                                                        fontWeight: \"700\",\n                                                        color: \"var(--color-gray-900)\"\n                                                    },\n                                                    children: reports.length\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"0.875rem\",\n                                                        color: \"var(--color-gray-600)\"\n                                                    },\n                                                    children: \"分析报告\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"1rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"3rem\",\n                                                height: \"3rem\",\n                                                background: \"linear-gradient(135deg, var(--color-success-500), var(--color-success-600))\",\n                                                borderRadius: \"var(--radius-lg)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.TrendingUpIcon, {\n                                                style: {\n                                                    width: \"1.5rem\",\n                                                    height: \"1.5rem\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"2rem\",\n                                                        fontWeight: \"700\",\n                                                        color: \"var(--color-gray-900)\"\n                                                    },\n                                                    children: \"82%\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"0.875rem\",\n                                                        color: \"var(--color-gray-600)\"\n                                                    },\n                                                    children: \"平均准确率\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"1rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"3rem\",\n                                                height: \"3rem\",\n                                                background: \"linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600))\",\n                                                borderRadius: \"var(--radius-lg)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.SparklesIcon, {\n                                                style: {\n                                                    width: \"1.5rem\",\n                                                    height: \"1.5rem\",\n                                                    color: \"white\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"2rem\",\n                                                        fontWeight: \"700\",\n                                                        color: \"var(--color-gray-900)\"\n                                                    },\n                                                    children: \"15\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"0.875rem\",\n                                                        color: \"var(--color-gray-600)\"\n                                                    },\n                                                    children: \"AI模型\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    style: {\n                        marginBottom: \"2rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"0.5rem\"\n                            },\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn \".concat(selectedCategory === category ? \"btn-primary\" : \"btn-ghost\", \" btn-sm\"),\n                                    onClick: ()=>setSelectedCategory(category),\n                                    children: category === \"all\" ? \"全部\" : category\n                                }, category, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1rem\"\n                                },\n                                children: filteredReports.map((report)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"flex-start\",\n                                                        marginBottom: \"1rem\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                flex: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        gap: \"1rem\",\n                                                                        marginBottom: \"0.5rem\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            style: {\n                                                                                fontSize: \"1.25rem\",\n                                                                                fontWeight: \"600\",\n                                                                                color: \"var(--color-gray-900)\"\n                                                                            },\n                                                                            children: report.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"badge \".concat(report.status === \"已完成\" ? \"badge-success\" : \"badge-warning\"),\n                                                                            children: report.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        report.trend === \"up\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.TrendingUpIcon, {\n                                                                            style: {\n                                                                                width: \"1.25rem\",\n                                                                                height: \"1.25rem\",\n                                                                                color: \"var(--color-success-500)\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 266,\n                                                                            columnNumber: 29\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.TrendingDownIcon, {\n                                                                            style: {\n                                                                                width: \"1.25rem\",\n                                                                                height: \"1.25rem\",\n                                                                                color: \"var(--color-error-500)\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 268,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    style: {\n                                                                        fontSize: \"0.875rem\",\n                                                                        color: \"var(--color-gray-600)\",\n                                                                        marginBottom: \"1rem\"\n                                                                    },\n                                                                    children: report.summary\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: \"grid\",\n                                                                        gridTemplateColumns: \"repeat(auto-fit, minmax(150px, 1fr))\",\n                                                                        gap: \"1rem\",\n                                                                        marginBottom: \"1rem\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"0.75rem\",\n                                                                                        color: \"var(--color-gray-500)\"\n                                                                                    },\n                                                                                    children: \"市场份额\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 287,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    style: {\n                                                                                        fontSize: \"1rem\",\n                                                                                        fontWeight: \"600\",\n                                                                                        color: \"var(--color-gray-900)\"\n                                                                                    },\n                                                                                    children: report.metrics.marketShare\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 288,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"0.75rem\",\n                                                                                        color: \"var(--color-gray-500)\"\n                                                                                    },\n                                                                                    children: \"利润率\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 293,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    style: {\n                                                                                        fontSize: \"1rem\",\n                                                                                        fontWeight: \"600\",\n                                                                                        color: \"var(--color-gray-900)\"\n                                                                                    },\n                                                                                    children: report.metrics.profitMargin\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 294,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"0.75rem\",\n                                                                                        color: \"var(--color-gray-500)\"\n                                                                                    },\n                                                                                    children: \"竞品数量\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 299,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    style: {\n                                                                                        fontSize: \"1rem\",\n                                                                                        fontWeight: \"600\",\n                                                                                        color: \"var(--color-gray-900)\"\n                                                                                    },\n                                                                                    children: report.metrics.competitorCount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                gap: \"1rem\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: \"center\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: \"1.5rem\",\n                                                                            fontWeight: \"700\",\n                                                                            color: report.score >= 80 ? \"var(--color-success-600)\" : report.score >= 60 ? \"var(--color-warning-600)\" : \"var(--color-error-600)\"\n                                                                        },\n                                                                        children: report.score\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: \"0.75rem\",\n                                                                            color: \"var(--color-gray-500)\"\n                                                                        },\n                                                                        children: \"分析评分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"center\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                fontSize: \"0.875rem\",\n                                                                color: \"var(--color-gray-500)\"\n                                                            },\n                                                            children: [\n                                                                \"创建时间: \",\n                                                                report.createdAt\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"0.5rem\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"btn btn-ghost btn-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.EyeIcon, {\n                                                                            style: {\n                                                                                width: \"1rem\",\n                                                                                height: \"1rem\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"查看详情\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"btn btn-ghost btn-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChartBarIcon_DocumentArrowDownIcon_EyeIcon_SparklesIcon_TrendingDownIcon_TrendingUpIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DocumentArrowDownIcon, {\n                                                                            style: {\n                                                                                width: \"1rem\",\n                                                                                height: \"1rem\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"导出报告\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, report.id, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined),\n                            filteredReports.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    padding: \"3rem\",\n                                    color: \"var(--color-gray-500)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"暂无分析报告\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnalysisPage, \"Js/Fxi04VewjqzAAlAJHljwr7qg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth\n    ];\n});\n_c = AnalysisPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnalysisPage);\nvar _c;\n$RefreshReg$(_c, \"AnalysisPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/analysis.tsx\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js ***!
  \*******************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction DocumentArrowDownIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n    }));\n}\n_c = DocumentArrowDownIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DocumentArrowDownIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"DocumentArrowDownIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js ***!
  \*****************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction EyeIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n    }));\n}\n_c = EyeIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(EyeIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"EyeIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);