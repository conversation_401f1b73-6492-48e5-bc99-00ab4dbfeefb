import React from 'react';

const StyleTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">样式测试页面</h1>
          <p className="text-lg text-gray-600">验证所有CSS样式是否正确加载</p>
        </div>

        {/* 颜色测试 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">颜色系统测试</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-500 text-white p-4 rounded-lg text-center">
              <div className="text-sm font-medium">Primary Blue</div>
              <div className="text-xs opacity-75">bg-blue-500</div>
            </div>
            <div className="bg-gray-500 text-white p-4 rounded-lg text-center">
              <div className="text-sm font-medium">Gray</div>
              <div className="text-xs opacity-75">bg-gray-500</div>
            </div>
            <div className="bg-green-500 text-white p-4 rounded-lg text-center">
              <div className="text-sm font-medium">Success Green</div>
              <div className="text-xs opacity-75">bg-green-500</div>
            </div>
            <div className="bg-red-500 text-white p-4 rounded-lg text-center">
              <div className="text-sm font-medium">Error Red</div>
              <div className="text-xs opacity-75">bg-red-500</div>
            </div>
          </div>
        </div>

        {/* 按钮测试 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">按钮组件测试</h2>
          <div className="space-x-4">
            <button className="btn btn-primary">Primary Button</button>
            <button className="btn btn-secondary">Secondary Button</button>
            <button className="btn btn-primary" disabled>Disabled Button</button>
          </div>
        </div>

        {/* 表单测试 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">表单组件测试</h2>
          <div className="space-y-4 max-w-md">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                邮箱地址
              </label>
              <input
                type="email"
                className="input"
                placeholder="请输入邮箱地址"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                密码
              </label>
              <input
                type="password"
                className="input"
                placeholder="请输入密码"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                备注
              </label>
              <textarea
                className="input"
                rows={3}
                placeholder="请输入备注信息"
              />
            </div>
          </div>
        </div>

        {/* 布局测试 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">布局系统测试</h2>
          
          {/* Flexbox 测试 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-2">Flexbox 布局</h3>
            <div className="flex items-center justify-between bg-gray-100 p-4 rounded-lg">
              <div className="bg-blue-500 text-white px-4 py-2 rounded">Left</div>
              <div className="bg-green-500 text-white px-4 py-2 rounded">Center</div>
              <div className="bg-red-500 text-white px-4 py-2 rounded">Right</div>
            </div>
          </div>

          {/* Grid 测试 */}
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">Grid 布局</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-100 p-4 rounded-lg text-center">
                <div className="text-blue-800 font-medium">Grid Item 1</div>
              </div>
              <div className="bg-green-100 p-4 rounded-lg text-center">
                <div className="text-green-800 font-medium">Grid Item 2</div>
              </div>
              <div className="bg-red-100 p-4 rounded-lg text-center">
                <div className="text-red-800 font-medium">Grid Item 3</div>
              </div>
            </div>
          </div>
        </div>

        {/* 文本测试 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">文本样式测试</h2>
          <div className="space-y-2">
            <div className="text-xs text-gray-500">Extra Small Text (text-xs)</div>
            <div className="text-sm text-gray-600">Small Text (text-sm)</div>
            <div className="text-base text-gray-700">Base Text (text-base)</div>
            <div className="text-lg text-gray-800">Large Text (text-lg)</div>
            <div className="text-xl text-gray-900">Extra Large Text (text-xl)</div>
            <div className="text-2xl font-bold text-gray-900">2XL Bold Text (text-2xl font-bold)</div>
          </div>
        </div>

        {/* 间距测试 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">间距系统测试</h2>
          <div className="space-y-4">
            <div className="bg-blue-100 p-2 rounded">Padding 2 (p-2)</div>
            <div className="bg-green-100 p-4 rounded">Padding 4 (p-4)</div>
            <div className="bg-red-100 p-6 rounded">Padding 6 (p-6)</div>
          </div>
        </div>

        {/* 阴影测试 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">阴影效果测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg shadow-sm text-center">
              <div className="text-sm font-medium">Small Shadow</div>
              <div className="text-xs text-gray-500">shadow-sm</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow text-center">
              <div className="text-sm font-medium">Default Shadow</div>
              <div className="text-xs text-gray-500">shadow</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-md text-center">
              <div className="text-sm font-medium">Medium Shadow</div>
              <div className="text-xs text-gray-500">shadow-md</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-lg text-center">
              <div className="text-sm font-medium">Large Shadow</div>
              <div className="text-xs text-gray-500">shadow-lg</div>
            </div>
          </div>
        </div>

        {/* 交互测试 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">交互效果测试</h2>
          <div className="space-y-4">
            <button className="btn btn-primary hover:bg-blue-700 transition-colors">
              Hover Effect Button
            </button>
            <div className="bg-gray-100 hover:bg-gray-200 p-4 rounded-lg cursor-pointer transition-colors">
              Hover Effect Card
            </div>
          </div>
        </div>

        {/* 返回按钮 */}
        <div className="text-center">
          <a href="/" className="btn btn-secondary">
            返回首页
          </a>
        </div>
      </div>
    </div>
  );
};

export default StyleTestPage;
