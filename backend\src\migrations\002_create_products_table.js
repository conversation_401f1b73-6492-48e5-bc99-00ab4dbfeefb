/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('products', function(table) {
    // 主键
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // 关联用户
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // 基本信息
    table.string('title', 500).notNullable();
    table.text('description').nullable();
    table.string('url', 1000).notNullable();
    table.string('image_url', 1000).nullable();
    table.string('brand', 100).nullable();
    table.string('model', 100).nullable();
    
    // 平台信息
    table.enum('platform', ['taobao', 'tmall', 'jd', 'pdd']).notNullable();
    table.string('platform_product_id', 100).nullable();
    table.string('shop_name', 200).nullable();
    table.string('shop_url', 1000).nullable();
    
    // 价格信息
    table.decimal('current_price', 10, 2).nullable();
    table.decimal('original_price', 10, 2).nullable();
    table.decimal('min_price', 10, 2).nullable();
    table.decimal('max_price', 10, 2).nullable();
    table.string('currency', 3).defaultTo('CNY');
    
    // 销售信息
    table.integer('sales_count').nullable();
    table.decimal('rating', 3, 2).nullable();
    table.integer('review_count').nullable();
    table.integer('stock_quantity').nullable();
    
    // 分类信息
    table.string('category', 100).nullable();
    table.string('subcategory', 100).nullable();
    table.json('tags').nullable();
    table.json('attributes').nullable();
    
    // 状态信息
    table.enum('status', ['active', 'inactive', 'out_of_stock', 'discontinued']).defaultTo('active');
    table.boolean('is_tracked').defaultTo(false);
    table.boolean('is_analyzed').defaultTo(false);
    
    // 爬取信息
    table.timestamp('last_crawled_at').nullable();
    table.integer('crawl_count').defaultTo(0);
    table.json('crawl_errors').nullable();
    
    // 分析信息
    table.json('analysis_summary').nullable();
    table.decimal('analysis_score', 5, 2).nullable();
    table.json('market_position').nullable();
    
    // 时间戳
    table.timestamps(true, true);
    
    // 索引
    table.index(['user_id']);
    table.index(['platform']);
    table.index(['category']);
    table.index(['status']);
    table.index(['is_tracked']);
    table.index(['current_price']);
    table.index(['sales_count']);
    table.index(['rating']);
    table.index(['created_at']);
    table.index(['last_crawled_at']);
    
    // 复合索引
    table.index(['platform', 'platform_product_id']);
    table.index(['user_id', 'status']);
    table.index(['category', 'platform']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('products');
};
