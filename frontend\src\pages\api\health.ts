import type { NextApiRequest, NextApiResponse } from 'next';

interface HealthResponse {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: 'ok' | 'error';
    redis: 'ok' | 'error';
    api: 'ok' | 'error';
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<HealthResponse>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: 'error',
        redis: 'error',
        api: 'error',
      },
    });
  }

  try {
    // 检查API连接
    let apiStatus: 'ok' | 'error' = 'error';
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${apiUrl}/api/v1/health`, {
        method: 'GET',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      
      if (response.ok) {
        apiStatus = 'ok';
      }
    } catch (error) {
      console.error('API health check failed:', error);
    }

    const healthData: HealthResponse = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: apiStatus, // 通过API间接检查
        redis: apiStatus,    // 通过API间接检查
        api: apiStatus,
      },
    };

    // 如果任何服务不健康，返回错误状态
    const allServicesHealthy = Object.values(healthData.services).every(
      status => status === 'ok'
    );

    if (!allServicesHealthy) {
      healthData.status = 'error';
      return res.status(503).json(healthData);
    }

    res.status(200).json(healthData);
  } catch (error) {
    console.error('Health check error:', error);
    
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: 'error',
        redis: 'error',
        api: 'error',
      },
    });
  }
}
