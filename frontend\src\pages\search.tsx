import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  StarIcon,
  TrendingUpIcon,
  ShoppingBagIcon,
  EyeIcon,
  HeartIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';

const SearchPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('relevance');

  if (isLoading) {
    return <LoadingScreen message="加载搜索发现..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 模拟搜索结果数据
  const searchResults = [
    {
      id: 1,
      name: 'iPhone 15 Pro Max 1TB',
      brand: 'Apple',
      category: '手机',
      price: 12999,
      originalPrice: 13999,
      rating: 4.8,
      reviews: 1250,
      image: '/api/placeholder/200/200',
      tags: ['5G', '钛金属', 'A17 Pro'],
      trending: true,
      discount: 7,
      source: '天猫旗舰店'
    },
    {
      id: 2,
      name: '小米14 Ultra 16GB+1TB',
      brand: '小米',
      category: '手机',
      price: 6999,
      originalPrice: 7499,
      rating: 4.6,
      reviews: 890,
      image: '/api/placeholder/200/200',
      tags: ['徕卡影像', '骁龙8 Gen3', '无线充电'],
      trending: false,
      discount: 7,
      source: '小米官方店'
    },
    {
      id: 3,
      name: 'MacBook Pro 14英寸 M3 Pro',
      brand: 'Apple',
      category: '笔记本',
      price: 16999,
      originalPrice: 17999,
      rating: 4.9,
      reviews: 567,
      image: '/api/placeholder/200/200',
      tags: ['M3 Pro芯片', 'Liquid视网膜', '18小时续航'],
      trending: true,
      discount: 6,
      source: 'Apple Store'
    },
    {
      id: 4,
      name: 'AirPods Pro 2代 USB-C',
      brand: 'Apple',
      category: '耳机',
      price: 1899,
      originalPrice: 1999,
      rating: 4.7,
      reviews: 2340,
      image: '/api/placeholder/200/200',
      tags: ['主动降噪', 'USB-C', '空间音频'],
      trending: false,
      discount: 5,
      source: '京东自营'
    },
    {
      id: 5,
      name: 'iPad Pro 12.9英寸 M2',
      brand: 'Apple',
      category: '平板',
      price: 8999,
      originalPrice: 9999,
      rating: 4.8,
      reviews: 445,
      image: '/api/placeholder/200/200',
      tags: ['M2芯片', 'Liquid视网膜', 'Apple Pencil'],
      trending: true,
      discount: 10,
      source: '苏宁易购'
    },
    {
      id: 6,
      name: '华为Mate60 Pro 12GB+512GB',
      brand: '华为',
      category: '手机',
      price: 6999,
      originalPrice: 7499,
      rating: 4.5,
      reviews: 678,
      image: '/api/placeholder/200/200',
      tags: ['麒麟9000S', '卫星通话', '玄武架构'],
      trending: false,
      discount: 7,
      source: '华为商城'
    }
  ];

  const categories = ['all', '手机', '笔记本', '平板', '耳机'];
  const sortOptions = [
    { value: 'relevance', label: '相关度' },
    { value: 'price_low', label: '价格从低到高' },
    { value: 'price_high', label: '价格从高到低' },
    { value: 'rating', label: '评分最高' },
    { value: 'reviews', label: '评价最多' }
  ];

  const filteredResults = searchResults.filter(product => {
    const matchesSearch = searchTerm === '' || 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.brand.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <h1 style={{
              fontSize: '2rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              搜索发现
            </h1>
            <p style={{
              fontSize: '1rem',
              color: 'var(--color-gray-600)'
            }}>
              发现热门产品和最佳价格
            </p>
          </div>
        </div>

        {/* 搜索栏 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              gap: '1rem',
              alignItems: 'center',
              flexWrap: 'wrap'
            }}>
              <div style={{ flex: 1, minWidth: '300px', position: 'relative' }}>
                <MagnifyingGlassIcon style={{
                  position: 'absolute',
                  left: '0.75rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: '1.25rem',
                  height: '1.25rem',
                  color: 'var(--color-gray-400)'
                }} />
                <input
                  type="text"
                  className="input"
                  placeholder="搜索产品名称、品牌或型号..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ paddingLeft: '2.5rem' }}
                />
              </div>
              
              <select
                className="input"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                style={{ minWidth: '120px' }}
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? '全部分类' : category}
                  </option>
                ))}
              </select>
              
              <select
                className="input"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                style={{ minWidth: '140px' }}
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              
              <button className="btn btn-secondary">
                <FunnelIcon style={{ width: '1.25rem', height: '1.25rem' }} />
                筛选
              </button>
            </div>
          </div>
        </div>

        {/* 搜索结果 */}
        <div className="card">
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: 'var(--color-gray-900)'
              }}>
                搜索结果 ({filteredResults.length})
              </h2>
            </div>
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
              gap: '1.5rem'
            }}>
              {filteredResults.map((product) => (
                <div key={product.id} className="card">
                  <div className="card-body">
                    <div style={{
                      display: 'flex',
                      gap: '1rem'
                    }}>
                      <div style={{
                        width: '5rem',
                        height: '5rem',
                        background: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-lg)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.75rem',
                        color: 'var(--color-gray-500)',
                        flexShrink: 0
                      }}>
                        图片
                      </div>
                      
                      <div style={{ flex: 1 }}>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'flex-start',
                          marginBottom: '0.5rem'
                        }}>
                          <h3 style={{
                            fontSize: '1rem',
                            fontWeight: '600',
                            color: 'var(--color-gray-900)',
                            lineHeight: '1.4'
                          }}>
                            {product.name}
                          </h3>
                          {product.trending && (
                            <span className="badge badge-warning" style={{ fontSize: '0.625rem' }}>
                              <TrendingUpIcon style={{ width: '0.75rem', height: '0.75rem' }} />
                              热门
                            </span>
                          )}
                        </div>
                        
                        <p style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '0.5rem'
                        }}>
                          {product.brand} · {product.source}
                        </p>
                        
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          marginBottom: '0.75rem'
                        }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                            <StarIcon style={{ width: '1rem', height: '1rem', color: 'var(--color-warning-500)' }} />
                            <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                              {product.rating}
                            </span>
                          </div>
                          <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>
                            ({product.reviews}条评价)
                          </span>
                        </div>
                        
                        <div style={{
                          display: 'flex',
                          flexWrap: 'wrap',
                          gap: '0.25rem',
                          marginBottom: '0.75rem'
                        }}>
                          {product.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              style={{
                                fontSize: '0.625rem',
                                padding: '0.125rem 0.375rem',
                                background: 'var(--color-primary-100)',
                                color: 'var(--color-primary-700)',
                                borderRadius: 'var(--radius-sm)'
                              }}
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                        
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}>
                          <div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                              <span style={{
                                fontSize: '1.25rem',
                                fontWeight: '700',
                                color: 'var(--color-error-600)'
                              }}>
                                ¥{product.price.toLocaleString()}
                              </span>
                              {product.discount > 0 && (
                                <span className="badge badge-error" style={{ fontSize: '0.625rem' }}>
                                  -{product.discount}%
                                </span>
                              )}
                            </div>
                            {product.originalPrice > product.price && (
                              <span style={{
                                fontSize: '0.75rem',
                                color: 'var(--color-gray-500)',
                                textDecoration: 'line-through'
                              }}>
                                ¥{product.originalPrice.toLocaleString()}
                              </span>
                            )}
                          </div>
                          
                          <div style={{ display: 'flex', gap: '0.5rem' }}>
                            <button className="btn btn-ghost btn-sm">
                              <HeartIcon style={{ width: '1rem', height: '1rem' }} />
                            </button>
                            <button className="btn btn-ghost btn-sm">
                              <EyeIcon style={{ width: '1rem', height: '1rem' }} />
                            </button>
                            <button className="btn btn-primary btn-sm">
                              <PlusIcon style={{ width: '1rem', height: '1rem' }} />
                              添加
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredResults.length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: 'var(--color-gray-500)'
              }}>
                <MagnifyingGlassIcon style={{
                  width: '3rem',
                  height: '3rem',
                  margin: '0 auto 1rem',
                  color: 'var(--color-gray-300)'
                }} />
                <p>没有找到匹配的产品</p>
                <p style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
                  尝试调整搜索关键词或筛选条件
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
