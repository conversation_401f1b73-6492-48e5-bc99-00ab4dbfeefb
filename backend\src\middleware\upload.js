const multer = require('multer');
const path = require('path');
const sharp = require('sharp');
const { createError } = require('./errorHandler');
const logger = require('../utils/logger');

// 允许的文件类型
const ALLOWED_IMAGE_TYPES = {
  'image/jpeg': '.jpg',
  'image/jpg': '.jpg',
  'image/png': '.png',
  'image/webp': '.webp',
  'image/gif': '.gif'
};

const ALLOWED_DOCUMENT_TYPES = {
  'application/pdf': '.pdf',
  'application/msword': '.doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'application/vnd.ms-excel': '.xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
  'text/csv': '.csv'
};

// 文件大小限制
const FILE_SIZE_LIMITS = {
  image: 5 * 1024 * 1024, // 5MB
  document: 10 * 1024 * 1024, // 10MB
  avatar: 2 * 1024 * 1024 // 2MB
};

/**
 * 生成唯一文件名
 */
const generateFileName = (originalName, userId) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  const ext = path.extname(originalName).toLowerCase();
  return `${userId}_${timestamp}_${random}${ext}`;
};

/**
 * 文件过滤器
 */
const createFileFilter = (allowedTypes) => {
  return (req, file, cb) => {
    if (allowedTypes[file.mimetype]) {
      cb(null, true);
    } else {
      const error = createError.badRequest(
        `不支持的文件类型: ${file.mimetype}`,
        'UNSUPPORTED_FILE_TYPE'
      );
      cb(error, false);
    }
  };
};

/**
 * 存储配置
 */
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = 'uploads/';
    
    // 根据文件类型确定存储路径
    if (file.fieldname === 'avatar') {
      uploadPath += 'avatars/';
    } else if (ALLOWED_IMAGE_TYPES[file.mimetype]) {
      uploadPath += 'images/';
    } else if (ALLOWED_DOCUMENT_TYPES[file.mimetype]) {
      uploadPath += 'documents/';
    } else {
      uploadPath += 'others/';
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const userId = req.user ? req.user.id : 'anonymous';
    const fileName = generateFileName(file.originalname, userId);
    cb(null, fileName);
  }
});

/**
 * 内存存储配置（用于图片处理）
 */
const memoryStorage = multer.memoryStorage();

/**
 * 基础上传配置
 */
const baseUpload = multer({
  storage: storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.document,
    files: 10,
    fields: 20
  },
  fileFilter: createFileFilter({
    ...ALLOWED_IMAGE_TYPES,
    ...ALLOWED_DOCUMENT_TYPES
  })
});

/**
 * 图片上传配置
 */
const imageUpload = multer({
  storage: memoryStorage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.image,
    files: 5
  },
  fileFilter: createFileFilter(ALLOWED_IMAGE_TYPES)
});

/**
 * 头像上传配置
 */
const avatarUpload = multer({
  storage: memoryStorage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.avatar,
    files: 1
  },
  fileFilter: createFileFilter(ALLOWED_IMAGE_TYPES)
});

/**
 * 文档上传配置
 */
const documentUpload = multer({
  storage: storage,
  limits: {
    fileSize: FILE_SIZE_LIMITS.document,
    files: 3
  },
  fileFilter: createFileFilter(ALLOWED_DOCUMENT_TYPES)
});

/**
 * 图片处理中间件
 */
const processImage = (options = {}) => {
  const {
    width = 800,
    height = 600,
    quality = 80,
    format = 'jpeg',
    resize = true
  } = options;
  
  return async (req, res, next) => {
    if (!req.file || !req.file.buffer) {
      return next();
    }
    
    try {
      let processor = sharp(req.file.buffer);
      
      // 调整大小
      if (resize) {
        processor = processor.resize(width, height, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }
      
      // 设置格式和质量
      if (format === 'jpeg') {
        processor = processor.jpeg({ quality });
      } else if (format === 'png') {
        processor = processor.png({ quality });
      } else if (format === 'webp') {
        processor = processor.webp({ quality });
      }
      
      // 处理图片
      const processedBuffer = await processor.toBuffer();
      const info = await processor.metadata();
      
      // 更新文件信息
      req.file.buffer = processedBuffer;
      req.file.size = processedBuffer.length;
      req.file.processedInfo = info;
      
      logger.info('图片处理完成', {
        originalSize: req.file.size,
        processedSize: processedBuffer.length,
        width: info.width,
        height: info.height,
        format: info.format
      });
      
      next();
    } catch (error) {
      logger.error('图片处理失败:', error);
      next(createError.badRequest('图片处理失败', 'IMAGE_PROCESSING_ERROR'));
    }
  };
};

/**
 * 保存处理后的图片
 */
const saveProcessedImage = (uploadPath = 'uploads/images/') => {
  return async (req, res, next) => {
    if (!req.file || !req.file.buffer) {
      return next();
    }
    
    try {
      const userId = req.user ? req.user.id : 'anonymous';
      const fileName = generateFileName(req.file.originalname, userId);
      const filePath = path.join(uploadPath, fileName);
      
      // 确保目录存在
      const fs = require('fs').promises;
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      
      // 保存文件
      await fs.writeFile(filePath, req.file.buffer);
      
      // 更新文件信息
      req.file.filename = fileName;
      req.file.path = filePath;
      req.file.destination = uploadPath;
      
      logger.info('图片保存成功', {
        fileName,
        filePath,
        size: req.file.size
      });
      
      next();
    } catch (error) {
      logger.error('保存图片失败:', error);
      next(createError.internal('保存图片失败', 'IMAGE_SAVE_ERROR'));
    }
  };
};

/**
 * 文件验证中间件
 */
const validateFile = (options = {}) => {
  const {
    required = false,
    maxSize = FILE_SIZE_LIMITS.document,
    allowedTypes = Object.keys({ ...ALLOWED_IMAGE_TYPES, ...ALLOWED_DOCUMENT_TYPES })
  } = options;
  
  return (req, res, next) => {
    // 检查是否必须上传文件
    if (required && (!req.file && !req.files)) {
      return next(createError.badRequest('文件不能为空', 'FILE_REQUIRED'));
    }
    
    // 如果没有文件，跳过验证
    if (!req.file && !req.files) {
      return next();
    }
    
    const files = req.files ? Object.values(req.files).flat() : [req.file];
    
    for (const file of files) {
      // 检查文件大小
      if (file.size > maxSize) {
        return next(createError.badRequest(
          `文件大小超出限制: ${Math.round(maxSize / 1024 / 1024)}MB`,
          'FILE_TOO_LARGE'
        ));
      }
      
      // 检查文件类型
      if (!allowedTypes.includes(file.mimetype)) {
        return next(createError.badRequest(
          `不支持的文件类型: ${file.mimetype}`,
          'UNSUPPORTED_FILE_TYPE'
        ));
      }
    }
    
    next();
  };
};

/**
 * 清理上传的文件
 */
const cleanupFiles = (files) => {
  const fs = require('fs');
  
  if (!Array.isArray(files)) {
    files = [files];
  }
  
  files.forEach(file => {
    if (file && file.path) {
      fs.unlink(file.path, (err) => {
        if (err) {
          logger.error('删除文件失败:', err);
        } else {
          logger.info('文件清理成功:', file.path);
        }
      });
    }
  });
};

module.exports = {
  // 基础上传器
  upload: baseUpload,
  
  // 专用上传器
  single: (fieldName) => baseUpload.single(fieldName),
  multiple: (fieldName, maxCount = 5) => baseUpload.array(fieldName, maxCount),
  fields: (fields) => baseUpload.fields(fields),
  
  // 图片上传
  imageUpload,
  avatarUpload,
  documentUpload,
  
  // 处理中间件
  processImage,
  saveProcessedImage,
  validateFile,
  
  // 工具函数
  cleanupFiles,
  generateFileName,
  
  // 常量
  ALLOWED_IMAGE_TYPES,
  ALLOWED_DOCUMENT_TYPES,
  FILE_SIZE_LIMITS
};
