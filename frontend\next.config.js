/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // 环境变量配置
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    NEXT_PUBLIC_WS_URL: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000',
  },

  // 图片配置
  images: {
    domains: [
      'localhost',
      'img.alicdn.com',
      'gd1.alicdn.com',
      'gd2.alicdn.com',
      'gd3.alicdn.com',
      'gd4.alicdn.com',
      'img10.360buyimg.com',
      'img11.360buyimg.com',
      'img12.360buyimg.com',
      'img13.360buyimg.com',
      'img14.360buyimg.com',
      'img30.360buyimg.com',
      'commimg.pddpic.com',
      'img.pddpic.com'
    ],
    formats: ['image/webp', 'image/avif'],
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: false,
      },
    ];
  },

  // API路由重写
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/api/v1/:path*`,
      },
    ];
  },

  // Webpack配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 添加自定义webpack配置
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    return config;
  },

  // 实验性功能
  experimental: {
    appDir: false, // 使用传统的pages目录结构
  },

  // 编译器选项
  compiler: {
    // 移除console.log（仅在生产环境）
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // 输出配置
  output: 'standalone',

  // 压缩配置
  compress: true,

  // 电源配置
  poweredByHeader: false,

  // 生成ETag
  generateEtags: true,

  // 页面扩展名
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],
};

module.exports = nextConfig;
