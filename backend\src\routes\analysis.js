const express = require('express');
const { body, query, param } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const analysisController = require('../controllers/analysisController');
const { validateRequest } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// 所有分析路由都需要认证
router.use(authenticate);

/**
 * @route   POST /api/v1/analysis/start
 * @desc    开始分析任务
 * @access  Private
 */
router.post('/start', [
  body('type')
    .isIn(['product', 'market', 'competitor', 'trend'])
    .withMessage('分析类型不正确'),
  body('config')
    .notEmpty()
    .withMessage('分析配置不能为空'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high'])
    .withMessage('优先级不正确'),
  validateRequest
], asyncHandler(analysisController.startAnalysis));

/**
 * @route   GET /api/v1/analysis
 * @desc    获取分析任务列表
 * @access  Private
 */
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('每页数量必须在1-50之间'),
  query('status')
    .optional()
    .isIn(['pending', 'running', 'completed', 'failed', 'cancelled'])
    .withMessage('状态不正确'),
  query('type')
    .optional()
    .isIn(['product', 'market', 'competitor', 'trend'])
    .withMessage('分析类型不正确'),
  validateRequest
], asyncHandler(analysisController.getAnalysisList));

/**
 * @route   GET /api/v1/analysis/:id
 * @desc    获取分析任务详情
 * @access  Private
 */
router.get('/:id', [
  param('id').isUUID().withMessage('分析ID格式不正确'),
  validateRequest
], asyncHandler(analysisController.getAnalysisById));

/**
 * @route   GET /api/v1/analysis/:id/status
 * @desc    获取分析任务状态
 * @access  Private
 */
router.get('/:id/status', [
  param('id').isUUID().withMessage('分析ID格式不正确'),
  validateRequest
], asyncHandler(analysisController.getAnalysisStatus));

/**
 * @route   GET /api/v1/analysis/:id/result
 * @desc    获取分析结果
 * @access  Private
 */
router.get('/:id/result', [
  param('id').isUUID().withMessage('分析ID格式不正确'),
  validateRequest
], asyncHandler(analysisController.getAnalysisResult));

/**
 * @route   POST /api/v1/analysis/:id/cancel
 * @desc    取消分析任务
 * @access  Private
 */
router.post('/:id/cancel', [
  param('id').isUUID().withMessage('分析ID格式不正确'),
  validateRequest
], asyncHandler(analysisController.cancelAnalysis));

/**
 * @route   POST /api/v1/analysis/:id/retry
 * @desc    重试失败的分析任务
 * @access  Private
 */
router.post('/:id/retry', [
  param('id').isUUID().withMessage('分析ID格式不正确'),
  validateRequest
], asyncHandler(analysisController.retryAnalysis));

/**
 * @route   DELETE /api/v1/analysis/:id
 * @desc    删除分析任务
 * @access  Private
 */
router.delete('/:id', [
  param('id').isUUID().withMessage('分析ID格式不正确'),
  validateRequest
], asyncHandler(analysisController.deleteAnalysis));

/**
 * @route   GET /api/v1/analysis/:id/export
 * @desc    导出分析结果
 * @access  Private
 */
router.get('/:id/export', [
  param('id').isUUID().withMessage('分析ID格式不正确'),
  query('format')
    .optional()
    .isIn(['json', 'csv', 'excel', 'pdf'])
    .withMessage('导出格式不正确'),
  validateRequest
], asyncHandler(analysisController.exportAnalysis));

/**
 * @route   POST /api/v1/analysis/:id/share
 * @desc    分享分析结果
 * @access  Private
 */
router.post('/:id/share', [
  param('id').isUUID().withMessage('分析ID格式不正确'),
  body('shareType')
    .isIn(['public', 'private'])
    .withMessage('分享类型不正确'),
  body('expiresIn')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('过期天数必须在1-365之间'),
  validateRequest
], asyncHandler(analysisController.shareAnalysis));

/**
 * @route   GET /api/v1/analysis/shared/:token
 * @desc    获取分享的分析结果
 * @access  Public
 */
router.get('/shared/:token', [
  param('token').notEmpty().withMessage('分享令牌不能为空'),
  validateRequest
], asyncHandler(analysisController.getSharedAnalysis));

/**
 * @route   GET /api/v1/analysis/templates
 * @desc    获取分析模板列表
 * @access  Private
 */
router.get('/templates', asyncHandler(analysisController.getAnalysisTemplates));

/**
 * @route   POST /api/v1/analysis/templates
 * @desc    创建分析模板
 * @access  Private
 */
router.post('/templates', [
  body('name')
    .notEmpty()
    .withMessage('模板名称不能为空')
    .isLength({ max: 100 })
    .withMessage('模板名称不能超过100字符'),
  body('type')
    .isIn(['product', 'market', 'competitor', 'trend'])
    .withMessage('分析类型不正确'),
  body('config')
    .notEmpty()
    .withMessage('模板配置不能为空'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('模板描述不能超过500字符'),
  validateRequest
], asyncHandler(analysisController.createAnalysisTemplate));

/**
 * @route   GET /api/v1/analysis/statistics
 * @desc    获取分析统计信息
 * @access  Private
 */
router.get('/statistics', [
  query('period')
    .optional()
    .isIn(['7d', '30d', '90d', '1y'])
    .withMessage('统计周期不正确'),
  validateRequest
], asyncHandler(analysisController.getAnalysisStatistics));

module.exports = router;
