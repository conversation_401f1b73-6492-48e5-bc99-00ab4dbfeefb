/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/products"],{

/***/ "__barrel_optimize__?names=EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EyeIcon: function() { return /* reexport safe */ _EyeIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   FunnelIcon: function() { return /* reexport safe */ _FunnelIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   MagnifyingGlassIcon: function() { return /* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   PencilIcon: function() { return /* reexport safe */ _PencilIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   PlusIcon: function() { return /* reexport safe */ _PlusIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   TrashIcon: function() { return /* reexport safe */ _TrashIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _EyeIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EyeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _FunnelIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./FunnelIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _PencilIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PencilIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _PlusIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PlusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _TrashIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TrashIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWVJY29uLEZ1bm5lbEljb24sTWFnbmlmeWluZ0dsYXNzSWNvbixQZW5jaWxJY29uLFBsdXNJY29uLFRyYXNoSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUNpRDtBQUNNO0FBQ2tCO0FBQ2xCO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ZjdkNSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllSWNvbiB9IGZyb20gXCIuL0V5ZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBGdW5uZWxJY29uIH0gZnJvbSBcIi4vRnVubmVsSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1hZ25pZnlpbmdHbGFzc0ljb24gfSBmcm9tIFwiLi9NYWduaWZ5aW5nR2xhc3NJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGVuY2lsSWNvbiB9IGZyb20gXCIuL1BlbmNpbEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbHVzSWNvbiB9IGZyb20gXCIuL1BsdXNJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHJhc2hJY29uIH0gZnJvbSBcIi4vVHJhc2hJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/products\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/products.tsx */ \"./src/pages/products.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/products\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDYWktcHJvZHVjdC1zZWxlY3QlNUNmcm9udGVuZCU1Q3NyYyU1Q3BhZ2VzJTVDcHJvZHVjdHMudHN4JnBhZ2U9JTJGcHJvZHVjdHMhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMERBQTBCO0FBQ2pEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz84NzAxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvcHJvZHVjdHNcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9wcm9kdWN0cy50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL3Byb2R1Y3RzXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts!\n"));

/***/ }),

/***/ "./src/pages/products.tsx":
/*!********************************!*\
  !*** ./src/pages/products.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst ProductsPage = ()=>{\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            message: \"加载产品管理...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n            lineNumber: 18,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    // 模拟产品数据\n    const products = [\n        {\n            id: 1,\n            name: \"iPhone 15 Pro Max\",\n            brand: \"Apple\",\n            category: \"手机\",\n            price: 9999,\n            stock: 50,\n            status: \"在售\",\n            image: \"/api/placeholder/100/100\",\n            createdAt: \"2024-01-15\"\n        },\n        {\n            id: 2,\n            name: \"小米14 Ultra\",\n            brand: \"小米\",\n            category: \"手机\",\n            price: 5999,\n            stock: 30,\n            status: \"在售\",\n            image: \"/api/placeholder/100/100\",\n            createdAt: \"2024-01-10\"\n        },\n        {\n            id: 3,\n            name: \"MacBook Pro M3\",\n            brand: \"Apple\",\n            category: \"笔记本\",\n            price: 14999,\n            stock: 15,\n            status: \"在售\",\n            image: \"/api/placeholder/100/100\",\n            createdAt: \"2024-01-08\"\n        },\n        {\n            id: 4,\n            name: \"AirPods Pro 2\",\n            brand: \"Apple\",\n            category: \"耳机\",\n            price: 1899,\n            stock: 0,\n            status: \"缺货\",\n            image: \"/api/placeholder/100/100\",\n            createdAt: \"2024-01-05\"\n        }\n    ];\n    const filteredProducts = products.filter((product)=>product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.brand.toLowerCase().includes(searchTerm.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            background: \"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)\",\n            padding: \"2rem 0\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: \"1200px\",\n                margin: \"0 auto\",\n                padding: \"0 1rem\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    style: {\n                        marginBottom: \"2rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                fontSize: \"2rem\",\n                                                fontWeight: \"700\",\n                                                color: \"var(--color-gray-900)\",\n                                                marginBottom: \"0.5rem\"\n                                            },\n                                            children: \"产品管理\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: \"1rem\",\n                                                color: \"var(--color-gray-600)\"\n                                            },\n                                            children: \"管理您的产品库存和信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PlusIcon, {\n                                            style: {\n                                                width: \"1.25rem\",\n                                                height: \"1.25rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"添加产品\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    style: {\n                        marginBottom: \"2rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"1rem\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1,\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.MagnifyingGlassIcon, {\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"0.75rem\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                width: \"1.25rem\",\n                                                height: \"1.25rem\",\n                                                color: \"var(--color-gray-400)\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"input\",\n                                            placeholder: \"搜索产品名称或品牌...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            style: {\n                                                paddingLeft: \"2.5rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.FunnelIcon, {\n                                            style: {\n                                                width: \"1.25rem\",\n                                                height: \"1.25rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"筛选\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"flex-start\",\n                                                    gap: \"1rem\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"4rem\",\n                                                            height: \"4rem\",\n                                                            background: \"var(--color-gray-200)\",\n                                                            borderRadius: \"var(--radius-lg)\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"0.75rem\",\n                                                            color: \"var(--color-gray-500)\"\n                                                        },\n                                                        children: \"图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            flex: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                style: {\n                                                                    fontSize: \"1.125rem\",\n                                                                    fontWeight: \"600\",\n                                                                    color: \"var(--color-gray-900)\",\n                                                                    marginBottom: \"0.25rem\"\n                                                                },\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                style: {\n                                                                    fontSize: \"0.875rem\",\n                                                                    color: \"var(--color-gray-600)\",\n                                                                    marginBottom: \"0.5rem\"\n                                                                },\n                                                                children: [\n                                                                    product.brand,\n                                                                    \" \\xb7 \",\n                                                                    product.category\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    justifyContent: \"space-between\",\n                                                                    alignItems: \"center\",\n                                                                    marginBottom: \"1rem\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: \"700\",\n                                                                            color: \"var(--color-primary-600)\"\n                                                                        },\n                                                                        children: [\n                                                                            \"\\xa5\",\n                                                                            product.price.toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"badge \".concat(product.status === \"在售\" ? \"badge-success\" : \"badge-error\"),\n                                                                        children: product.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"0.875rem\",\n                                                                    color: \"var(--color-gray-600)\",\n                                                                    marginBottom: \"1rem\"\n                                                                },\n                                                                children: [\n                                                                    \"库存: \",\n                                                                    product.stock,\n                                                                    \" 件\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    gap: \"0.5rem\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"btn btn-ghost btn-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.EyeIcon, {\n                                                                                style: {\n                                                                                    width: \"1rem\",\n                                                                                    height: \"1rem\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                lineNumber: 236,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            \"查看\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"btn btn-ghost btn-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PencilIcon, {\n                                                                                style: {\n                                                                                    width: \"1rem\",\n                                                                                    height: \"1rem\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                lineNumber: 240,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            \"编辑\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"btn btn-ghost btn-sm\",\n                                                                        style: {\n                                                                            color: \"var(--color-error-600)\"\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.TrashIcon, {\n                                                                                style: {\n                                                                                    width: \"1rem\",\n                                                                                    height: \"1rem\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            \"删除\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, product.id, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined),\n                            filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    padding: \"3rem\",\n                                    color: \"var(--color-gray-500)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"没有找到匹配的产品\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductsPage, \"vG5DgwcCDVBAhaM2A4XxZDX/Ka4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth\n    ];\n});\n_c = ProductsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductsPage);\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/products.tsx\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js ***!
  \*****************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction EyeIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n    }));\n}\n_c = EyeIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(EyeIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"EyeIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js":
/*!********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js ***!
  \********************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction FunnelIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z\"\n    }));\n}\n_c = FunnelIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(FunnelIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"FunnelIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js":
/*!********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js ***!
  \********************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction PencilIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n    }));\n}\n_c = PencilIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PencilIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PencilIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js ***!
  \******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction PlusIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 4.5v15m7.5-7.5h-15\"\n    }));\n}\n_c = PlusIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PlusIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PlusIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js ***!
  \*******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction TrashIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n    }));\n}\n_c = TrashIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(TrashIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"TrashIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9UcmFzaEljb24uanMiLCJtYXBwaW5ncyI6Ijs7QUFBK0I7QUFDL0IsU0FBU0MsVUFBVSxLQUlsQixFQUFFQyxNQUFNO1FBSlUsRUFDakJDLEtBQUssRUFDTEMsT0FBTyxFQUNQLEdBQUdDLE9BQ0osR0FKa0I7SUFLakIsT0FBTyxXQUFXLEdBQUVMLGdEQUFtQixDQUFDLE9BQU9PLE9BQU9DLE1BQU0sQ0FBQztRQUMzREMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsYUFBYTtRQUNiQyxRQUFRO1FBQ1IsZUFBZTtRQUNmLGFBQWE7UUFDYkMsS0FBS1o7UUFDTCxtQkFBbUJFO0lBQ3JCLEdBQUdDLFFBQVFGLFFBQVEsV0FBVyxHQUFFSCxnREFBbUIsQ0FBQyxTQUFTO1FBQzNEZSxJQUFJWDtJQUNOLEdBQUdELFNBQVMsTUFBTSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFFBQVE7UUFDekRnQixlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsR0FBRztJQUNMO0FBQ0Y7S0F0QlNqQjtBQXVCVCxNQUFNa0IsYUFBYSxXQUFXLEdBQUduQiw2Q0FBZ0IsQ0FBQ0M7O0FBQ2xELCtEQUFla0IsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9UcmFzaEljb24uanM/M2FkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIFRyYXNoSWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwibTE0Ljc0IDktLjM0NiA5bS00Ljc4OCAwTDkuMjYgOW05Ljk2OC0zLjIxYy4zNDIuMDUyLjY4Mi4xMDcgMS4wMjIuMTY2bS0xLjAyMi0uMTY1TDE4LjE2IDE5LjY3M2EyLjI1IDIuMjUgMCAwIDEtMi4yNDQgMi4wNzdIOC4wODRhMi4yNSAyLjI1IDAgMCAxLTIuMjQ0LTIuMDc3TDQuNzcyIDUuNzltMTQuNDU2IDBhNDguMTA4IDQ4LjEwOCAwIDAgMC0zLjQ3OC0uMzk3bS0xMiAuNTYyYy4zNC0uMDU5LjY4LS4xMTQgMS4wMjItLjE2NW0wIDBhNDguMTEgNDguMTEgMCAwIDEgMy40NzgtLjM5N203LjUgMHYtLjkxNmMwLTEuMTgtLjkxLTIuMTY0LTIuMDktMi4yMDFhNTEuOTY0IDUxLjk2NCAwIDAgMC0zLjMyIDBjLTEuMTguMDM3LTIuMDkgMS4wMjItMi4wOSAyLjIwMXYuOTE2bTcuNSAwYTQ4LjY2NyA0OC42NjcgMCAwIDAtNy41IDBcIlxuICB9KSk7XG59XG5jb25zdCBGb3J3YXJkUmVmID0gLyojX19QVVJFX18qLyBSZWFjdC5mb3J3YXJkUmVmKFRyYXNoSWNvbik7XG5leHBvcnQgZGVmYXVsdCBGb3J3YXJkUmVmOyJdLCJuYW1lcyI6WyJSZWFjdCIsIlRyYXNoSWNvbiIsInN2Z1JlZiIsInRpdGxlIiwidGl0bGVJZCIsInByb3BzIiwiY3JlYXRlRWxlbWVudCIsIk9iamVjdCIsImFzc2lnbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInJlZiIsImlkIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiZCIsIkZvcndhcmRSZWYiLCJmb3J3YXJkUmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);