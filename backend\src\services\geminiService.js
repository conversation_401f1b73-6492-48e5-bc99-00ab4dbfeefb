const axios = require('axios');
const { createError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { cache } = require('../config/redis');

class GeminiService {
  constructor() {
    this.baseURL = 'https://generativelanguage.googleapis.com/v1beta';
    this.defaultModel = 'gemini-pro';
    this.maxRetries = 3;
    this.retryDelay = 1000;
  }

  /**
   * 创建API客户端
   */
  createClient(apiKey) {
    if (!apiKey) {
      throw createError.badRequest('Gemini API密钥不能为空', 'MISSING_API_KEY');
    }

    return axios.create({
      baseURL: this.baseURL,
      timeout: 60000,
      headers: {
        'Content-Type': 'application/json',
      },
      params: {
        key: apiKey
      }
    });
  }

  /**
   * 测试API连接
   */
  async testConnection(apiKey) {
    try {
      const client = this.createClient(apiKey);
      
      const response = await client.post(`/models/${this.defaultModel}:generateContent`, {
        contents: [{
          parts: [{
            text: '你好，请回复"连接成功"'
          }]
        }]
      });

      if (response.data && response.data.candidates) {
        logger.info('Gemini API连接测试成功');
        return {
          success: true,
          message: '连接成功',
          model: this.defaultModel,
          response: response.data.candidates[0]?.content?.parts[0]?.text || '连接成功'
        };
      }

      throw new Error('API响应格式异常');
    } catch (error) {
      logger.error('Gemini API连接测试失败:', error);
      
      if (error.response) {
        const { status, data } = error.response;
        if (status === 401) {
          throw createError.unauthorized('API密钥无效', 'INVALID_API_KEY');
        } else if (status === 403) {
          throw createError.forbidden('API密钥权限不足', 'INSUFFICIENT_PERMISSIONS');
        } else if (status === 429) {
          throw createError.tooManyRequests('API请求频率超限', 'RATE_LIMIT_EXCEEDED');
        }
        
        throw createError.badRequest(
          data.error?.message || 'API请求失败',
          'API_REQUEST_FAILED'
        );
      }
      
      throw createError.internal('网络连接失败', 'NETWORK_ERROR');
    }
  }

  /**
   * 生成内容
   */
  async generateContent(apiKey, prompt, options = {}) {
    const {
      model = this.defaultModel,
      temperature = 0.7,
      maxOutputTokens = 2048,
      topP = 0.8,
      topK = 40
    } = options;

    try {
      const client = this.createClient(apiKey);
      
      const requestData = {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature,
          maxOutputTokens,
          topP,
          topK
        }
      };

      const startTime = Date.now();
      const response = await this.retryRequest(
        () => client.post(`/models/${model}:generateContent`, requestData)
      );
      const duration = Date.now() - startTime;

      logger.logApiCall('gemini', 'generateContent', duration, true);

      if (!response.data || !response.data.candidates) {
        throw new Error('API响应格式异常');
      }

      const candidate = response.data.candidates[0];
      if (!candidate || !candidate.content) {
        throw new Error('生成内容为空');
      }

      return {
        text: candidate.content.parts[0]?.text || '',
        finishReason: candidate.finishReason,
        safetyRatings: candidate.safetyRatings,
        usage: response.data.usageMetadata,
        model,
        duration
      };
    } catch (error) {
      logger.error('Gemini内容生成失败:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * 分析产品
   */
  async analyzeProduct(apiKey, productData, analysisType = 'basic') {
    const prompts = {
      basic: this.createBasicAnalysisPrompt(productData),
      detailed: this.createDetailedAnalysisPrompt(productData),
      competitive: this.createCompetitiveAnalysisPrompt(productData)
    };

    const prompt = prompts[analysisType];
    if (!prompt) {
      throw createError.badRequest('不支持的分析类型', 'INVALID_ANALYSIS_TYPE');
    }

    try {
      const result = await this.generateContent(apiKey, prompt, {
        temperature: 0.3,
        maxOutputTokens: 4096
      });

      // 解析结构化结果
      const analysis = this.parseAnalysisResult(result.text, analysisType);
      
      return {
        ...analysis,
        metadata: {
          model: result.model,
          duration: result.duration,
          usage: result.usage,
          analysisType,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('产品分析失败:', error);
      throw error;
    }
  }

  /**
   * 市场趋势分析
   */
  async analyzeMarket(apiKey, category, keywords, timeRange = '30d') {
    const prompt = this.createMarketAnalysisPrompt(category, keywords, timeRange);
    
    try {
      const result = await this.generateContent(apiKey, prompt, {
        temperature: 0.4,
        maxOutputTokens: 3072
      });

      const analysis = this.parseMarketAnalysis(result.text);
      
      return {
        ...analysis,
        metadata: {
          model: result.model,
          duration: result.duration,
          category,
          keywords,
          timeRange,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('市场分析失败:', error);
      throw error;
    }
  }

  /**
   * 产品比较
   */
  async compareProducts(apiKey, products, compareFields) {
    const prompt = this.createProductComparisonPrompt(products, compareFields);
    
    try {
      const result = await this.generateContent(apiKey, prompt, {
        temperature: 0.2,
        maxOutputTokens: 4096
      });

      const comparison = this.parseComparisonResult(result.text);
      
      return {
        ...comparison,
        metadata: {
          model: result.model,
          duration: result.duration,
          productCount: products.length,
          compareFields,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('产品比较失败:', error);
      throw error;
    }
  }

  /**
   * 价格趋势预测
   */
  async predictTrend(apiKey, productId, historicalData, predictionDays) {
    const prompt = this.createTrendPredictionPrompt(productId, historicalData, predictionDays);
    
    try {
      const result = await this.generateContent(apiKey, prompt, {
        temperature: 0.1,
        maxOutputTokens: 2048
      });

      const prediction = this.parseTrendPrediction(result.text);
      
      return {
        ...prediction,
        metadata: {
          model: result.model,
          duration: result.duration,
          productId,
          dataPoints: historicalData.length,
          predictionDays,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('趋势预测失败:', error);
      throw error;
    }
  }

  /**
   * 重试请求
   */
  async retryRequest(requestFn, retries = this.maxRetries) {
    for (let i = 0; i <= retries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        if (i === retries) throw error;
        
        // 如果是速率限制错误，等待更长时间
        const delay = error.response?.status === 429 
          ? this.retryDelay * Math.pow(2, i) 
          : this.retryDelay;
        
        logger.warn(`API请求失败，${delay}ms后重试 (${i + 1}/${retries}):`, error.message);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * 处理API错误
   */
  handleApiError(error) {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          return createError.badRequest(
            data.error?.message || '请求参数错误',
            'INVALID_REQUEST'
          );
        case 401:
          return createError.unauthorized('API密钥无效', 'INVALID_API_KEY');
        case 403:
          return createError.forbidden('API权限不足', 'INSUFFICIENT_PERMISSIONS');
        case 429:
          return createError.tooManyRequests('API请求频率超限', 'RATE_LIMIT_EXCEEDED');
        case 500:
          return createError.internal('Gemini服务器错误', 'GEMINI_SERVER_ERROR');
        default:
          return createError.internal(
            data.error?.message || 'API请求失败',
            'API_REQUEST_FAILED'
          );
      }
    }
    
    if (error.code === 'ECONNABORTED') {
      return createError.internal('请求超时', 'REQUEST_TIMEOUT');
    }
    
    return createError.internal('网络连接失败', 'NETWORK_ERROR');
  }

  /**
   * 创建基础分析提示词
   */
  createBasicAnalysisPrompt(productData) {
    return `请分析以下产品信息，并以JSON格式返回分析结果：

产品信息：
- 标题：${productData.title}
- 价格：${productData.current_price}元
- 平台：${productData.platform}
- 销量：${productData.sales_count}
- 评分：${productData.rating}
- 评论数：${productData.review_count}
- 类别：${productData.category}

请从以下维度进行分析：
1. 产品竞争力评分（1-100分）
2. 价格合理性分析
3. 市场表现评估
4. 主要优势（3-5点）
5. 潜在风险（2-3点）
6. 选品建议

返回格式：
{
  "score": 数字,
  "priceAnalysis": "价格分析文本",
  "marketPerformance": "市场表现文本",
  "strengths": ["优势1", "优势2", "优势3"],
  "risks": ["风险1", "风险2"],
  "recommendation": "选品建议文本"
}`;
  }

  /**
   * 创建详细分析提示词
   */
  createDetailedAnalysisPrompt(productData) {
    return `请对以下产品进行深度分析，并以JSON格式返回详细分析结果：

产品信息：
- 标题：${productData.title}
- 描述：${productData.description || '无'}
- 品牌：${productData.brand || '无'}
- 价格：${productData.current_price}元（原价：${productData.original_price || '无'}元）
- 平台：${productData.platform}
- 店铺：${productData.shop_name || '无'}
- 销量：${productData.sales_count}
- 评分：${productData.rating}/5.0
- 评论数：${productData.review_count}
- 库存：${productData.stock_quantity || '无'}
- 类别：${productData.category}/${productData.subcategory || '无'}
- 标签：${JSON.stringify(productData.tags || [])}
- 属性：${JSON.stringify(productData.attributes || {})}

请从以下维度进行深度分析：
1. 综合竞争力评分（1-100分）
2. 价格策略分析（定价合理性、价格竞争力）
3. 市场定位分析（目标用户群体、市场细分）
4. 产品特色分析（核心卖点、差异化优势）
5. 销售表现分析（销量趋势、用户反馈）
6. 竞争环境分析（同类产品对比、市场份额）
7. 风险评估（供应链、市场、技术风险）
8. 机会识别（增长潜力、扩展机会）
9. 选品建议（是否推荐、改进建议）

返回格式：
{
  "overallScore": 数字,
  "pricingAnalysis": {
    "reasonableness": "定价合理性分析",
    "competitiveness": "价格竞争力分析",
    "strategy": "定价策略建议"
  },
  "marketPositioning": {
    "targetAudience": "目标用户群体",
    "marketSegment": "市场细分",
    "positioning": "市场定位"
  },
  "productFeatures": {
    "coreSellingPoints": ["卖点1", "卖点2", "卖点3"],
    "differentiators": ["差异化1", "差异化2"],
    "uniqueValue": "独特价值主张"
  },
  "salesPerformance": {
    "trend": "销量趋势分析",
    "userFeedback": "用户反馈分析",
    "marketResponse": "市场响应分析"
  },
  "competitiveAnalysis": {
    "competitors": ["竞品1", "竞品2"],
    "advantages": ["优势1", "优势2"],
    "disadvantages": ["劣势1", "劣势2"],
    "marketShare": "市场份额估计"
  },
  "riskAssessment": {
    "supplyChain": "供应链风险",
    "market": "市场风险",
    "technology": "技术风险",
    "overall": "整体风险等级"
  },
  "opportunities": {
    "growth": "增长潜力",
    "expansion": "扩展机会",
    "trends": "趋势机会"
  },
  "recommendation": {
    "isRecommended": true/false,
    "reason": "推荐理由",
    "improvements": ["改进建议1", "改进建议2"],
    "actionPlan": "行动计划"
  }
}`;
  }

  /**
   * 创建竞争分析提示词
   */
  createCompetitiveAnalysisPrompt(productData) {
    return `请对以下产品进行竞争分析，重点关注竞争环境和市场地位：

产品信息：
- 标题：${productData.title}
- 品牌：${productData.brand || '无'}
- 价格：${productData.current_price}元
- 平台：${productData.platform}
- 销量：${productData.sales_count}
- 评分：${productData.rating}/5.0
- 类别：${productData.category}

请进行以下竞争分析：
1. 竞争激烈程度评估（1-10分）
2. 主要竞争对手识别（3-5个）
3. 竞争优势分析
4. 竞争劣势分析
5. 市场地位评估
6. 竞争策略建议

返回格式：
{
  "competitionIntensity": 数字,
  "mainCompetitors": [
    {
      "name": "竞品名称",
      "brand": "品牌",
      "estimatedPrice": "估计价格",
      "strengths": ["优势1", "优势2"]
    }
  ],
  "competitiveAdvantages": ["优势1", "优势2", "优势3"],
  "competitiveDisadvantages": ["劣势1", "劣势2"],
  "marketPosition": "市场地位描述",
  "competitiveStrategy": "竞争策略建议"
}`;
  }

  /**
   * 创建市场分析提示词
   */
  createMarketAnalysisPrompt(category, keywords, timeRange) {
    return `请分析以下市场信息，并提供市场趋势分析：

分析目标：
- 商品类别：${category}
- 关键词：${keywords.join(', ')}
- 时间范围：${timeRange}

请从以下角度进行市场分析：
1. 市场规模评估
2. 增长趋势分析
3. 消费者需求变化
4. 价格趋势分析
5. 竞争格局分析
6. 机会与威胁识别
7. 市场预测

返回格式：
{
  "marketSize": "市场规模评估",
  "growthTrend": "增长趋势分析",
  "consumerDemand": "消费者需求变化",
  "priceTrend": "价格趋势分析",
  "competitiveLandscape": "竞争格局分析",
  "opportunities": ["机会1", "机会2", "机会3"],
  "threats": ["威胁1", "威胁2"],
  "forecast": "市场预测",
  "recommendations": ["建议1", "建议2", "建议3"]
}`;
  }

  /**
   * 创建产品比较提示词
   */
  createProductComparisonPrompt(products, compareFields) {
    const productList = products.map((product, index) =>
      `产品${index + 1}：
- 标题：${product.title}
- 价格：${product.current_price}元
- 销量：${product.sales_count}
- 评分：${product.rating}
- 平台：${product.platform}`
    ).join('\n\n');

    return `请比较以下产品，重点关注指定的比较维度：

${productList}

比较维度：${compareFields.join(', ')}

请提供详细的产品比较分析：
1. 各产品在每个维度的表现评分（1-10分）
2. 优劣势对比分析
3. 性价比排名
4. 适用场景分析
5. 选择建议

返回格式：
{
  "comparison": [
    {
      "productIndex": 0,
      "scores": {"维度1": 分数, "维度2": 分数},
      "strengths": ["优势1", "优势2"],
      "weaknesses": ["劣势1", "劣势2"]
    }
  ],
  "ranking": [0, 1, 2],
  "valueForMoney": "性价比分析",
  "useCases": "适用场景分析",
  "recommendation": "选择建议"
}`;
  }

  /**
   * 创建趋势预测提示词
   */
  createTrendPredictionPrompt(productId, historicalData, predictionDays) {
    const dataPoints = historicalData.map(point =>
      `${point.date}: ${point.price}元`
    ).join('\n');

    return `请基于以下历史价格数据，预测未来${predictionDays}天的价格趋势：

产品ID：${productId}
历史价格数据：
${dataPoints}

请提供：
1. 价格趋势分析（上升/下降/稳定）
2. 预测价格范围
3. 影响因素分析
4. 置信度评估
5. 建议操作时机

返回格式：
{
  "trend": "上升/下降/稳定",
  "predictedPriceRange": {
    "min": 最低价格,
    "max": 最高价格,
    "average": 平均价格
  },
  "influencingFactors": ["因素1", "因素2", "因素3"],
  "confidence": 置信度百分比,
  "actionTiming": "建议操作时机",
  "reasoning": "预测理由"
}`;
  }
  /**
   * 解析分析结果
   */
  parseAnalysisResult(text, analysisType) {
    try {
      // 尝试提取JSON部分
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // 如果没有找到JSON，返回基础结构
      return {
        score: 0,
        analysis: text,
        error: 'JSON解析失败，返回原始文本'
      };
    } catch (error) {
      logger.error('解析分析结果失败:', error);
      return {
        score: 0,
        analysis: text,
        error: '结果解析失败'
      };
    }
  }

  /**
   * 解析市场分析结果
   */
  parseMarketAnalysis(text) {
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        marketSize: '解析失败',
        analysis: text,
        error: 'JSON解析失败'
      };
    } catch (error) {
      logger.error('解析市场分析失败:', error);
      return {
        analysis: text,
        error: '市场分析解析失败'
      };
    }
  }

  /**
   * 解析比较结果
   */
  parseComparisonResult(text) {
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        comparison: [],
        analysis: text,
        error: 'JSON解析失败'
      };
    } catch (error) {
      logger.error('解析比较结果失败:', error);
      return {
        comparison: [],
        analysis: text,
        error: '比较结果解析失败'
      };
    }
  }

  /**
   * 解析趋势预测结果
   */
  parseTrendPrediction(text) {
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        trend: 'unknown',
        analysis: text,
        error: 'JSON解析失败'
      };
    } catch (error) {
      logger.error('解析趋势预测失败:', error);
      return {
        trend: 'unknown',
        analysis: text,
        error: '趋势预测解析失败'
      };
    }
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(apiKey) {
    try {
      const client = this.createClient(apiKey);
      const response = await client.get('/models');

      if (response.data && response.data.models) {
        return response.data.models
          .filter(model => model.name.includes('gemini'))
          .map(model => ({
            name: model.name,
            displayName: model.displayName,
            description: model.description,
            inputTokenLimit: model.inputTokenLimit,
            outputTokenLimit: model.outputTokenLimit,
            supportedGenerationMethods: model.supportedGenerationMethods
          }));
      }

      return [];
    } catch (error) {
      logger.error('获取模型列表失败:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * 获取使用统计
   */
  async getUsageStats(userId, period = '30d') {
    try {
      const cacheKey = `usage_stats:${userId}:${period}`;
      const cached = await cache.get(cacheKey);

      if (cached) {
        return cached;
      }

      // 这里应该从数据库获取实际的使用统计
      // 暂时返回模拟数据
      const stats = {
        totalRequests: 0,
        totalTokens: 0,
        averageResponseTime: 0,
        successRate: 100,
        period,
        lastUpdated: new Date().toISOString()
      };

      // 缓存5分钟
      await cache.set(cacheKey, stats, 300);

      return stats;
    } catch (error) {
      logger.error('获取使用统计失败:', error);
      throw createError.internal('获取使用统计失败', 'STATS_ERROR');
    }
  }

  /**
   * 优化商品标题和描述
   */
  async optimizeListing(apiKey, title, description, category, targetKeywords) {
    const prompt = `请优化以下商品信息，提高搜索排名和转化率：

原标题：${title}
原描述：${description || '无'}
商品类别：${category}
目标关键词：${targetKeywords.join(', ')}

请提供：
1. 优化后的标题（包含关键词，吸引点击）
2. 优化后的描述（突出卖点，提高转化）
3. 建议的标签
4. SEO优化建议

返回格式：
{
  "optimizedTitle": "优化后的标题",
  "optimizedDescription": "优化后的描述",
  "suggestedTags": ["标签1", "标签2", "标签3"],
  "seoTips": ["SEO建议1", "SEO建议2", "SEO建议3"],
  "improvements": "改进说明"
}`;

    try {
      const result = await this.generateContent(apiKey, prompt, {
        temperature: 0.7,
        maxOutputTokens: 2048
      });

      const optimization = this.parseAnalysisResult(result.text, 'optimization');

      return {
        ...optimization,
        metadata: {
          model: result.model,
          duration: result.duration,
          originalTitle: title,
          category,
          targetKeywords,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('商品信息优化失败:', error);
      throw error;
    }
  }

  /**
   * 分析竞争对手
   */
  async analyzeCompetitor(apiKey, competitorUrl, analysisDepth = 'basic') {
    const prompt = `请分析以下竞争对手信息：

竞争对手URL：${competitorUrl}
分析深度：${analysisDepth}

请提供：
1. 竞争对手基本信息
2. 产品特色分析
3. 定价策略分析
4. 营销策略分析
5. 优劣势对比
6. 应对建议

返回格式：
{
  "competitorInfo": {
    "name": "竞争对手名称",
    "platform": "平台",
    "category": "类别"
  },
  "productFeatures": ["特色1", "特色2", "特色3"],
  "pricingStrategy": "定价策略分析",
  "marketingStrategy": "营销策略分析",
  "strengths": ["优势1", "优势2"],
  "weaknesses": ["劣势1", "劣势2"],
  "recommendations": ["应对建议1", "应对建议2", "应对建议3"]
}`;

    try {
      const result = await this.generateContent(apiKey, prompt, {
        temperature: 0.4,
        maxOutputTokens: 3072
      });

      const analysis = this.parseAnalysisResult(result.text, 'competitor');

      return {
        ...analysis,
        metadata: {
          model: result.model,
          duration: result.duration,
          competitorUrl,
          analysisDepth,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('竞争对手分析失败:', error);
      throw error;
    }
  }
}

module.exports = new GeminiService();
