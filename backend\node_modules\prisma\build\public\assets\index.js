var e=Object.defineProperty,t=Object.defineProperties,s=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,r=(t,s,i)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[s]=i,l=(e,t)=>{for(var s in t||(t={}))a.call(t,s)&&r(e,s,t[s]);if(i)for(var s of i(t))n.call(t,s)&&r(e,s,t[s]);return e},o=(e,i)=>t(e,s(i)),d=(e,t)=>{var s={};for(var r in e)a.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&i)for(var r of i(e))t.indexOf(r)<0&&n.call(e,r)&&(s[r]=e[r]);return s};import{l as c,o as h,a as p,u,b as m,d as g,c as v,v as f,t as y,w as I,r as C,e as w,f as b,g as E,h as _,R as x,i as S,j as N,m as L,k as R,A as M,n as O,F as k}from"./vendor.js";var A=Object.defineProperty,D=Object.getOwnPropertyDescriptor,T=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?D(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&A(t,s,n),n};class P{constructor(){this.transport={type:"http",url:`${window.location.origin}/api`},this.updates=!1,this.readonly=!1}async update(e){c.exports.has(e,"transport")&&(this.transport=e.transport),c.exports.has(e,"updates")&&(this.updates=e.updates),c.exports.has(e,"readonly")&&(this.readonly=e.readonly)}}T([h],P.prototype,"transport",2),T([h],P.prototype,"updates",2),T([h],P.prototype,"readonly",2),T([p],P.prototype,"update",1);var V=new P;class j{constructor(e){this.path=e.path,this.code=e.code,this.type=e.type,this.message=e.message,this.stack=e.stack,this.context=e.context||null,this.nativeError=e.nativeError}}const F=({path:e,message:t,code:s,type:i,stack:a,context:n,nativeError:r})=>{const l=new j({path:e,message:t,code:s,type:i,stack:a,context:n||null,nativeError:r});return console.error(`[${e}] ${t}`,n),console.error(r),l},B=(e,t,s)=>{const i=a=>a===e.length?s&&s():t(e[a],(()=>i(a+1)));return i(0)};const H=[(e,t,s)=>(e.createObjectStore("projects",{keyPath:"id"}),e.createObjectStore("openTabs",{keyPath:"id"}),e.createObjectStore("sessions",{keyPath:"id"}),e.createObjectStore("scripts",{keyPath:"id"}),s()),(e,t,s)=>(e.createObjectStore("models",{keyPath:"id"}),e.createObjectStore("fields",{keyPath:"id"}),e.createObjectStore("enums",{keyPath:"id"}),s()),(e,t,s)=>{const i=t.objectStore("sessions"),a=t.objectStore("scripts"),n=i.getAll();n.onsuccess=()=>{const e=a.getAll();e.onsuccess=()=>{const t=n.result,r=e.result;B(t,((e,t)=>{const s=r.find((t=>t.id===e.scriptId));s?i.put(o(l({},e),{lastSavedHash:s.lastSavedHash||""})).onsuccess=t:e.lastSavedHash=""}),(()=>{B(r,((e,t)=>{delete e.lastSavedHash,a.put(e).onsuccess=t}),(()=>s()))}))}}},(e,t,s)=>{e.createObjectStore("tabs",{keyPath:"id"});const i=t.objectStore("openTabs"),a=t.objectStore("tabs"),n=i.getAll();n.onsuccess=()=>{const t=n.result;B(t,((e,t)=>{if(!e.sessionId)return t();e.preview=!1,a.put(e).onsuccess=t}),(()=>(e.deleteObjectStore("openTabs"),s())))}},(e,t,s)=>{const i=t.objectStore("tabs"),a=t.objectStore("projects"),n=i.getAll();n.onsuccess=()=>{const e=a.getAll();e.onsuccess=()=>{const t=n.result,i=e.result;B(i,((e,s)=>{e.tabOrder=t.filter((t=>t.projectId===e.id)).map((e=>e.id)),a.put(e).onsuccess=s}),(()=>s()))}}},(e,t,s)=>{const i=["tabs","sessions","scripts","models","fields","enums"],a={};B(i,((e,s)=>{const i=t.objectStore(e).getAll();i.onsuccess=()=>{a[e]=i.result,s()}}),(()=>{B(i,((s,i)=>{e.deleteObjectStore(s),e.createObjectStore(s,{keyPath:["id","projectId"]});const n=a[s];B(n,((e,i)=>{t.objectStore(s).put(e).onsuccess=i}),(()=>i()))}),(()=>(e.createObjectStore("actions",{keyPath:["id","projectId"]}),s())))}))},(e,t,s)=>{const i=t.objectStore("projects"),a=i.getAll();a.onsuccess=()=>{const e=a.result;B(e,((e,t)=>{e.theme="light",i.put(e).onsuccess=t}),(()=>s()))}},(e,t,s)=>{const i=t.objectStore("sessions"),a=t.objectStore("scripts"),n=i.getAll();n.onsuccess=()=>{const e=n.result;B(e,((e,t)=>{e.type=e.scriptId?"script":"fallback",i.put(e).onsuccess=t}),(()=>{const e=a.getAll();e.onsuccess=()=>{const t=e.result;B(t,((e,t)=>{e.generated=!1,a.put(e).onsuccess=t}),(()=>s()))}}))}},(e,t,s)=>{const i=t.objectStore("scripts"),a=i.getAll();a.onsuccess=()=>{const e=a.result;B(e,((e,t)=>{e.frozen=e.generated,delete e.generated,i.put(e).onsuccess=t}),(()=>s()))}},(e,t,s)=>(e.deleteObjectStore("models"),e.deleteObjectStore("fields"),e.deleteObjectStore("enums"),s()),(e,t,s)=>{const i=t.objectStore("projects");i.createIndex("createdAt","createdAt"),i.createIndex("updatedAt","updatedAt");const a=t.objectStore("actions");a.createIndex("createdAt","createdAt"),a.createIndex("updatedAt","updatedAt");const n=t.objectStore("scripts");n.createIndex("createdAt","createdAt"),n.createIndex("updatedAt","updatedAt");const r=t.objectStore("sessions");r.createIndex("createdAt","createdAt"),r.createIndex("updatedAt","updatedAt");const l=t.objectStore("tabs");l.createIndex("createdAt","createdAt"),l.createIndex("updatedAt","updatedAt");const o=i.getAll();return o.onsuccess=()=>{const e=o.result;B(e,((e,t)=>{delete e.tabOrder,e.createdAt=(new Date).toISOString(),e.updatedAt=(new Date).toISOString(),i.put(e).onsuccess=t}),(()=>{B([a,n,r,l],((e,t)=>{const s=e.getAll();s.onsuccess=()=>{const i=s.result;B(i,((t,s)=>{t.createdAt=(new Date).toISOString(),t.updatedAt=(new Date).toISOString(),e.put(t).onsuccess=s}),(()=>t()))}}),(()=>s()))}))},s()},(e,t,s)=>{const i=t.objectStore("projects");i.deleteIndex("createdAt"),i.createIndex("createdAt",["id","createdAt"]),i.deleteIndex("updatedAt"),i.createIndex("updatedAt",["id","updatedAt"]);const a=t.objectStore("actions");a.deleteIndex("createdAt"),a.createIndex("createdAt",["id","projectId","createdAt"]),a.deleteIndex("updatedAt"),a.createIndex("updatedAt",["id","projectId","updatedAt"]);const n=t.objectStore("scripts");n.deleteIndex("createdAt"),n.createIndex("createdAt",["id","projectId","createdAt"]),n.deleteIndex("updatedAt"),n.createIndex("updatedAt",["id","projectId","updatedAt"]);const r=t.objectStore("sessions");r.deleteIndex("createdAt"),r.createIndex("createdAt",["id","projectId","createdAt"]),r.deleteIndex("updatedAt"),r.createIndex("updatedAt",["id","projectId","updatedAt"]);const l=t.objectStore("tabs");return l.deleteIndex("createdAt"),l.createIndex("createdAt",["id","projectId","createdAt"]),l.deleteIndex("updatedAt"),l.createIndex("updatedAt",["id","projectId","updatedAt"]),s()},(e,t,s)=>{const i=t.objectStore("scripts"),a=i.getAll();a.onsuccess=()=>{const e=a.result;B(e,((e,t)=>{e.where=e.where.map((e=>(e.fieldIds=[e.fieldId],delete e.fieldId,e))),i.put(e).onsuccess=t}),(()=>s()))}}],Z=(e,t,s,i)=>{console.log("------Starting IndexedDB migration------");const a=u(i);B(H.slice(t),((t,s)=>t(e,a,s)),(()=>console.log("------IndexedDB migration complete------")))};class q{constructor(e,t){this.cursor=()=>this.db.transaction(this.storeName).store.openCursor(),this.transaction=e=>this.db.transaction(this.storeName,e),this.getAll=async({projectId:e}={})=>{const t=await this.db.getAllFromIndex(this.storeName,"createdAt");return e?t.filter((t=>t.projectId===e)):t},this.create=async e=>{try{await this.db.put(this.storeName,o(l({},e),{createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()}))}catch(t){console.log("Error during PersistenceItem.create",t,this.storeName)}},this.update=async(e,t)=>{try{const t=await this.db.get(this.storeName,e);await this.db.put(this.storeName,o(l({},t),{updatedAt:(new Date).toISOString()}))}catch(s){console.log("Error during PersistenceItem.update",s,this.storeName)}},this.delete=async e=>{try{await this.db.delete(this.storeName,e)}catch(t){console.log("Error during PersistenceItem.delete",t,this.storeName)}},this.clear=async()=>{try{await this.db.clear(this.storeName)}catch(e){console.log("Error during PersistenceItem.clear",e,this.storeName)}},this.storeName=e,this.db=t}}var U=new class{constructor(){this.databaseName="Prisma Studio",this.indexedDBVersion=13,this.databaseInstance=null,this.projectId="",this.ready=!1,this.init=async({projectId:e})=>{try{this.projectId=e,this.databaseInstance=await m(this.databaseName,this.indexedDBVersion,{upgrade:Z}),this.projects=new q("projects",this.databaseInstance),this.tabs=new q("tabs",this.databaseInstance),this.sessions=new q("sessions",this.databaseInstance),this.scripts=new q("scripts",this.databaseInstance),this.actions=new q("actions",this.databaseInstance),this.ready=!0}catch(t){throw F({path:"PersistenceStore.init",message:"Unable to init PersistenceStore",nativeError:t})}},this.save=(e,t)=>new Promise((async(s,i)=>{if(!this.ready)throw F({path:"PersistenceStore.save",message:"PersistenceStore is not ready to receive `save` operations yet",context:{tableName:e,value:t}});try{switch(e){case"sessions":await this.sessions.create(t);break;case"scripts":await this.scripts.create(t);break;case"tabs":await this.tabs.create(t);break;case"projects":await this.projects.create(t);break;case"actions":await this.actions.create(t)}s()}catch(a){i(a)}})),this.load=async e=>new Promise((async(t,s)=>{if(!this.ready)throw F({path:"PersistenceStore.load",message:"PersistenceStore is not ready to receive `load` operations yet",context:{tableName:e}});try{switch(e){case"projects":return t(await this.projects.getAll());default:return t(await this[e].getAll({projectId:this.projectId}))}}catch(i){return s(i)}})),this.remove=async(e,t)=>new Promise((async(s,i)=>{if(!this.ready)throw F({path:"PersistenceStore.remove",message:"PersistenceStore is not ready to receive `remove` operations yet",context:{tableName:e,id:t}});try{switch(e){case"projects":return await this[e].delete(t),s();default:return await this[e].delete([t,this.projectId]),s()}}catch(a){return i(a)}})),this.clear=async e=>new Promise((async(t,s)=>{if(!this.ready)throw F({path:"PersistenceStore.clear",message:"PersistenceStore is not ready to receive `clear` operations yet",context:{tableName:e}});try{return await this[e].clear(),t()}catch(i){return s(i)}})),this.clearAll=async()=>{var e;null==(e=this.databaseInstance)||e.close(),await g(this.databaseName)}}},z=Object.defineProperty,$=Object.getOwnPropertyDescriptor,J=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?$(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&z(t,s,n),n};class W{constructor(e,{idbTableName:t}={}){this.idbTableName=null,this.members={},this.type=e,this.idbTableName=null!=t?t:null}get values(){return this.members}get size(){return Object.keys(this.members).length}async restore(){if(this.idbTableName){(await U.load(this.idbTableName)).forEach((e=>{e?this.add(e,{skipPersist:!0}):console.warn("Attempt to restore a null member from IndexedDB, ignoring",{member:e,idbTableName:this.idbTableName})}))}return Promise.resolve()}get(e){return e&&this.members[e]||null}add(e,{skipPersist:t=!1}={}){let s;if(e.id||(e.id=f()),this.members[e.id])s=this.members[e.id],s.update(e,{skipPersist:t});else{s=new(0,this.type)(e),s.idbTableName=this.idbTableName,this.members[e.id]=s,!t&&this.idbTableName&&U.save(this.idbTableName,s.serialize())}return s}remove(e){const t=this.members[e];return t&&delete this.members[e],this.idbTableName&&U.remove(this.idbTableName,e),t}clear(){this.members={},this.idbTableName&&U.clear(this.idbTableName)}toJS(){return y(this)}}J([h],W.prototype,"type",2),J([h],W.prototype,"idbTableName",2),J([h],W.prototype,"members",2),J([v],W.prototype,"values",1),J([v],W.prototype,"size",1),J([p],W.prototype,"restore",1),J([p],W.prototype,"add",1),J([p],W.prototype,"remove",1),J([p],W.prototype,"clear",1),J([p],W.prototype,"toJS",1);var K=Object.defineProperty,G=Object.getOwnPropertyDescriptor,Q=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?G(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&K(t,s,n),n};class Y{constructor(e){this.forceUpdate=async()=>{await U.save(this.idbTableName,this.serialize())},this.id=e.id}update(e,{skipPersist:t=!1}={}){if(!t&&this.idbTableName){const t=this.serialize(),s=Object.keys(t),i=Object.keys(e);new Set([...s,...i]).size!==s.length+i.length&&this.forceUpdate()}}serialize(){}}Q([h],Y.prototype,"id",2),Q([h],Y.prototype,"idbTableName",2),Q([p],Y.prototype,"update",1),Q([p],Y.prototype,"forceUpdate",2);const X=(e,t)=>`${e}.${t}`;var ee=Object.defineProperty,te=Object.getOwnPropertyDescriptor,se=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?te(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&ee(t,s,n),n};class ie extends Y{constructor(e){super(e),this.id=e.id,this.name=e.name,this.values=e.values}update(e,t={}){c.exports.has(e,"name")&&(this.name=e.name),c.exports.has(e,"values")&&(this.values=e.values),super.update(e,t)}}se([h],ie.prototype,"id",2),se([h],ie.prototype,"name",2),se([h],ie.prototype,"values",2),se([p],ie.prototype,"update",1);var ae=Object.defineProperty,ne=Object.getOwnPropertyDescriptor;class re extends W{constructor(){super(ie),this.getByName=e=>this.get(e)}add(e,t={}){return super.add(l({id:e.name},e),t)}}((e,t,s,i)=>{for(var a,n=i>1?void 0:i?ne(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);i&&n&&ae(t,s,n)})([p],re.prototype,"add",1);var le=new re,oe=Object.defineProperty,de=Object.getOwnPropertyDescriptor,ce=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?de(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&oe(t,s,n),n};class he extends Y{constructor(e){if(super(e),this.id=e.id,this.modelId=e.modelId,this.name=e.name,this.type=e.type,this.kind=e.kind,"Json"===this.type&&"string"==typeof e.default)try{this.default=JSON.parse(e.default)}catch(t){this.default=e.default}else if("BigInt"===this.type&&"string"==typeof e.default)try{this.default=BigInt(e.default)}catch(t){this.default=e.default}else this.default=e.default;this.isId=e.isId,this.isUnique=e.isUnique,this.isRequired=e.isRequired,this.isList=e.isList,this.isReadOnly=e.isReadOnly,this.isUpdatedAt=e.isUpdatedAt,this.relationName=e.relationName,this.relationFromFieldNames=e.relationFromFieldNames,this.relationToFieldNames=e.relationToFieldNames}get model(){return as.get(this.modelId)}get isScalar(){return"scalar"===this.kind&&!this.isEnum}get isString(){return"String"===this.type}get isInt(){return"Int"===this.type}get isBigInt(){return"BigInt"===this.type}get isFloat(){return"Float"===this.type}get isDecimal(){return"Decimal"===this.type}get isBoolean(){return"Boolean"===this.type}get isDateTime(){return"DateTime"===this.type}get isJson(){return"Json"===this.type}get isBytes(){return"Bytes"===this.type}get isEnum(){return!!this.typeAsEnum}get isRelation(){return"object"===this.kind}get isScalarListRelation(){return this.isScalar&&this.isList&&this.isPartOfRelation||!1}get isScalarListTwoWayMNRelation(){var e;if(this.isScalarListRelation){const t=(null==(e=this.model)?void 0:e.name)||null;if(!t)return!1;const s=this.relationItIsPartOf;if(!s)return!1;const i=as.getByName(s.type);if(!i)return!1;const a=i.fields.find((e=>e.type===t));if(!a)return!1;const n=a.relationFromFieldNames[0],r=i.getFieldByName(n);if(!r)return!1;if(r.isScalarListRelation)return!0}return!1}get isPartOfRelation(){return null!==this.relationItIsPartOf}get relationItIsPartOf(){return this.model&&this.model.fields.find((e=>e.isRelation&&e.relationFromFieldNames.includes(this.name)))||null}get isSortable(){return this.isScalar&&!this.isList||this.isEnum&&!this.isList}get isFunctionDefault(){return"string"!=typeof this.default&&"number"!=typeof this.default&&"boolean"!=typeof this.default&&"bigint"!=typeof this.default&&!c.exports.isArray(this.default)}get defaultAsString(){return this.isList?"[]":this.default?"string"==typeof this.default||"number"==typeof this.default||"boolean"==typeof this.default?`${this.default}`:"bigint"==typeof this.default||c.exports.isArray(this.default)?this.default.toString():c.exports.isObject(this.default)?`${this.default.name}()`:"":""}get placeholder(){return this.isList?"[]":this.isString?"Value":this.isInt||this.isFloat||this.isBigInt||this.isDecimal?"1337":this.isBoolean?"false":this.isDateTime?(new Date).toISOString():this.isJson?"{}":this.isEnum?this.typeAsEnum.values[0]:this.isRelation?"ID":"Value"}get typeAsModel(){return this.isRelation?as.getByName(this.type):null}get typeAsEnum(){return le.getByName(this.type)}get typeAsLabel(){let e=this.type;const t=this.typeAsModel;return t&&(e=t.name),this.isList?e+="[]":this.isRequired||(e+="?"),e}get lowestValidValue(){if(this.isList)return[];if(!this.isRequired)return null;if(this.isString)return"";if(this.isInt||this.isFloat||this.isDecimal)return 0;if(this.isBigInt)return BigInt(0);if(this.isBoolean)return!1;if(this.isDateTime)return new Date(0).toISOString();if(this.isJson)return{};if(this.isEnum){if(!this.typeAsEnum)throw F({path:"Field.lowestValidValue",message:"Invalid type of field: enum",context:{fieldId:this.id,type:this.type}});return this.typeAsEnum.values[0]}return null}get getRelationIDFieldName(){if(!this.isRelation)return null;const e=as.getByName(this.type);return e&&e.idField?e.idField.name:null}update(e,t={}){c.exports.has(e,"default")&&(this.default=e.default),c.exports.has(e,"isId")&&(this.isId=e.isId),c.exports.has(e,"isUnique")&&(this.isUnique=e.isUnique),c.exports.has(e,"isRequired")&&(this.isRequired=e.isRequired),c.exports.has(e,"isList")&&(this.isList=e.isList),c.exports.has(e,"isReadOnly")&&(this.isReadOnly=e.isReadOnly),c.exports.has(e,"isUpdatedAt")&&(this.isUpdatedAt=e.isUpdatedAt),super.update(e,t)}}ce([h],he.prototype,"id",2),ce([h],he.prototype,"modelId",2),ce([h],he.prototype,"name",2),ce([h],he.prototype,"type",2),ce([h],he.prototype,"kind",2),ce([h],he.prototype,"default",2),ce([h],he.prototype,"isId",2),ce([h],he.prototype,"isUnique",2),ce([h],he.prototype,"isRequired",2),ce([h],he.prototype,"isList",2),ce([h],he.prototype,"isReadOnly",2),ce([h],he.prototype,"isUpdatedAt",2),ce([h],he.prototype,"relationName",2),ce([h],he.prototype,"relationFromFieldNames",2),ce([h],he.prototype,"relationToFieldNames",2),ce([v],he.prototype,"model",1),ce([v],he.prototype,"isScalar",1),ce([v],he.prototype,"isString",1),ce([v],he.prototype,"isInt",1),ce([v],he.prototype,"isBigInt",1),ce([v],he.prototype,"isFloat",1),ce([v],he.prototype,"isDecimal",1),ce([v],he.prototype,"isBoolean",1),ce([v],he.prototype,"isDateTime",1),ce([v],he.prototype,"isJson",1),ce([v],he.prototype,"isBytes",1),ce([v],he.prototype,"isEnum",1),ce([v],he.prototype,"isRelation",1),ce([v],he.prototype,"isScalarListRelation",1),ce([v],he.prototype,"isScalarListTwoWayMNRelation",1),ce([v],he.prototype,"isPartOfRelation",1),ce([v],he.prototype,"relationItIsPartOf",1),ce([v],he.prototype,"isSortable",1),ce([v],he.prototype,"isFunctionDefault",1),ce([v],he.prototype,"defaultAsString",1),ce([v],he.prototype,"placeholder",1),ce([v],he.prototype,"typeAsModel",1),ce([v],he.prototype,"typeAsEnum",1),ce([v],he.prototype,"typeAsLabel",1),ce([v],he.prototype,"lowestValidValue",1),ce([v],he.prototype,"getRelationIDFieldName",1),ce([p],he.prototype,"update",1);var pe=new class extends W{constructor(){super(he)}getByName(e,t){return this.get(X(t,e))}},ue=Object.defineProperty,me=Object.getOwnPropertyDescriptor,ge=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?me(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&ue(t,s,n),n};const ve=class{constructor(){this.previousError={type:null},this.updateError=(e={})=>{c.exports.has(e,"visible")&&(this.error.visible=e.visible)},this.updateActions=(e={})=>{c.exports.has(e,"visible")&&(this.actions.visible=e.visible)},this.setPreviousError=e=>{this.previousError=e,localStorage.setItem(ve.previousErrorLocalStorageKey,JSON.stringify(e))},this.error={visible:!1};const e=localStorage.getItem(ve.previousErrorLocalStorageKey);e&&(this.previousError=JSON.parse(e)),this.actions={visible:!1}}};let fe=ve;fe.previousErrorLocalStorageKey="previousError",ge([h],fe.prototype,"error",2),ge([h],fe.prototype,"actions",2),ge([h],fe.prototype,"previousError",2),ge([p],fe.prototype,"updateError",2),ge([p],fe.prototype,"updateActions",2),ge([p],fe.prototype,"setPreviousError",2);var ye=new fe;const Ie=(e,t)=>{if(null==t)throw F({path:"getRecordId",message:"Invalid recordValue",context:{modelId:e,recordValue:t}});const s=as.get(e);if(!s)throw F({path:"getRecordId",message:"Invalid modelId",context:{modelId:e}});let i=`${e}::`;return i+=s.uniqueIdentifier.fields.map((e=>null===t[e.name]?"null":void 0===t[e.name]?"undefined":t[e.name])).join(","),i};var Ce=Object.defineProperty,we=Object.getOwnPropertyDescriptor,be=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?we(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Ce(t,s,n),n};class Ee{constructor(e){this.update=e=>{c.exports.has(e,"selectedRecordIds")&&(this.selectedRecordIds=e.selectedRecordIds)},this.sessionId=e.sessionId,this.selectedRecordIds=[]}get session(){return He.get(this.sessionId)}get maxRows(){var e,t;return(null==(t=null==(e=this.session)?void 0:e.script)?void 0:t.recordIds.length)||0}get maxColumns(){var e,t;return(null==(t=null==(e=this.session)?void 0:e.script)?void 0:t.fieldIds.length)||0}get selectedRecords(){return this.selectedRecordIds.map((e=>at.get(e)))}}be([h],Ee.prototype,"sessionId",2),be([h],Ee.prototype,"selectedRecordIds",2),be([v],Ee.prototype,"session",1),be([v],Ee.prototype,"maxRows",1),be([v],Ee.prototype,"maxColumns",1),be([v],Ee.prototype,"selectedRecords",1),be([p],Ee.prototype,"update",2);const _e=(e,t)=>{const s=e.split("::"),i=Number(s[0].slice(1,-1));if(isNaN(i))throw F({path:"parseTreePath",message:"Invalid tree path: Failed to parse segment as index",context:{path:e,recordIdx:i}});const a=at.get(t[i]);if(!a)throw F({path:"parseTreePath",message:"Invalid tree path: Invalid record index",context:{path:e,recordIdx:i}});return s.slice(1).reduce((({model:t,field:s,index:i,record:a,value:n,arraySliceIndices:r},l)=>{var o,d,c,h,p,u;if(l.startsWith("[")&&l.endsWith("]"))if(s=null,i=Number(l.slice(1,-1)),isNaN(i)){const e=l.slice(1,-1).split("-");r=[Number(e[0]),Number(e[1])],i=null,a=null,n=n.slice(r[0],r[1]+1)}else if(t){const c=Ie(t.id,n[i]);if(!c)throw F({path:"parseTreePath",message:"Invalid tree path: Failed to parse tree path segment as array",context:{path:e,fieldName:l,recordId:c,accumulator:{model:t,field:s,index:i,record:a,value:n,arraySliceIndices:r}}});n=null!=(d=null==(a=null!=(o=at.get(c))?o:null)?void 0:a.value)?d:null}else a=null,n=null!=(c=n[i])?c:null;else{if(!t)throw F({path:"parseTreePath",message:"Invalid tree path: Failed to parse tree path segment as field name",context:{path:e,fieldName:l,accumulator:{model:t,field:s,index:i,record:a,value:n,arraySliceIndices:r}}});if(!(s=t.getFieldByName(l)))throw F({path:"parseTreePath",message:"Invalid field name",context:{path:e,fieldName:l,accumulator:{model:t,field:s,index:i,record:a,value:n,arraySliceIndices:r}}});if(t=null!=(h=s.typeAsModel)?h:null,i=null,a=null,n=n[l],r=null,!s.isScalar&&!s.isEnum&&!Array.isArray(n)&&null!=n){const o=t&&Ie(t.id,n);if(!o)throw F({path:"parseTreePath",message:"Invalid tree path: Failed to parse tree path segment as relation",context:{path:e,fieldName:l,recordId:o,accumulator:{model:t,field:s,index:i,record:a,value:n,arraySliceIndices:r}}});n=null!=(u=null==(a=null!=(p=at.get(o))?p:null)?void 0:a.value)?u:null}}return{model:t,field:s,index:i,record:a,value:n,arraySliceIndices:r}}),{model:a.model,field:null,index:null,record:a,value:a.value,arraySliceIndices:null})};var xe=Object.defineProperty,Se=Object.getOwnPropertyDescriptor,Ne=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Se(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&xe(t,s,n),n};class Le{constructor(e){this.selectionOrder=[],this.isExpanded=e=>this.expandedPaths.includes(e),this.select=e=>{this.activePath=e},this.move=(e,t)=>{0===this.selectionOrder.length&&this.setSelectionOrder();const s=this.selectionOrder.findIndex((e=>e===this.activePath));let i;switch(t){case"up":i=this.selectionOrder[s-e];break;case"down":i=this.selectionOrder[s+e]}this.activePath=i||"[0]"},this.expand=()=>{var e,t;if(this.isExpanded(this.activePath))return;if(!(null==(e=this.session)?void 0:e.script))throw F({path:"SessionSelectionTree.expand",message:"Unable to resolve recordIds",context:{sessionId:this.sessionId,scriptId:null==(t=this.session)?void 0:t.scriptId}});const{record:s,value:i,field:a}=_e(this.activePath,this.session.script.recordIds);null!=i&&((null==a?void 0:a.isList)||!(null==a?void 0:a.isScalar)&&!(null==a?void 0:a.isEnum))&&0!==(null==i?void 0:i.length)&&(this.expandedPaths.push(this.activePath),this.setSelectionOrder(),s&&s.fetchRelations())},this.collapse=()=>{this.expandedPaths=this.expandedPaths.filter((e=>!e.startsWith(this.activePath))),this.setSelectionOrder()},this.update=e=>{c.exports.has(e,"isEditing")&&(this.isEditing=e.isEditing)},this.reset=()=>{this.isEditing=!1},this.sessionId=e.sessionId,this.isEditing=e.isEditing,this.activePath="",this.expandedPaths=[]}get session(){return He.get(this.sessionId)}jumpToParent(){const e=this.activePath.split("::").slice(0,-1).join("::");""!=e&&(this.activePath=e)}setSelectionOrder(){var e,t;if(!(null==(e=this.session)?void 0:e.script))throw F({path:"SessionSelectionTree.setSelectionOrder",message:"Unable to resolve recordIds",context:{sessionId:this.sessionId,scriptId:null==(t=this.session)?void 0:t.scriptId}});const{recordIds:s}=this.session.script;let i=s.map(((e,t)=>`[${t}]`));this.expandedPaths.forEach((e=>{const{value:t,model:a}=_e(e,s),n=i.findIndex((t=>t===e)),r=[];if(r.push(i[n]),Array.isArray(t))if(t.length<=100)r.push(...Array.from({length:t.length}).map(((t,s)=>`${e}::[${s}]`)));else{const s=Math.floor(t.length/100);r.push(...Array.from({length:s}).map(((t,s)=>`${e}::[${100*s}-${100*(s+1)-1}]`))),t.length%100!=0&&r.push(`${e}::[${100*s}-${100*s+t.length%100-1}]`)}else null!==t&&a&&r.push(...a.fields.map((t=>`${e}::${t.name}`)));i=[...i.slice(0,n),...r,...i.slice(n+1)]})),this.selectionOrder=i}}Ne([h],Le.prototype,"sessionId",2),Ne([h],Le.prototype,"isEditing",2),Ne([h],Le.prototype,"activePath",2),Ne([h],Le.prototype,"expandedPaths",2),Ne([h],Le.prototype,"selectionOrder",2),Ne([v],Le.prototype,"session",1),Ne([p],Le.prototype,"select",2),Ne([p],Le.prototype,"move",2),Ne([p],Le.prototype,"expand",2),Ne([p],Le.prototype,"collapse",2),Ne([p],Le.prototype,"jumpToParent",1),Ne([p],Le.prototype,"setSelectionOrder",1),Ne([p],Le.prototype,"update",2),Ne([p],Le.prototype,"reset",2);var Re=Object.defineProperty,Me=Object.getOwnPropertyDescriptor,Oe=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Me(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Re(t,s,n),n};class ke{constructor(e){this.table=new Ee(e.table),this.tree=new Le(e.tree)}}Oe([h],ke.prototype,"table",2),Oe([h],ke.prototype,"tree",2);var Ae=Object.defineProperty,De=Object.getOwnPropertyDescriptor,Te=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?De(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Ae(t,s,n),n};class Pe extends Y{constructor(e){var t,s;super(e),this.createUncommittedRecord=()=>{if(!this.isScript)return;const e=xs.add({type:"create",recordId:null,sessionId:this.id,value:{modelId:this.script.modelId}});xs.add({type:"update",recordId:e.recordId,sessionId:this.id,value:this.script.model.fields.reduce(((e,t)=>(t.isId?e[t.name]=void 0:void 0!==t.default?t.isFunctionDefault?e[t.name]=void 0:e[t.name]=t.default:t.isUpdatedAt?e[t.name]=(new Date).toISOString():!t.isScalar&&!t.isEnum||t.isPartOfRelation?t.isRelation&&t.isList?e[t.name]=t.lowestValidValue:e[t.name]=void 0:e[t.name]=t.lowestValidValue,e)),{})})},this.update=(e,t={})=>{c.exports.has(e,"lastSavedHash")&&(this.lastSavedHash=e.lastSavedHash),super.update(e,t)},this.serialize=()=>({projectId:Kt.activeProjectId,id:this.id,type:this.type,lastSavedHash:this.lastSavedHash,scriptId:this.scriptId}),this.id=e.id,this.type=e.type,this.scriptId=null!=(t=e.scriptId)?t:null,this.lastSavedHash=null!=(s=e.lastSavedHash)?s:this.hash,this.selection=new ke({table:{sessionId:this.id,selectedRecordIds:[]},tree:{sessionId:this.id,isEditing:!1}})}get script(){if(!this.isScript)throw F({path:"Session.script",message:"Invalid `get` call to script: Session is not a `script` type",context:{type:this.type,scriptId:this.scriptId}});const e=St.get(this.scriptId);if(!e)throw F({path:"Session.script",message:"Invalid scriptId in session",context:{type:this.type,scriptId:this.scriptId}});return e}get name(){return this.isScript?this.script.model.name:"Session Name"}get isScript(){return"script"===this.type}get isModelList(){return"model-list"===this.type}get isDirty(){return this.isScript?!this.script.frozen&&(null===this.script.name||(!!Object.values(xs.values).filter((e=>e.sessionId===this.id))||null!==this.script.name&&this.lastSavedHash!==this.hash)):this.lastSavedHash!==this.hash}get hash(){return this.isScript?this.script.hash:""}forceSave(){this.isDirty&&this.update({lastSavedHash:this.hash})}discardChanges(){if(this.isScript)try{const{code:e,modelId:t,where:s,fieldIds:i,sortFieldId:a,sortOrder:n}=JSON.parse(this.lastSavedHash);this.script.update({code:e,modelId:t,where:s,fieldIds:i,sort:{fieldId:a,order:n}})}catch(e){console.log("Could not restore session",e)}}}Te([h],Pe.prototype,"id",2),Te([h],Pe.prototype,"type",2),Te([h],Pe.prototype,"scriptId",2),Te([h],Pe.prototype,"lastSavedHash",2),Te([v],Pe.prototype,"script",1),Te([v],Pe.prototype,"name",1),Te([v],Pe.prototype,"isScript",1),Te([v],Pe.prototype,"isModelList",1),Te([v],Pe.prototype,"isDirty",1),Te([v],Pe.prototype,"hash",1),Te([p],Pe.prototype,"createUncommittedRecord",2),Te([p],Pe.prototype,"forceSave",1),Te([p],Pe.prototype,"discardChanges",1),Te([p],Pe.prototype,"update",2);var Ve=Object.defineProperty,je=Object.getOwnPropertyDescriptor,Fe=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?je(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Ve(t,s,n),n};class Be extends W{constructor(){super(Pe,{idbTableName:"sessions"}),this.findOrCreate=({scriptId:e})=>Object.values(this.values).find((t=>t.scriptId===e))||this.add({type:"script",scriptId:e,lastSavedHash:null}),this.remove=e=>{const t=super.remove(e);return Object.values(xs.values).forEach((t=>{t.sessionId===e&&xs.remove(t.id)})),t}}}Fe([p],Be.prototype,"findOrCreate",2),Fe([p],Be.prototype,"remove",2);var He=new Be;const Ze=(e,t)=>e.isList?Array.isArray(t)?t.every((t=>e.isEnum?!qe(e,t):e.isRelation?!Ue(e,t):!ze(e,t)))?void 0:"Every value in this list must be valid":"Value must be a list":e.isEnum?qe(e,t):e.isRelation?Ue(e,t):ze(e,t),qe=(e,t)=>{if(!e.isRequired&&c.exports.isNil(t))return;if(void 0===t&&void 0!==e.default)return;const s=e.typeAsEnum;return s&&s.values.includes(t)?void 0:"Value must be an Enum variant"},Ue=(e,t)=>{if(!e.isRequired&&c.exports.isNil(t))return;if(e.isRequired&&!t)return"Required fields must not be `null`";return e.typeAsModel?void 0:`Value must be a ${e.type} identifier`},ze=(e,t)=>{if((void 0!==t||void 0===e.default)&&(e.isList||e.isRequired||!c.exports.isNil(t)))return e.isRequired&&c.exports.isNil(t)?"Required fields must not be `null`":e.isString&&"string"!=typeof t?"Value must be a String":!e.isInt||"number"==typeof t&&!isNaN(t)&&Number.isInteger(t)?e.isFloat&&("number"!=typeof t||isNaN(t))?"Value must be a Float":e.isBigInt&&"bigint"!=typeof t?"Value must be a valid BigInt":e.isBoolean&&"boolean"!=typeof t?"Value must be a Boolean":e.isDateTime&&isNaN(new Date(String(t)))?"Value must be a valid DateTime":e.isJson&&"object"!=typeof t?"Value must be a valid Json":void 0:"Value must be an Integer"};var $e=Object.defineProperty,Je=Object.getOwnPropertyDescriptor,We=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Je(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&$e(t,s,n),n};class Ke extends Y{constructor(e){if(super(e),this.serialize=()=>({projectId:Kt.activeProjectId,id:this.id,type:this.type,recordId:this.recordId,sessionId:this.sessionId,value:y(this.value)}),this.id=e.id,this.sessionId=e.sessionId,this.type=e.type,this.value=e.value,"create"===this.type){const t=`tmp--${this.id}`,s=e.value.modelId;at.add({id:t,modelId:s,value:{}}),this.recordId=t}else this.recordId=e.recordId}get record(){return this.recordId?at.get(this.recordId):null}get session(){return He.get(this.sessionId)}get isValid(){return(e=>{const t=xs.get(e);if(!t)throw F({path:"isActionValid",message:"Invalid action",context:{actionId:e}});const s=at.get(t.recordId);if(!s)return!1;const i=s.model;if(!i)return!1;const a=Object.keys(t.value);switch(t.type){case"create":return!!as.get(t.value.modelId);case"delete":return!!at.get(t.recordId);case"update":return a.every((e=>{const s=i.getFieldByName(e),a=t.value[e];return!!s&&!Ze(s,a)}));default:return!1}})(this.id)}update(e,t={}){c.exports.has(e,"recordId")&&(this.recordId=e.recordId),c.exports.has(e,"sessionId")&&(this.sessionId=e.sessionId),c.exports.has(e,"value")&&(this.value=e.value),super.update(e,t)}}We([h],Ke.prototype,"id",2),We([h],Ke.prototype,"recordId",2),We([h],Ke.prototype,"sessionId",2),We([h],Ke.prototype,"type",2),We([h],Ke.prototype,"value",2),We([v],Ke.prototype,"record",1),We([v],Ke.prototype,"session",1),We([v],Ke.prototype,"isValid",1),We([p],Ke.prototype,"update",1);const Ge=async e=>{var t,s;console.log("Running query: ",e);let{error:i,data:a}=await window.transport.request({channel:"prisma",action:"clientRequest",payload:{data:l({schemaHash:Kt.activeProject.schemaHash},e)}});if(i)throw F({path:"runQuery",code:i.code,type:i.type,stack:i.stack,message:`Error in Prisma Client request: \n\n${i.message}`,context:{code:e,error:i}});if(!a)throw F({path:"runQuery",message:`Malformed response from Prisma Client: \n\n${a}`,context:{code:e}});const n=as.get(e.modelName);if(!n)throw F({path:"runQuery",message:"Unrecognized Model",context:{code:e,response:a}});const r=Object.entries((null==(t=e.args)?void 0:t.select)||(null==(s=e.args)?void 0:s.include)||{}).filter((([e,t])=>!!t)).map((([e])=>{var t;return null==(t=pe.getByName(n.id,e))?void 0:t.id})).filter((e=>!!e));if(!a)return Promise.resolve({modelId:n.id,fieldIds:r,recordIds:[]});Array.isArray(a)||(a=[a]);const o=a.map((e=>at.add({modelId:n.id,value:e}).id));return{modelId:n.id,fieldIds:r,recordIds:o}};var Qe=Object.defineProperty,Ye=Object.getOwnPropertyDescriptor,Xe=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Ye(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Qe(t,s,n),n};class et extends Y{constructor(e){super(e),this.fetchRelations=async()=>{try{if(!this.isCommitted)return;await Ge((e=>{const t=at.get(e);if(!t)throw F({path:"getFindOneQuery",message:"Invalid recordId",context:{recordId:e}});const s=t.model,i=s.uniqueIdentifier,a=i.name,n=i.fields.reduce(((e,s)=>(e[s.name]=t.value[s.name],e)),{});let r;return r=1===i.fields.length?n:{[`${a}`]:n},{modelName:s.name,operation:"findUnique",args:{where:r,select:s.fields.reduce(((e,t)=>(e[t.name]=!0,e)),{})}}})(this.id))}catch(e){us.update({type:"client",description:"Unable to fetch record",dump:e.message}),ye.updateError({visible:!0})}},this.id=e.id,this.valueInDB=e.value||{},this.modelId=e.modelId}get model(){let e=as.get(this.modelId);if(!e)throw F({path:"Record.model",message:"Invalid modelId",context:{modelId:this.modelId}});return e}get value(){const e=xs.actions.filter((e=>e.recordId===this.id&&"update"===e.type));if(0==e.length)return this.valueInDB;const t=e[0];return this.model.fields.reduce(((e,s)=>void 0===t.value[s.name]?(e[s.name]=this.valueInDB[s.name],e):(e[s.name]=t.value[s.name],e)),{})}get isCommitted(){return!this.id.startsWith("tmp-")}get dirtyFieldNames(){if(!this.isCommitted)return this.model.fields.map((e=>e.name));const e=xs.actions.filter((e=>{var t;return e.sessionId===(null==(t=Tt.activeTab)?void 0:t.sessionId)&&e.recordId===this.id})).reduce(((e,t)=>(Object.keys(t.value).forEach((t=>{e.add(t)})),e)),new Set);return Array.from(e)}get invalidFields(){return Object.keys(this.value).map((e=>{const t=this.model.getFieldByName(e);if(!t)return;const s=Ze(t,this.value[e]);return s?{field:t,reason:s}:void 0})).filter((e=>!!e))}update(e,t={}){c.exports.has(e,"value")&&(this.valueInDB=l(l({},this.valueInDB),e.value)),super.update(e,t)}}Xe([h],et.prototype,"id",2),Xe([h],et.prototype,"modelId",2),Xe([h],et.prototype,"valueInDB",2),Xe([v],et.prototype,"model",1),Xe([v],et.prototype,"value",1),Xe([v],et.prototype,"isCommitted",1),Xe([v],et.prototype,"dirtyFieldNames",1),Xe([v],et.prototype,"invalidFields",1),Xe([p],et.prototype,"update",1),Xe([p],et.prototype,"fetchRelations",2);var tt=Object.defineProperty,st=Object.getOwnPropertyDescriptor;class it extends W{constructor(){super(et)}add(e,t={}){var s;const i=null!=(s=e.id)?s:Ie(e.modelId,e.value);if(!i)throw F({path:"RecordStore.add",message:"Unable to determine ID for record to create",context:{record:e}});const a=super.add(l({id:i},e));return Object.keys(e.value).forEach((s=>{var i;const n=a.model.getFieldByName(s);if(!n)throw F({path:"RecordStore.add",message:"Invalid field name",context:{fieldName:s,recordValue:e.value}});if(!n.isRelation)return;const r=null==(i=n.typeAsModel)?void 0:i.id;if(!r)throw F({path:"RecordStore.add",message:"Unable to create related records",context:{fieldId:n.id,type:n.type}});if(n.isList)e.value[s].map((e=>{const s=Ie(r,e);!this.get(s)&&e&&this.add({modelId:r,value:e},t)}));else if(null!==e.value[s]){const i=Ie(r,e.value[s]);!this.get(i)&&e.value[s]&&this.add({modelId:r,value:e.value[s]},t)}})),a}}((e,t,s,i)=>{for(var a,n=i>1?void 0:i?st(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);i&&n&&tt(t,s,n)})([p],it.prototype,"add",1);var at=new it;const nt=e=>Array.from(new Set(e)),rt=({modelId:e,where:t,fieldIds:s,sort:i,pagination:a})=>{const n=as.get(e);if(!n)throw F({path:"getFindManyQuery",message:"Invalid modelId",context:{modelId:e}});const r={};if(t&&(null==t?void 0:t.size)>0&&(r.where={AND:Object.values(t.values).reduce(((e,t)=>{var s,i,a,n,r,l,o,d,h;if(!t.enabled||!t.isValid)return e;if(!c.exports.first(t.fields)||!c.exports.last(t.fields))return e;"in"===t.operation||"notIn"===t.operation?t.value=JSON.parse(t.value||"[]"):!(null==(s=c.exports.last(t.fields))?void 0:s.isInt)&&!(null==(i=c.exports.last(t.fields))?void 0:i.isFloat)||(null==(a=c.exports.last(t.fields))?void 0:a.isList)?(null==(n=c.exports.last(t.fields))?void 0:n.isBoolean)&&!(null==(r=c.exports.last(t.fields))?void 0:r.isList)?t.value=Boolean("false"!==t.value&&t.value):(null==(l=c.exports.last(t.fields))?void 0:l.isDateTime)&&!(null==(o=c.exports.last(t.fields))?void 0:o.isList)?t.value=new Date(t.value).toISOString():(null==(d=c.exports.last(t.fields))?void 0:d.isBigInt)&&!(null==(h=c.exports.last(t.fields))?void 0:h.isList)&&(t.value=BigInt(t.value)):t.value=Number(t.value);let p={[`${t.operation}`]:t.value};return"isNull"===t.operation?p={equals:null}:"isNotNull"===t.operation&&(p={not:{equals:null}}),c.exports.last(t.fields).isList&&(p={some:p}),t.fields.length>1&&(p={[`${c.exports.last(t.fields).name}`]:p}),c.exports.first(t.fields).isList&&(p={some:p}),e.push({[c.exports.first(t.fields).name]:p}),e}),[])}),(null==i?void 0:i.fieldId)&&(null==i?void 0:i.order)){const e=pe.get(i.fieldId);if(!e)throw F({path:"getFindManyQuery",message:"Invalid sort fieldId",context:{sort:i}});r.orderBy=[{[`${e.name}`]:i.order}]}void 0!==(null==a?void 0:a.take)&&null!==a.take&&(r.take=Number(a.take)),void 0!==(null==a?void 0:a.skip)&&null!==a.skip&&(r.skip=Number(a.skip)),s=s?nt([].concat(n.uniqueIdentifier.fields.map((e=>e.id)),s)):n.fieldIds;const l=((e,t)=>t.slice().sort(((t,s)=>e.fieldIds.findIndex((e=>e===t))-e.fieldIds.findIndex((e=>e===s)))))(n,s).map((e=>pe.get(e)));return r.select=l.reduce(((e,t)=>{if(!t)return e;if(t.isList&&t.isRelation){const s=t.getRelationIDFieldName;e[t.name]=!s||{select:{[s]:!0}}}else e[t.name]=!0;return e}),{}),{modelName:n.name,operation:"findMany",args:r}};var lt=Object.defineProperty,ot=Object.getOwnPropertyDescriptor,dt=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?ot(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&lt(t,s,n),n};class ct{constructor(e){this.take=e.take,this.skip=e.skip}update(e={}){c.exports.has(e,"take")&&(this.take=e.take),c.exports.has(e,"skip")&&(this.skip=e.skip)}reset(){this.update({take:100,skip:0})}}dt([h],ct.prototype,"take",2),dt([h],ct.prototype,"skip",2),dt([p],ct.prototype,"update",1),dt([p],ct.prototype,"reset",1);var ht=Object.defineProperty,pt=Object.getOwnPropertyDescriptor,ut=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?pt(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&ht(t,s,n),n};class mt{constructor(e){this.fieldId=e.fieldId,this.order=e.order}get field(){return pe.get(this.fieldId)}update(e={}){c.exports.has(e,"fieldId")&&(this.fieldId=e.fieldId),c.exports.has(e,"order")&&(this.order=e.order)}reset(){this.update({fieldId:null,order:"asc"})}}ut([h],mt.prototype,"fieldId",2),ut([h],mt.prototype,"order",2),ut([v],mt.prototype,"field",1),ut([p],mt.prototype,"update",1),ut([p],mt.prototype,"reset",1);const gt=(e,t)=>{if(!e.isScalar)return!1;if(e.isString)return"string"==typeof t;if(e.isInt||e.isFloat||e.isDecimal)return""!==t&&!isNaN(Number(t));if(e.isBigInt)try{return""!==t&&!!BigInt(t)}catch(s){return!1}if(e.isBoolean)return"true"===t||"false"===t;if(e.isDateTime)return!isNaN(new Date(t));if(e.isJson)try{return!!JSON.parse(t)}catch(s){return!1}return!!e.isBytes&&"string"==typeof t},vt=(e,t)=>!!e.isEnum&&e.typeAsEnum.values.includes(t);var ft=Object.defineProperty,yt=Object.getOwnPropertyDescriptor,It=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?yt(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&ft(t,s,n),n};class Ct extends Y{constructor(e){super(e),this.update=(e,t)=>{c.exports.has(e,"fieldIds")&&(this.fieldIds=e.fieldIds),c.exports.has(e,"operation")&&(this.operation=e.operation),c.exports.has(e,"value")&&(this.value=e.value),c.exports.has(e,"enabled")&&(this.enabled=e.enabled),this.script.update({where:[]}),super.update(e,t)},this.serialize=()=>({id:String(this.id),fieldIds:[...this.fieldIds],operation:String(this.operation),value:this.value,scriptId:String(this.scriptId),enabled:this.enabled}),this.id=e.id,this.fieldIds=e.fieldIds,this.operation=e.operation||this.supportedOperations[0],this.value=e.value,this.scriptId=e.scriptId,this.enabled=e.enabled||!0}get fields(){return this.fieldIds.map((e=>{const t=pe.get(e);if(!t)throw F({path:"ScriptWhereItem.fields",message:"Invalid fieldId",context:{fieldId:e}});return t}))}get script(){const e=St.get(this.scriptId);if(!e)throw F({path:"ScriptWhereItem.script",message:"Invalid scriptId",context:{scriptId:this.scriptId}});return e}get supportedOperations(){const e=c.exports.last(this.fields);if(!e)return[];let t;if(e.isRequired&&e.isString)t="StringFilter";else if(!e.isRequired&&e.isString)t="StringNullableFilter";else if(e.isRequired&&e.isInt)t="IntFilter";else if(!e.isRequired&&e.isInt)t="IntNullableFilter";else if(e.isRequired&&e.isFloat)t="FloatFilter";else if(!e.isRequired&&e.isFloat)t="FloatNullableFilter";else if(e.isRequired&&e.isBigInt)t="BigIntFilter";else if(!e.isRequired&&e.isBigInt)t="BigIntNullableFilter";else if(e.isRequired&&e.isDecimal)t="DecimalFilter";else if(!e.isRequired&&e.isDecimal)t="DecimalNullableFilter";else if(e.isRequired&&e.isBoolean)t="BoolFilter";else if(!e.isRequired&&e.isBoolean)t="BoolNullableFilter";else if(e.isRequired&&e.isDateTime)t="DateTimeFilter";else if(!e.isRequired&&e.isDateTime)t="DateTimeNullableFilter";else if(e.isRequired&&e.isJson)t="JsonFilter";else if(!e.isRequired&&e.isJson)t="JsonNullableFilter";else if(e.isRequired&&e.isBytes)t="BytesFilter";else if(!e.isRequired&&e.isBytes)t="BytesNullableFilter";else if(e.isRequired&&e.isEnum)t=`Enum${e.type}Filter`;else if(!e.isRequired&&e.isEnum)t=`Enum${e.type}NullableFilter`;else{if(!e.isRelation)throw F({path:"ScriptWhere.supportedOperations",message:"Unsupported field for `where` filter",context:{fields:this.fields.map((e=>null==e?void 0:e.serialize()))}});t=`${e.type}WhereInput`}const s=Ss.inputObjectTypes.get(t);if(!s)throw F({path:"ScriptWhere.supportedOperations",message:"Could not find appropriate InputType for this field type. This should never happen.",context:{inputTypeName:t,fields:this.fields.map((e=>null==e?void 0:e.serialize()))}});const i=s.fields.map((e=>e.name)).filter((e=>"mode"!==e));return e.isRequired||e.isRelation?i:i.concat(["isNull","isNotNull"])}get isValid(){return(e=>{var t,s,i,a,n,r;if(!e.enabled)return!0;if(!c.exports.last(e.fields))return!1;if(!e.operation)return!1;if(!(null==(t=c.exports.last(e.fields))?void 0:t.isScalar)&&!(null==(s=c.exports.last(e.fields))?void 0:s.isEnum))return!1;if(e.fields.length>1&&!(null==(i=c.exports.first(e.fields))?void 0:i.isRelation))return!1;if(["isNull","isNotNull"].includes(e.operation)&&(void 0===e.value||null===e.value))return!0;if((null==(a=c.exports.last(e.fields))?void 0:a.isRequired)&&(void 0===e.value||null===e.value))return!1;if(void 0===e.value||null===e.value)return!1;if(["in","notIn"].includes(e.operation))try{const t=JSON.parse(e.value);return Array.isArray(t)&&t.every((t=>{var s,i;return(null==(s=c.exports.last(e.fields))?void 0:s.isScalar)?gt(c.exports.last(e.fields),t):!!(null==(i=c.exports.last(e.fields))?void 0:i.isEnum)&&vt(c.exports.last(e.fields),t)}))}catch(l){return!1}return!!e.supportedOperations.includes(e.operation)&&((null==(n=c.exports.last(e.fields))?void 0:n.isScalar)?gt(c.exports.last(e.fields),e.value):!(null==(r=c.exports.last(e.fields))?void 0:r.isEnum)||vt(c.exports.last(e.fields),e.value))})(this)}getFilterableFieldsAtIndex(e){if(e>=this.fieldIds.length)throw F({path:"ScriptWhere.getFilterableFieldsAtIndex",message:"Index is out of range",context:{fieldIds:this.fieldIds,index:e}});if(0===e)return this.script.model.fields.filter((e=>e.isScalar&&!e.isList||e.isEnum&&!e.isList||e.isRelation));if(1===e){return pe.get(this.fieldIds[e-1]).typeAsModel.fields.filter((e=>e.isScalar&&!e.isList||e.isEnum&&!e.isList))}return[]}}It([h],Ct.prototype,"id",2),It([h],Ct.prototype,"fieldIds",2),It([h],Ct.prototype,"operation",2),It([h],Ct.prototype,"value",2),It([h],Ct.prototype,"scriptId",2),It([h],Ct.prototype,"enabled",2),It([v],Ct.prototype,"fields",1),It([v],Ct.prototype,"script",1),It([v],Ct.prototype,"supportedOperations",1),It([v],Ct.prototype,"isValid",1),It([p],Ct.prototype,"update",2);class wt extends W{constructor({modelId:e,scriptId:t}){super(Ct),this.serialize=()=>Object.values(this.values).map((e=>e.serialize())),this.modelId=e,this.scriptId=t}get model(){let e=as.get(this.modelId);if(!e)throw F({path:"ScriptWhere.model",message:"Invalid modelId",context:{modelId:this.modelId}});return e}get script(){return St.get(this.scriptId)}add(e,t){var s;const i=super.add(o(l({},e),{scriptId:this.scriptId}),t);return null==(s=this.script)||s.update({where:[]},t),i}}It([h],wt.prototype,"modelId",2),It([h],wt.prototype,"scriptId",2),It([v],wt.prototype,"model",1),It([v],wt.prototype,"script",1),It([p],wt.prototype,"add",1);var bt=Object.defineProperty,Et=Object.getOwnPropertyDescriptor,_t=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Et(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&bt(t,s,n),n};class xt extends Y{constructor(e){var t,s,i,a,n,r,l,o;super(e),this.run=async()=>{if(!this.model)throw F({path:"Script.run",message:"Invalid modelId",context:{modelId:this.modelId}});this.update({running:!0}),ye.updateError({visible:!1});const e=this.frozen||"visual"===this.inputMode?rt({modelId:this.modelId,where:this.where,sort:this.sort,pagination:this.pagination}):this.code;try{const[,{recordIds:t,modelId:s,fieldIds:i}]=await Promise.all([this.model.runCountQuery(),Ge(e)]);this.update({recordIds:t}),"visual"===this.inputMode&&this.update({code:e}),"code"===this.inputMode&&this.update({modelId:s,fieldIds:i}),ye.previousError.type&&ye.setPreviousError({type:null})}catch(t){"PrismaClientSchemaDriftedError"===t.type?us.update({type:"schemaDrift",description:"Your source Prisma Schema has changed. Please reload Studio to continue. Your changes will be saved.",dump:""}):us.update({type:"client",description:"Unable to run script",dump:`Message: ${t.message}\n  \nQuery:\n${JSON.stringify({modelName:e.modelName,operation:e.operation,args:e.args},null,2)}\n  `}),ye.updateError({visible:!0})}finally{this.update({running:!1})}},this.loadMore=async()=>{if(this.__recordIds.length>=(this.model.count||0)||"code"===this.inputMode)return[];this.update({running:!0});const e=rt({modelId:this.modelId,where:this.where,sort:this.sort,pagination:{take:100,skip:this.pagination.skip+this.__recordIds.length}});try{const{recordIds:t}=await Ge(e),s=nt([...this.__recordIds,...t]);return this.update({running:!1,recordIds:s,pagination:{take:s.length}}),t.map((e=>at.get(e))).filter((e=>!!e))}catch(t){throw this.update({running:!1}),console.log(t.type),"PrismaClientSchemaDriftedError"===t.type?us.update({type:"schemaDrift",description:"Your source Prisma Schema has changed. Please reload Studio to continue. Your changes will be saved.",dump:""}):us.update({type:"client",description:"Unable to fetch paginated records",dump:`Message: ${t.message}\n  \nStack: ${t.stack}\n                  \nQuery:\n${JSON.stringify(e,null,2)}\n  `}),ye.updateError({visible:!0}),F({path:"Script.loadMore",message:"Unable to fetch next page of records",context:{take:this.pagination.take,skip:this.pagination.skip,lastFetchedRecordId:c.exports.last(this.__recordIds)},nativeError:t})}},this.update=(e,t={})=>{c.exports.has(e,"name")&&(this.name=e.name),c.exports.has(e,"frozen")&&(this.frozen=e.frozen),c.exports.has(e,"modelId")&&(this.modelId=e.modelId),c.exports.has(e,"fieldIds")&&(this.fieldIds=e.fieldIds),c.exports.has(e,"inputMode")&&(this.inputMode=e.inputMode),c.exports.has(e,"viewMode")&&(this.viewMode=e.viewMode),c.exports.has(e,"sort")&&this.sort.update(e.sort),c.exports.has(e,"pagination")&&this.pagination.update(e.pagination),c.exports.has(e,"code")&&(this.code="string"==typeof e.code?JSON.parse(e.code):e.code),c.exports.has(e,"recordIds")&&(this.__recordIds=e.recordIds.filter((e=>{var t;return(null==(t=at.get(e))?void 0:t.modelId)===this.modelId}))),c.exports.has(e,"running")&&(this.running=e.running),super.update(e,t)},this.serialize=()=>({projectId:Kt.activeProjectId,id:this.id,frozen:this.frozen,name:this.name,inputMode:this.inputMode,code:JSON.stringify(this.code),modelId:this.modelId,where:this.where.serialize(),fieldIds:this.fieldIds.map((e=>String(e))),sortFieldId:this.sort.fieldId,sortOrder:this.sort.order,viewMode:this.viewMode}),this.id=e.id,this.frozen=e.frozen,this.name=e.name,this.modelId=e.modelId,this.fieldIds=e.fieldIds||[],this.inputMode="visual",this.where=new wt({modelId:e.modelId,scriptId:this.id}),(e.where||[]).forEach((e=>this.where.add(e))),this.sort=new mt({fieldId:null!=(s=null==(t=e.sort)?void 0:t.fieldId)?s:null,order:null!=(a=null==(i=e.sort)?void 0:i.order)?a:"asc"}),this.pagination=new ct({take:null!=(r=null==(n=e.pagination)?void 0:n.take)?r:100,skip:null!=(o=null==(l=e.pagination)?void 0:l.skip)?o:0}),this.viewMode="table",this.code=("string"==typeof e.code?JSON.parse(e.code):e.code)||rt({modelId:this.modelId,where:this.where,sort:this.sort,pagination:this.pagination}),this.__recordIds=[],this.running=!1}get hash(){return(({code:e,modelId:t,where:s,fieldIds:i})=>JSON.stringify({code:e,modelId:t,where:s.serialize(),fieldIds:i}))({code:this.code,modelId:this.modelId,where:this.where,fieldIds:this.fieldIds})}get model(){let e=as.get(this.modelId);if(!e)throw F({path:"Script.model",message:"Invalid modelId",context:{modelId:this.modelId}});return e}get fields(){return this.fieldIds.map((e=>{const t=pe.get(e);if(!t)throw F({path:"Script.fields",message:"Invalid fieldId",context:{fieldId:e}});return t}))}get recordIds(){const e=xs.actions.filter((e=>{var t,s;return"create"===e.type&&e.sessionId===(null==(t=Tt.activeTab)?void 0:t.sessionId)&&(null==(s=e.record)?void 0:s.modelId)===this.modelId})).map((e=>e.recordId)).filter((e=>!!e)),t=xs.actions.filter((e=>{var t;return"delete"===e.type&&e.sessionId===(null==(t=Tt.activeTab)?void 0:t.sessionId)})).map((e=>e.recordId));return[...e,...this.__recordIds.filter((e=>!t.includes(e)))]}get records(){return this.recordIds.map((e=>at.get(e))).filter((e=>!!e))}get uncommittedRecords(){return xs.actions.filter((e=>{var t;return"create"===e.type&&(null==(t=e.record)?void 0:t.modelId)===this.modelId})).map((e=>e.record)).filter((e=>!!e))}reset(){this.update({recordIds:[],inputMode:"visual",viewMode:"table",running:!1}),this.sort.reset(),this.pagination.reset()}}_t([h],xt.prototype,"id",2),_t([h],xt.prototype,"frozen",2),_t([h],xt.prototype,"name",2),_t([h],xt.prototype,"modelId",2),_t([h],xt.prototype,"fieldIds",2),_t([h],xt.prototype,"inputMode",2),_t([h],xt.prototype,"where",2),_t([h],xt.prototype,"sort",2),_t([h],xt.prototype,"pagination",2),_t([h],xt.prototype,"viewMode",2),_t([h],xt.prototype,"code",2),_t([h],xt.prototype,"running",2),_t([h],xt.prototype,"__recordIds",2),_t([v],xt.prototype,"hash",1),_t([v],xt.prototype,"model",1),_t([v],xt.prototype,"fields",1),_t([v],xt.prototype,"recordIds",1),_t([v],xt.prototype,"records",1),_t([v],xt.prototype,"uncommittedRecords",1),_t([p],xt.prototype,"run",2),_t([p],xt.prototype,"loadMore",2),_t([p],xt.prototype,"update",2),_t([p],xt.prototype,"reset",1);var St=new class extends W{constructor(){super(xt,{idbTableName:"scripts"})}},Nt=Object.defineProperty,Lt=Object.getOwnPropertyDescriptor,Rt=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Lt(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Nt(t,s,n),n};class Mt extends Y{constructor(e){super(e),this.save=()=>{this.session&&(this.session.forceSave(),this.session.isScript&&!this.session.script.name&&this.session.script.update({name:`Copy of ${this.session.script.model.name}`}))},this.discardChanges=()=>{this.session&&this.session.discardChanges()},this.load=({modelId:e,scriptId:t,sessionId:s})=>{if(!s&&!t&&!e)throw F({path:"Tab.load",message:"Invaid params",context:{modelId:e,scriptId:t,sessionId:s}});if(e){const s=as.get(e);if(!s)throw F({path:"Tab.load",message:"Invalid modelId",context:{modelId:e}});t=St.add({frozen:!0,name:null,modelId:e,fieldIds:s.fieldIds}).id}t&&(s=He.add({type:"script",scriptId:t,lastSavedHash:null}).id),s&&this.update({sessionId:s}),xs.actions.length>0?ye.updateActions({visible:!0}):ye.updateActions({visible:!1})},this.update=(e,t={})=>(c.exports.has(e,"sessionId")&&(this.sessionId=e.sessionId),c.exports.has(e,"preview")&&(this.preview=e.preview),super.update(e,t)),this.serialize=()=>({projectId:Kt.activeProjectId,id:this.id,sessionId:this.sessionId,preview:this.preview}),this.id=e.id,this.sessionId=e.sessionId,this.preview=e.preview||!1,this.isFiltersOpen=!1,this.preview&&(this.disposer=I((()=>{var e;return!!(null==(e=this.session)?void 0:e.isDirty)}),(()=>{this.update({preview:!1})})))}get title(){var e;return(null==(e=this.session)?void 0:e.isScript)?this.session.script.name||this.session.script.model&&this.session.script.model.name:"+"}get session(){return He.get(this.sessionId)}get isDirty(){if(!this.session)return!1;if(!this.session.isScript)return!1;const e=this.session.script,t=Object.values(xs.values).filter((e=>e.sessionId===this.sessionId));return e.fieldIds.length!==e.model.fieldIds.length||0!==e.pagination.skip||t.length>0}get hasFilters(){if(!this.session)return!1;if(!this.session.isScript)return!1;return this.session.script.where.size>0}clean(){this.disposer&&this.disposer()}toggleFilterPanel(){this.isFiltersOpen=!this.isFiltersOpen}}Rt([h],Mt.prototype,"id",2),Rt([h],Mt.prototype,"sessionId",2),Rt([h],Mt.prototype,"preview",2),Rt([h],Mt.prototype,"isFiltersOpen",2),Rt([v],Mt.prototype,"title",1),Rt([v],Mt.prototype,"session",1),Rt([v],Mt.prototype,"isDirty",1),Rt([v],Mt.prototype,"hasFilters",1),Rt([p],Mt.prototype,"save",2),Rt([p],Mt.prototype,"discardChanges",2),Rt([p],Mt.prototype,"load",2),Rt([p],Mt.prototype,"update",2),Rt([p],Mt.prototype,"clean",1),Rt([p],Mt.prototype,"toggleFilterPanel",1);var Ot=Object.defineProperty,kt=Object.getOwnPropertyDescriptor,At=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?kt(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Ot(t,s,n),n};class Dt extends W{constructor(){super(Mt,{idbTableName:"tabs"}),this.hydrate=({activeTabId:e})=>{let t=this.get(e);t&&t.session?(Object.values(this.values).forEach((e=>{if(e.session){if(e.session.isScript){if(!as.get(e.session.script.modelId))return void this.remove(e.id)}}else this.remove(e.id)})),t=this.get(e),t&&t.session?this.switch({id:e}):this.switch({id:"model-list-tab"})):this.switch({id:"model-list-tab"})},this.remove=e=>{var t,s;if(!this.get(e))return this.get("model-list-tab");const i=this.openTabs.findIndex((e=>e.id===this.activeTabId)),a=this.openTabs.findIndex((t=>t.id===e)),n=super.remove(e);if(Object.values(xs.values).filter((e=>e.sessionId===n.sessionId)).forEach((e=>xs.remove(e.id))),i===a){let e=a-1;-1===e&&(e=a),this.switch({id:null!=(s=null==(t=this.openTabs[e])?void 0:t.id)?s:"model-list-tab"})}return n},He.add({id:"model-list-session",type:"model-list",scriptId:null,lastSavedHash:null},{skipPersist:!0}),super.add({id:"model-list-tab",sessionId:"model-list-session",preview:!1},{skipPersist:!0}),this.activeTabId="model-list-tab"}get activeTab(){return this.get(this.activeTabId)}get openTabs(){return Object.values(this.values).reverse().filter((e=>!!e&&!!e.session)).sort((e=>"model-list-tab"===e.id?1:-1))}get previewTab(){return Object.values(this.values).find((e=>e.preview))||null}add(e,t={}){var s=e,{modelId:i,scriptId:a,sessionId:n}=s,r=d(s,["modelId","scriptId","sessionId"]);let c;if(!n&&!a&&!i)throw F({path:"TabStore.add",message:"Invaid params",context:{modelId:i,scriptId:a,sessionId:n}});if(i){const e=as.get(i);if(!e)throw F({path:"TabStore.add",message:"Invalid modelId",context:{modelId:i}});a=St.add({frozen:!0,name:null,modelId:i,fieldIds:e.fieldIds}).id}return a&&(n=He.add({type:"script",scriptId:a,lastSavedHash:null}).id),n&&(c=super.add(o(l({},r),{sessionId:n}),t)),c}switch({id:e,index:t,direction:s}){if(!e&&void 0===t&&void 0===s)throw F({path:"TabStore.switch",message:"Invalid params",context:{id:e,index:t,direction:s}});let i;if(e?i=this.get(e):void 0!==t&&(i=this.openTabs[t]),s){const e=this.openTabs.findIndex((e=>e.id===this.activeTabId));-1!==e&&("right"===s?(i=this.openTabs[e+1],e===this.openTabs.length-1&&(i=this.openTabs[0])):"left"===s&&(i=this.openTabs[e-1]))}i||(i=this.get("model-list-tab")),this.activeTabId=i.id,xs.actions.length>0?ye.updateActions({visible:!0}):ye.updateActions({visible:!1})}}At([h],Dt.prototype,"activeTabId",2),At([v],Dt.prototype,"activeTab",1),At([v],Dt.prototype,"openTabs",1),At([v],Dt.prototype,"previewTab",1),At([p],Dt.prototype,"hydrate",2),At([p],Dt.prototype,"add",1),At([p],Dt.prototype,"switch",1),At([p],Dt.prototype,"remove",2);var Tt=new Dt,Pt=Object.defineProperty,Vt=Object.getOwnPropertyDescriptor,jt=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Vt(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Pt(t,s,n),n};class Ft{constructor(){const e=window.matchMedia("(prefers-color-scheme: dark)"),t=new URLSearchParams(window.location.search).get("theme"),s=localStorage.getItem("theme");this.theme=t||(s||(e.matches?"dark":"light")),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",(e=>this.apply(e.matches?"dark":"light")))}apply(e){this.theme=e;const t=window.matchMedia("(prefers-color-scheme: dark)");t.matches&&"light"===this.theme||!t.matches&&"dark"===this.theme?localStorage.setItem("theme",this.theme):localStorage.removeItem("theme")}hydrate(e){this.apply(e)}}jt([h],Ft.prototype,"theme",2),jt([p],Ft.prototype,"apply",1),jt([p],Ft.prototype,"hydrate",1);var Bt=new Ft,Ht=Object.defineProperty,Zt=Object.getOwnPropertyDescriptor,qt=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Zt(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Ht(t,s,n),n};class Ut extends Y{constructor(e){super(e),this.update=(e,t)=>{c.exports.has(e,"name")&&(this.name=e.name),c.exports.has(e,"schemaPath")&&(this.schemaPath=e.schemaPath),c.exports.has(e,"schemaHash")&&(this.schemaHash=e.schemaHash),c.exports.has(e,"recentModelIds")&&(this.recentModelIds=e.recentModelIds),super.update(e,t)},this.id=e.id,this.name=e.name,this.schemaPath=e.schemaPath,this.schemaHash=e.schemaHash,this.recentModelIds=e.recentModelIds||[],C((()=>[Tt.activeTabId,Bt.theme]),(()=>{qs.ready&&U.save("projects",this.serialize())}),{fireImmediately:!0})}get recentModels(){return this.recentModelIds.map((e=>as.get(e))).filter((e=>!!e))}addRecentModel(e){this.recentModelIds.includes(e)&&(this.recentModelIds=this.recentModelIds.filter((t=>t!==e))),5===this.recentModelIds.length&&this.recentModelIds.pop(),this.recentModelIds.unshift(e)}serialize(){return{id:this.id,activeTabId:String(Tt.activeTabId),recentModelIds:this.recentModelIds.map((e=>String(e)))}}}qt([h],Ut.prototype,"id",2),qt([h],Ut.prototype,"name",2),qt([h],Ut.prototype,"schemaPath",2),qt([h],Ut.prototype,"schemaHash",2),qt([h],Ut.prototype,"recentModelIds",2),qt([v],Ut.prototype,"recentModels",1),qt([p],Ut.prototype,"addRecentModel",1),qt([p],Ut.prototype,"update",2);var zt=Object.defineProperty,$t=Object.getOwnPropertyDescriptor,Jt=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?$t(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&zt(t,s,n),n};class Wt extends W{constructor(){super(Ut,{idbTableName:"projects"}),this.activeProjectId=null}get activeProject(){const e=this.activeProjectId;return e?this.get(e):null}switch(e){this.activeProjectId=e}}Jt([h],Wt.prototype,"activeProjectId",2),Jt([v],Wt.prototype,"activeProject",1),Jt([p],Wt.prototype,"switch",1);var Kt=new Wt;const Gt=({modelId:e})=>{const t=as.get(e);if(!t)throw F({path:"getCountQuery",message:"Invalid modelId",context:{modelId:e}});return{modelName:t.name,operation:"count"}};var Qt=Object.defineProperty,Yt=Object.getOwnPropertyDescriptor,Xt=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Yt(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Qt(t,s,n),n};class es extends Y{constructor(e){super(e),this.getFieldByName=e=>pe.getByName(e,this.id),this.runCountQuery=async()=>{try{const{error:e,data:t}=await window.transport.request({channel:"prisma",action:"clientRequest",payload:{data:l({schemaHash:Kt.activeProject.schemaHash},Gt({modelId:this.id}))}});if(e||!t)throw e;if(Array.isArray(t))throw new Error(`Malformed response for \`count\` query: ${JSON.stringify(t,null,2)} `);const s=parseInt(t);this.update({count:s})}catch(e){console.log("Count request failed for model:",this.name,e),this.update({count:0})}},this.id=e.id,this.dbName=e.dbName,this.name=e.name,this.plural=e.plural,this.count=void 0,this.fieldIds=e.fieldIds||[],this.compoundId=e.compoundId,this.compoundUnique=e.compoundUnique}get fields(){return this.fieldIds.map((e=>{const t=pe.get(e);if(!t)throw F({path:"Model.field",message:"Invalid fieldId",context:{fieldId:e}});return t}))}get uniqueIdentifier(){var e,t;if(this.idField)return{name:this.idField.name,fields:[this.idField]};if(this.compoundId.fieldIds.length>0){const t=this.compoundId.fieldIds.map((e=>{const t=pe.get(e);if(!t)throw F({path:"Model.uniqueIdentifier",message:"Invalid fieldId in compoundId.fieldIds",context:{fieldId:e}});return t}));return{name:null!=(e=this.compoundId.name)?e:t.map((e=>e.name)).join("_"),fields:t}}if(this.compoundUnique.fieldIds.length>0){const e=this.compoundUnique.fieldIds.map((e=>{const t=pe.get(e);if(!t)throw F({path:"Model.uniqueIdentifier",message:"Invalid fieldId in compoundUnique.fieldIds",context:{fieldId:e}});return t}));return{name:null!=(t=this.compoundUnique.name)?t:e.map((e=>e.name)).join("_"),fields:e}}const s=this.uniqueFields&&this.uniqueFields[0];if(s)return{name:s.name,fields:[s]};throw F({path:"ModelStore.uniqueIdentifiers",message:"Unable to resolve unique identifiers for model",context:{modelId:this.id}})}get idField(){return this.fields.find((e=>e.isId))||null}get hasScalarListRelation(){return this.fields.some((e=>e.isScalarListRelation))||!1}get hasScalarListTwoWayMNRelation(){return this.fields.some((e=>e.isScalarListTwoWayMNRelation))||!1}get uniqueFields(){return this.fields.filter((e=>e.isUnique))}update(e,t={}){c.exports.has(e,"name")&&(this.name=e.name),c.exports.has(e,"plural")&&(this.plural=e.plural),c.exports.has(e,"count")&&(this.count=e.count),c.exports.has(e,"fieldIds")&&(this.fieldIds=e.fieldIds),c.exports.has(e,"compoundId")&&(this.compoundId=e.compoundId),c.exports.has(e,"compoundUnique")&&(this.compoundUnique=e.compoundUnique),super.update(e,t)}}Xt([h],es.prototype,"id",2),Xt([h],es.prototype,"dbName",2),Xt([h],es.prototype,"name",2),Xt([h],es.prototype,"plural",2),Xt([h],es.prototype,"count",2),Xt([h],es.prototype,"fieldIds",2),Xt([h],es.prototype,"compoundId",2),Xt([h],es.prototype,"compoundUnique",2),Xt([v],es.prototype,"fields",1),Xt([v],es.prototype,"uniqueIdentifier",1),Xt([v],es.prototype,"idField",1),Xt([v],es.prototype,"hasScalarListRelation",1),Xt([v],es.prototype,"hasScalarListTwoWayMNRelation",1),Xt([v],es.prototype,"uniqueFields",1),Xt([p],es.prototype,"runCountQuery",2),Xt([p],es.prototype,"update",1);var ts=Object.defineProperty,ss=Object.getOwnPropertyDescriptor;class is extends W{constructor(){super(es)}add(e,t={}){const s=e.name;return super.add(o(l({},e),{id:s}),t)}getByName(e){return this.get(e)}}((e,t,s,i)=>{for(var a,n=i>1?void 0:i?ss(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);i&&n&&ts(t,s,n)})([p],is.prototype,"add",1);var as=new is,ns=Object.defineProperty,rs=Object.getOwnPropertyDescriptor;class ls{send(e){window.transport.request({channel:"telemetry",action:"send",payload:{data:{command:e.command,commandDetails:JSON.stringify(e.commandDetails),commandContext:JSON.stringify({model_count:as.size})}}})}}((e,t,s,i)=>{for(var a,n=i>1?void 0:i?rs(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);i&&n&&ns(t,s,n)})([p],ls.prototype,"send",1);var os=new ls,ds=Object.defineProperty,cs=Object.getOwnPropertyDescriptor,hs=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?cs(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&ds(t,s,n),n};class ps{constructor(){this.type="fatal",this.description=null,this.dump=null}update(e){this.type=e.type,this.description=e.description,this.dump=e.dump,os.send({command:"error_throw",commandDetails:{type:this.type,description:this.description}})}}hs([h],ps.prototype,"type",2),hs([h],ps.prototype,"description",2),hs([h],ps.prototype,"dump",2),hs([p],ps.prototype,"update",1);var us=new ps;const ms=e=>{const t=[];return e.filter((e=>"delete"===e.type)).forEach((s=>{if(!s.record)throw F({path:"prismaQueriesFromActions._delete",message:"Unrecognized Record in Action",context:{action:s.serialize()}});const i=s.record.model,a={where:ys(s)};t.push({modelName:i.name,operation:"delete",args:a}),e=e.filter((e=>s.id!==e.id&&s.recordId!==e.recordId))})),{actions:e,requests:t}},gs=e=>{const t=[];return e.filter((e=>"create"===e.type)).forEach((s=>{if(!s.record)throw F({path:"prismaQueriesFromActions._create",message:"Unrecognized Record in Action",context:{action:s.serialize()}});const i=s.record.model,a={data:{},select:fs(s)},n=e.find((e=>"update"===e.type&&e.sessionId===s.sessionId&&e.recordId===s.recordId));n&&(a.data=Cs(n),a.select=l(l({},a.select),fs(n))),t.push({modelName:i.name,operation:"create",args:a}),e=e.filter((e=>e.id!==(null==n?void 0:n.id)&&s.id!==e.id))})),{actions:e,requests:t}},vs=e=>{const t=[];return e.filter((e=>"update"===e.type)).forEach((s=>{if(!s.record)throw F({path:"prismaQueriesFromActions._update",message:"Unrecognized Record in Action",context:{action:s.serialize()}});const i=s.record.model,a={where:ys(s),data:Is(s),select:fs(s)};t.push({modelName:i.name,operation:"update",args:a}),e=e.filter((e=>s.id!==e.id))})),{actions:e,requests:t}},fs=e=>{if(!e.record)throw F({path:"prismaQueriesFromActions._getRequestSelectArgument",message:"Unrecognized Record in Action",context:{action:e.serialize()}});const t=e.record.model.uniqueIdentifier.fields.reduce(((e,t)=>(e[t.name]=!0,e)),{});return"create"===e.type||"delete"===e.type?t:Object.keys(e.value).reduce(((e,t)=>(e[t]=!0,e)),t)},ys=e=>{if(!e.record)throw F({path:"prismaQueriesFromActions._getRequestWhereArgument",message:"Unrecognized Record in Action",context:{action:e.serialize()}});const t=e.record.model.uniqueIdentifier,s=t.name,i=t.fields.reduce(((t,s)=>(t[s.name]=e.record.valueInDB[s.name],t)),{});return 1===t.fields.length?i:{[s]:i}},Is=e=>{if(!e.record)throw F({path:"prismaQueriesFromActions._getUpdateRequestDataArgument",message:"Unrecognized Record in Action",context:{action:e.serialize()}});const t=e.record.model;return Object.keys(e.value).reduce(((s,i)=>{const a=t.getFieldByName(i),n=e.value[i];if(!a)throw F({path:"prismaQueriesFromActions._getUpdateRequestDataArgument",message:"Malformed field in action",context:{action:e.serialize()}});if(a.isReadOnly)return s;if(void 0===n)return s;if(a.isScalar&&a.isList||a.isEnum&&a.isList)s[i]={set:n};else if(a.isScalar||a.isEnum)s[i]=n;else if(a.isList&&a.isRelation){if(!a.typeAsModel)throw F({path:"prismaQueriesFromActions._getUpdateRequestDataArgument",message:"Invalid field type",context:{field:a.serialize()}});const t=(e.record.valueInDB[i]||[]).map((e=>Ie(a.typeAsModel.id,e))),r=n.map((e=>Ie(a.typeAsModel.id,e))),l=c.exports.difference(r,t),o=c.exports.difference(t,r);s[i]={},c.exports.isEmpty(l)||(s[i].connect=l.map(((e,t)=>{const s=at.get(e)||at.add({id:e,modelId:a.typeAsModel.id,value:n[t]}),i=s.model.uniqueIdentifier,r=i.name,l=i.fields.reduce(((e,t)=>(e[t.name]=s.valueInDB[t.name],e)),{});return 1===i.fields.length?l:{[r]:l}}))),c.exports.isEmpty(o)||(s[i].disconnect=o.map(((t,s)=>{const i=at.get(t);if(!i)throw F({path:"prismaQueriesFromActions._getUpdateRequestDataArgument",message:"Attempting to disconnect a non-existent record",context:{action:e.serialize(),index:s}});const a=i.model.uniqueIdentifier,n=a.name,r=a.fields.reduce(((e,t)=>(e[t.name]=i.valueInDB[t.name],e)),{});return 1===a.fields.length?r:{[n]:r}})))}else if(a.isRelation)if(null===n)s[i]={disconnect:!0};else{if(!a.typeAsModel)throw F({path:"prismaQueriesFromActions._getUpdateRequestDataArgument",message:"Invalid field type",context:{field:a.serialize()}});const e=Ie(a.typeAsModel.id,n),t=at.get(e)||at.add({id:e,modelId:a.typeAsModel.id,value:n}),r=t.model.uniqueIdentifier,l=r.name,o=r.fields.reduce(((e,s)=>(e[s.name]=t.valueInDB[s.name],e)),{});1===r.fields.length?s[i]={connect:o}:s[i]={connect:{[l]:o}}}return s}),{})},Cs=e=>{const t=Is(e);return Object.keys(t).forEach((s=>{if(!e.record)throw F({path:"prismaQueriesFromActions._getCreateRequestDataArgument",message:"Unrecognized Record in Action",context:{action:e.serialize()}});const i=e.record.model.getFieldByName(s);if(!i)throw F({path:"prismaQueriesFromActions._getUpdateRequestDataArgument",message:"Malformed field in action",context:{action:e.serialize()}});i.isRelation&&!i.isList&&t[s].disconnect&&delete t[s],i.isRelation&&i.isList&&t[s].disconnect&&delete t[s].disconnect})),t};var ws=Object.defineProperty,bs=Object.getOwnPropertyDescriptor,Es=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?bs(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&ws(t,s,n),n};class _s extends W{constructor(){super(Ke,{idbTableName:"actions"}),this.committing=!1,this.add=(e,t={})=>{var s;const i=super.add(e,t);return this.consolidate(),this.actions.length>0&&(null==(s=Tt.activeTab)||s.update({preview:!1}),ye.updateActions({visible:!0})),i},this.remove=e=>{const t=super.remove(e);return 0===this.actions.length&&ye.updateActions({visible:!1}),t},this.commit=async()=>{var e,t;if(this.invalidActions.length>0)return console.error("Commit failed: Some actions are invalid",this.invalidActions),us.update({type:"fatal",description:"Failed to commit changes because some actions are invalid",dump:`Invalid actions: \n${JSON.stringify(this.invalidActions,null,2)}`}),void ye.updateError({visible:!0});if(!(null==(t=null==(e=Tt.activeTab)?void 0:e.session)?void 0:t.isScript))return;const s=this.actions;let i=[];try{i=(e=>{const t=e.map((e=>xs.get(e))),s=[ms,gs,vs].reduce(((e,t)=>{const s=t(e.actions);return e.actions=s.actions,e.requests.push(...s.requests),e}),{actions:t,requests:[]});return s.actions.length>0&&console.warn("All actions were not processed. Pending actions: ",JSON.stringify(y(s.actions),null,2)),s.requests})(s.map((e=>e.id))),console.log("Committing actions: ",i)}catch(a){throw console.error("Commit failed: Unable to generate Prisma Client requests",a),us.update({type:"fatal",description:"An unexpected error occurred while saving your changes",dump:`Message: ${a.message}\n`}),ye.updateError({visible:!0}),a}w((()=>this.committing=!0));try{const{error:e}=await window.transport.request({channel:"prisma",action:"clientRequest",payload:{data:{schemaHash:Kt.activeProject.schemaHash,operation:"$transaction",queries:i}}});if(e)throw e;this.actions.filter((e=>"create"===e.type)).forEach((e=>{var t;return null==(t=e.record)?void 0:t.model.update({count:(e.record.model.count||0)+1})})),this.actions.filter((e=>"delete"===e.type)).forEach((e=>{var t;return null==(t=e.record)?void 0:t.model.update({count:(e.record.model.count||0)-1})})),this.discard(),Tt.activeTab.session.isScript&&await Tt.activeTab.session.script.run(),w((()=>this.committing=!1))}catch(a){w((()=>this.committing=!1));const e=Tt.activeTab.session.script.model;if("PrismaClientSchemaDriftedError"===a.type)us.update({type:"schemaDrift",description:"Your source Prisma Schema has changed. Please reload Studio to continue. Your changes will be saved.",dump:""});else if("PrismaClientRustPanicError"===a.type||"PrismaClientUnknownRequestError"===a.type)us.update({type:"fatal",description:`An unexpected error occurred while saving your changes to the \`${e.name}\` table`,dump:`Message: ${a.message}\n\nQuery: \n${JSON.stringify(i[0],null,2)}\n`});else{const e=`Type: ${a.type}\nMessage: ${a.message}\n\nCode: ${a.code}\n\nQuery:\n${i[0]}\n`;us.update({type:"client",description:`Failed to commit changes: ${a.message}`,dump:e})}throw ye.updateError({visible:!0}),a}},this.discard=()=>{const e=[...this.actions];for(let t=0;t<e.length;t++)this.remove(e[t].id);ye.updateActions({visible:!1})},this.consolidate=()=>{const e=Object.values(this.values).map((e=>e.id)),t=new Set,s=(i,a)=>{const n=this.get(i);if(!n)return;const r=e.findIndex(((e,t)=>{if(t<=a)return!1;const s=this.get(e);return!!s&&(s.sessionId===n.sessionId&&s.recordId===n.recordId&&s.type===n.type)}));if(-1===r)return;const o=this.get(e[r]);return o?(n.update({value:l(l({},n.value),o.value)}),t.add(e[r]),e.splice(r,1),s(i,a)):void 0};e.forEach(s);e.forEach((s=>{const i=this.get(s);i&&"delete"===i.type&&e.forEach((e=>{var a;const n=this.get(e);n&&(n&&"delete"!==n.type&&n.recordId===i.recordId&&t.add(e),(null==(a=n.record)?void 0:a.isCommitted)||t.add(s))}))})),t.forEach((e=>this.remove(e)))}}get actions(){return Object.values(this.values).filter((e=>{var t;return e.sessionId===(null==(t=Tt.activeTab)?void 0:t.sessionId)}))}get invalidActions(){return this.actions.filter((e=>!e.isValid))}}Es([h],_s.prototype,"committing",2),Es([v],_s.prototype,"actions",1),Es([v],_s.prototype,"invalidActions",1),Es([p],_s.prototype,"add",2),Es([p],_s.prototype,"remove",2),Es([p],_s.prototype,"commit",2),Es([p],_s.prototype,"discard",2),Es([p],_s.prototype,"consolidate",2);var xs=new _s;var Ss=new class{constructor(){this.inputObjectTypes=new Map}hydrate(e){(e.schema.inputObjectTypes.prisma||[]).forEach((e=>this.inputObjectTypes.set(e.name,e)));const{models:t,enums:s,types:i}=e.datamodel;t.forEach((t=>{var s,a,n,r,l,o,d;const c=as.add({dbName:t.dbName,name:t.name,plural:(null==(s=e.mappings.modelOperations.find((e=>e.model===t.name)))?void 0:s.plural)||t.name,fieldIds:[],compoundId:{name:null,fieldIds:[]},compoundUnique:{name:null,fieldIds:[]}});let h=[];t.fields.forEach((e=>{if("unsupported"===e.kind)return;"object"===e.kind&&i.some((t=>t.name===e.type))&&(e.type="Json",e.kind="scalar");let t=pe.add({id:X(c.id,e.name),modelId:c.id,name:e.name,type:e.type,kind:e.kind,default:e.default,isId:e.isId,isUnique:e.isUnique,isRequired:e.isRequired,isList:e.isList,isReadOnly:e.isReadOnly,isUpdatedAt:e.isUpdatedAt,relationName:e.relationName||"",relationFromFieldNames:e.relationFromFields||[],relationToFieldNames:e.relationToFields||[]});h.push(t.id)}));const p={name:null!=(n=null==(a=t.primaryKey)?void 0:a.name)?n:null,fieldIds:((null==(r=t.primaryKey)?void 0:r.fields)||[]).map((e=>X(c.id,e)))},u={name:null!=(o=null==(l=t.uniqueIndexes[0])?void 0:l.name)?o:null,fieldIds:(null==(d=t.uniqueIndexes[0])?void 0:d.fields.map((e=>X(t.name,e))))||[]};c.update({fieldIds:h,compoundId:p,compoundUnique:u})})),s.forEach((e=>{le.add({name:e.name,values:e.values.map((e=>e.name))})}))}removeUnusedModels(e){const{models:t}=e.datamodel;c.exports.difference(Object.values(as.values).map((e=>e.id)),t.map((e=>e.name))).forEach((e=>{Object.values(xs.values).forEach((t=>{var s;(null==(s=t.session)?void 0:s.isScript)&&t.session.script.modelId===e&&xs.remove(t.id)})),Object.values(Tt.values).forEach((t=>{var s;(null==(s=t.session)?void 0:s.isScript)&&t.session.script.modelId===e&&Tt.remove(t.id)})),Object.values(He.values).forEach((t=>{(null==t?void 0:t.isScript)&&t.script.modelId===e&&He.remove(t.id)})),as.remove(e)}))}removeUnusedFields(e){const{models:t}=e.datamodel;c.exports.difference(Object.values(pe.values).map((e=>e.id)),c.exports.flatten(t.map((e=>e.fields.map((t=>X(e.name,t.name))))))).forEach((e=>pe.remove(e)))}removeUnusedEnums(e){const{enums:t}=e.datamodel;c.exports.difference(Object.values(le.values).map((e=>e.id)),t.map((e=>e.name))).forEach((e=>le.remove(e)))}};function Ns(e,t,s,i){Object.defineProperty(e,t,{get:s,set:i,enumerable:!0,configurable:!0})}var Ls={};Ns(Ls,"serializeRPCMessage",(()=>Rs)),Ns(Ls,"deserializeRPCMessage",(()=>Ms));function Rs(e){return JSON.stringify(e,((e,t)=>"bigint"==typeof t?"PrismaBigInt::"+t:"Buffer"===(null==t?void 0:t.type)&&Array.isArray(null==t?void 0:t.data)?"PrismaBytes::"+b.Buffer.from(t.data).toString("base64"):t))}function Ms(e){return JSON.parse(e,((e,t)=>"string"==typeof t&&t.startsWith("PrismaBigInt::")?BigInt(t.substr("PrismaBigInt::".length)):"string"==typeof t&&t.startsWith("PrismaBytes::")?t.substr("PrismaBytes::".length):t))}class Os{constructor(e){this.requestIdCounter=0,this.baseUrl=e}request(e){const t=new URL(this.baseUrl);return t.search=window.location.search,new Promise(((s,i)=>{const a=this.requestIdCounter;fetch(t.toString(),{method:"POST",headers:{"Content-Type":"text/plain"},body:Rs({requestId:a,channel:e.channel,action:e.action,payload:e.payload})}).then((async e=>{if(200===e.status){const t=Ms(await e.text());return s(t.payload)}return console.error("Non-200 Status Code in HTTPTransport.send. Body:",e.body),i({message:`Error in HTTP Request (Status: ${e.status})`,stack:JSON.stringify(e.body,null,2)})})).catch((e=>(console.error("Unable to communicate with backend: ",e),i({message:"Unable to communicate with Prisma Client. Is Studio still running? You may need to restart it using `npx prisma studio`",stack:e})))),this.requestIdCounter++}))}}const ks=async()=>{const e=Object.values(as.values),{error:t,data:s}=await window.transport.request({channel:"prisma",action:"clientRequest",payload:{data:{schemaHash:Kt.activeProject.schemaHash,operation:"$transaction",queries:e.map((e=>Gt({modelId:e.id})))}}});if(t)throw F({path:"getAllCounts",message:"Unable to process `count` query",context:{error:t}});if(!Array.isArray(s))throw F({path:"getAllCounts",message:"Malformed response for `count` query:\n\n",context:{error:t}});s.forEach(((t,s)=>{e[s].update({count:t})}))},As=e=>(e=>{if("object"==typeof(t=e)&&null!==t&&"message"in t&&"string"==typeof t.message)return e;var t;try{return new Error(JSON.stringify(e))}catch{return new Error(String(e))}})(e).message;var Ds=Object.defineProperty,Ts=Object.getOwnPropertyDescriptor,Ps=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Ts(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Ds(t,s,n),n};class Vs{constructor(){this.hasCheckedForUpdates=!1,this.latestVersion="0.503.0",this.changelog="",this.isDownloading=!1,this.isUpdateReady=!1,this.isInstalling=!1,this.check=async()=>{if(!V.updates)return;w((()=>{this.hasCheckedForUpdates=!1,this.isDownloading=!1,this.isUpdateReady=!1,this.isInstalling=!1}));const{error:e,data:t}=await window.transport.request({channel:"update",action:"check",payload:{data:null}});if(e)return void console.error("Update check failed",e);const{available:s,version:i,changelog:a}=t;w(s?()=>{this.hasCheckedForUpdates=!0,this.latestVersion=i,this.changelog=a}:()=>{this.hasCheckedForUpdates=!0,this.latestVersion="0.503.0"})},this.download=async()=>V.updates?this.isDownloading?Promise.reject("Another download is in progress"):(w((()=>{this.isDownloading=!0,this.isUpdateReady=!1,this.isInstalling=!1})),await window.transport.request({channel:"update",action:"download",payload:{data:null}}),void w((()=>{this.isDownloading=!1,this.isUpdateReady=!0}))):Promise.resolve(),this.cancelDownload=async()=>{if(V.updates){if(!this.isDownloading)return Promise.resolve();await window.transport.request({channel:"update",action:"downloadCancel",payload:{data:null}}),w((()=>{this.isDownloading=!1,this.isUpdateReady=!1}))}},this.install=async()=>{V.updates&&(w((()=>{this.isDownloading=!1,this.isUpdateReady=!0,this.isInstalling=!0})),await window.transport.request({channel:"update",action:"install",payload:{data:null}}),w((()=>{this.isUpdateReady=!1,this.isInstalling=!1})))}}get isUpToDate(){return"0.503.0"===this.latestVersion}}Ps([h],Vs.prototype,"hasCheckedForUpdates",2),Ps([h],Vs.prototype,"latestVersion",2),Ps([h],Vs.prototype,"changelog",2),Ps([h],Vs.prototype,"isDownloading",2),Ps([h],Vs.prototype,"isUpdateReady",2),Ps([h],Vs.prototype,"isInstalling",2),Ps([v],Vs.prototype,"isUpToDate",1),Ps([p],Vs.prototype,"check",2),Ps([p],Vs.prototype,"download",2),Ps([p],Vs.prototype,"cancelDownload",2);var js=new Vs;var Fs=Object.defineProperty,Bs=Object.getOwnPropertyDescriptor,Hs=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?Bs(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&Fs(t,s,n),n};class Zs{constructor(){this.ready=!1,E({enforceActions:"always"}),console.log("Studio version","0.503.0")}async initTransport(){if("http"!==V.transport.type)return console.error("Unrecognized transport, aborting"),Promise.reject();window.transport=new Os(V.transport.url),window.toJS=y,window.stores={ActionStore:xs,BootstrapStore:qs,ConfigStore:V,DMMFStore:Ss,EnumStore:le,ErrorStore:us,FieldStore:pe,LayoutStore:ye,ModelStore:as,PersistenceStore:U,ProjectStore:Kt,RecordStore:at,ScriptStore:St,SessionStore:He,TabStore:Tt,TelemetryStore:os,ThemeStore:Bt,UpdateStore:js}}async init(){console.time("Bootstrap Duration"),this.update({ready:!1}),this.disposeProjectReaction&&this.disposeProjectReaction();try{const{error:e,data:t}=await window.transport.request({channel:"prisma",action:"getDMMF",payload:{data:null}});if(e||!t)throw new Error(`Error starting Prisma Client: ${JSON.stringify(e,null,2)}`);const{dmmf:s,schemaHash:i}=t,{error:a,data:n}=await window.transport.request({channel:"project",action:"get",payload:{data:null}});if(a||!n)throw new Error(`Error fetching project: ${a}`);const{name:r,schemaPath:l}=n,o=(e=>((e.endsWith("/")||e.endsWith("\\"))&&(e=e.slice(0,-1)),(e.startsWith("/")||e.startsWith("\\"))&&(e=e.slice(1)),e.replace(RegExp(/\/|\\/,"gi"),"-")))(l);Kt.add({id:o,name:r,schemaPath:l,schemaHash:i,recentModelIds:[]},{skipPersist:!0}),Kt.switch(o),await U.init({projectId:o}),Ss.hydrate(s);for(let c of[Kt,pe,as,le,St,at,He,xs,Tt])await c.restore();Ss.removeUnusedModels(s),Ss.removeUnusedFields(s),Ss.removeUnusedEnums(s),window.requestIdleCallback?window.requestIdleCallback(ks):await ks();Object.values(St.values).filter((e=>e.frozen&&as.get(e.modelId))).forEach((e=>e.update({fieldIds:e.model.fieldIds})));const d=(await U.load("projects")).find((e=>e.id===o));d&&Tt.hydrate({activeTabId:d.activeTabId}),await Kt.activeProject.forceUpdate(),this.update({ready:!0}),console.timeEnd("Bootstrap Duration")}catch(e){console.error(e);const t=(e=>{const t=/Error code: (P\d{4})/g.exec(e);return(null==t?void 0:t[1])?t[1]:""})(As(e));throw this.update({ready:!0}),0!==t.length?us.update({type:"client",description:`There was an error parsing your schema file with Prisma. Review documentation on error ${t} to learn more.`,dump:`${e.message}\n${e.stack}`}):us.update({type:"fatal",description:"Unable to load project",dump:`${e.message}\n${e.stack}`}),ye.updateError({visible:!0}),F({path:"BootstrapStore.init",message:"Studio bootstrap failed",context:{message:e.message,stack:e.stack},nativeError:e})}window.requestIdleCallback?window.requestIdleCallback(this.cleanupIndexedDB):this.cleanupIndexedDB()}update(e={}){c.exports.has(e,"ready")&&(this.ready=e.ready)}cleanupIndexedDB(){const e=Object.values(Tt.openTabs).map((e=>e.sessionId));Object.values(He.values).filter((t=>!e.includes(t.id))).forEach((e=>He.remove(e.id)));const t=Object.values(He.values).map((e=>e.scriptId));Object.values(St.values).filter((e=>!t.includes(e.id)&&null===e.name)).forEach((e=>St.remove(e.id)))}}Hs([h],Zs.prototype,"ready",2),Hs([p],Zs.prototype,"initTransport",1),Hs([p],Zs.prototype,"init",1),Hs([p],Zs.prototype,"update",1);var qs=new Zs;var Us="_container_18uec_1",zs="_wide_18uec_32",$s="_ghost_18uec_35",Js="_blue_18uec_49",Ws="_green_18uec_62",Ks="_red_18uec_75",Gs="_disabled_18uec_85";var Qs=_((e=>{var t=e,{dataTestId:s,innerRef:i,className:a,children:n,ghost:r=!1,wide:o=!1,blue:c=!1,green:h=!1,red:p=!1,disabled:u,onClick:m}=t,g=d(t,["dataTestId","innerRef","className","children","ghost","wide","blue","green","red","disabled","onClick"]);return x.createElement("button",l({"data-testid":null!=s?s:"button",ref:i,className:S(Us,{[zs]:o,[$s]:r,[Js]:c,[Ws]:h,[Ks]:p,[Gs]:u},a),disabled:u,onClick:e=>{u||m&&(e.stopPropagation(),e.preventDefault(),m(e))}},g),n)}));function Ys(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"M7.875 16.9722L12 21.25M12 21.25L16.125 16.9722M12 21.25V11.625",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),N.exports.createElement("path",{d:"M20.8773 17.125C21.7462 16.5127 22.3978 15.6389 22.7375 14.6303C23.0773 13.6217 23.0874 12.5309 22.7666 11.5162C22.4458 10.5014 21.8107 9.6155 20.9534 8.987C20.0961 8.3585 19.0612 8.02011 17.999 8.02095H16.7398C16.4392 6.84701 15.8767 5.7567 15.0948 4.8321C14.3129 3.90751 13.3319 3.17272 12.2256 2.68306C11.1193 2.1934 9.91659 1.96162 8.70798 2.00518C7.49937 2.04873 6.31637 2.36649 5.24803 2.93452C4.1797 3.50255 3.25387 4.30606 2.54025 5.28455C1.82663 6.26304 1.34382 7.39102 1.12815 8.58356C0.912487 9.77611 0.969594 11.0021 1.29517 12.1694C1.62075 13.3366 2.20632 14.4146 3.00779 15.3222",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}))}function Xs(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 88 43",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"M9.632 40.256C7.92533 40.256 6.432 39.9893 5.152 39.456C3.872 38.9013 2.88 38.1227 2.176 37.12C1.49333 36.096 1.152 34.912 1.152 33.568V32.864C1.152 32.7573 1.184 32.672 1.248 32.608C1.33333 32.5227 1.42933 32.48 1.536 32.48H5.184C5.29067 32.48 5.376 32.5227 5.44 32.608C5.52533 32.672 5.568 32.7573 5.568 32.864V33.344C5.568 34.1973 5.96267 34.9227 6.752 35.52C7.54133 36.096 8.608 36.384 9.952 36.384C11.0827 36.384 11.9253 36.1493 12.48 35.68C13.0347 35.1893 13.312 34.592 13.312 33.888C13.312 33.376 13.1413 32.9493 12.8 32.608C12.4587 32.2453 11.9893 31.936 11.392 31.68C10.816 31.4027 9.888 31.0293 8.608 30.56C7.17867 30.0693 5.96267 29.568 4.96 29.056C3.97867 28.544 3.14667 27.8507 2.464 26.976C1.80267 26.08 1.472 24.9813 1.472 23.68C1.472 22.4 1.80267 21.28 2.464 20.32C3.12533 19.36 4.04267 18.624 5.216 18.112C6.38933 17.6 7.744 17.344 9.28 17.344C10.9013 17.344 12.3413 17.632 13.6 18.208C14.88 18.784 15.872 19.5947 16.576 20.64C17.3013 21.664 17.664 22.8587 17.664 24.224V24.704C17.664 24.8107 17.6213 24.9067 17.536 24.992C17.472 25.056 17.3867 25.088 17.28 25.088H13.6C13.4933 25.088 13.3973 25.056 13.312 24.992C13.248 24.9067 13.216 24.8107 13.216 24.704V24.448C13.216 23.552 12.8427 22.7947 12.096 22.176C11.3707 21.536 10.368 21.216 9.088 21.216C8.08533 21.216 7.296 21.4293 6.72 21.856C6.16533 22.2827 5.888 22.8693 5.888 23.616C5.888 24.1493 6.048 24.5973 6.368 24.96C6.70933 25.3227 7.2 25.6533 7.84 25.952C8.50133 26.2293 9.51467 26.6133 10.88 27.104C12.3947 27.6587 13.5787 28.1493 14.432 28.576C15.3067 29.0027 16.0853 29.6427 16.768 30.496C17.472 31.328 17.824 32.416 17.824 33.76C17.824 35.7653 17.088 37.3547 15.616 38.528C14.144 39.68 12.1493 40.256 9.632 40.256ZM29.1753 26.784C29.1753 26.8907 29.1326 26.9867 29.0473 27.072C28.9833 27.136 28.8979 27.168 28.7913 27.168H25.7193C25.6126 27.168 25.5593 27.2213 25.5593 27.328V34.112C25.5593 34.816 25.6979 35.3387 25.9753 35.68C26.2739 36.0213 26.7433 36.192 27.3833 36.192H28.4393C28.5459 36.192 28.6313 36.2347 28.6953 36.32C28.7806 36.384 28.8233 36.4693 28.8233 36.576V39.616C28.8233 39.8507 28.6953 39.9893 28.4393 40.032C27.5433 40.0747 26.9033 40.096 26.5193 40.096C24.7486 40.096 23.4259 39.808 22.5513 39.232C21.6766 38.6347 21.2286 37.5253 21.2073 35.904V27.328C21.2073 27.2213 21.1539 27.168 21.0473 27.168H19.2233C19.1166 27.168 19.0206 27.136 18.9353 27.072C18.8713 26.9867 18.8393 26.8907 18.8393 26.784V23.936C18.8393 23.8293 18.8713 23.744 18.9353 23.68C19.0206 23.5947 19.1166 23.552 19.2233 23.552H21.0473C21.1539 23.552 21.2073 23.4987 21.2073 23.392V19.584C21.2073 19.4773 21.2393 19.392 21.3033 19.328C21.3886 19.2427 21.4846 19.2 21.5913 19.2H25.1753C25.2819 19.2 25.3673 19.2427 25.4313 19.328C25.5166 19.392 25.5593 19.4773 25.5593 19.584V23.392C25.5593 23.4987 25.6126 23.552 25.7193 23.552H28.7913C28.8979 23.552 28.9833 23.5947 29.0473 23.68C29.1326 23.744 29.1753 23.8293 29.1753 23.936V26.784ZM40.5315 23.936C40.5315 23.8293 40.5635 23.744 40.6275 23.68C40.7128 23.5947 40.8088 23.552 40.9155 23.552H44.6595C44.7662 23.552 44.8515 23.5947 44.9155 23.68C45.0008 23.744 45.0435 23.8293 45.0435 23.936V39.616C45.0435 39.7227 45.0008 39.8187 44.9155 39.904C44.8515 39.968 44.7662 40 44.6595 40H40.9155C40.8088 40 40.7128 39.968 40.6275 39.904C40.5635 39.8187 40.5315 39.7227 40.5315 39.616V38.528C40.5315 38.464 40.5102 38.432 40.4675 38.432C40.4248 38.4107 40.3822 38.432 40.3395 38.496C39.4862 39.648 38.1635 40.224 36.3715 40.224C34.7502 40.224 33.4168 39.7333 32.3715 38.752C31.3262 37.7707 30.8035 36.3947 30.8035 34.624V23.936C30.8035 23.8293 30.8355 23.744 30.8995 23.68C30.9848 23.5947 31.0808 23.552 31.1875 23.552H34.8995C35.0062 23.552 35.0915 23.5947 35.1555 23.68C35.2408 23.744 35.2835 23.8293 35.2835 23.936V33.504C35.2835 34.3573 35.5075 35.0507 35.9555 35.584C36.4248 36.1173 37.0648 36.384 37.8755 36.384C38.6008 36.384 39.1982 36.1707 39.6675 35.744C40.1368 35.296 40.4248 34.72 40.5315 34.016V23.936ZM57.617 17.984C57.617 17.8773 57.649 17.792 57.713 17.728C57.7983 17.6427 57.8943 17.6 58.001 17.6H61.745C61.8517 17.6 61.937 17.6427 62.001 17.728C62.0863 17.792 62.129 17.8773 62.129 17.984V39.616C62.129 39.7227 62.0863 39.8187 62.001 39.904C61.937 39.968 61.8517 40 61.745 40H58.001C57.8943 40 57.7983 39.968 57.713 39.904C57.649 39.8187 57.617 39.7227 57.617 39.616V38.56C57.617 38.496 57.5957 38.464 57.553 38.464C57.5103 38.4427 57.4677 38.4533 57.425 38.496C56.529 39.6693 55.3023 40.256 53.745 40.256C52.2517 40.256 50.961 39.84 49.873 39.008C48.8063 38.176 48.0383 37.0347 47.569 35.584C47.2063 34.4747 47.025 33.184 47.025 31.712C47.025 30.1973 47.217 28.8747 47.601 27.744C48.0917 26.3787 48.8597 25.3013 49.905 24.512C50.9717 23.7013 52.2837 23.296 53.841 23.296C55.377 23.296 56.5717 23.8293 57.425 24.896C57.4677 24.96 57.5103 24.9813 57.553 24.96C57.5957 24.9387 57.617 24.896 57.617 24.832V17.984ZM56.945 35.008C57.3717 34.2187 57.585 33.1413 57.585 31.776C57.585 30.3467 57.3503 29.2267 56.881 28.416C56.3903 27.584 55.6757 27.168 54.737 27.168C53.7343 27.168 52.977 27.584 52.465 28.416C51.9317 29.248 51.665 30.3787 51.665 31.808C51.665 33.088 51.889 34.1547 52.337 35.008C52.8703 35.9253 53.6597 36.384 54.705 36.384C55.665 36.384 56.4117 35.9253 56.945 35.008ZM67.006 21.696C66.2807 21.696 65.6727 21.4613 65.182 20.992C64.7127 20.5013 64.478 19.8933 64.478 19.168C64.478 18.4213 64.7127 17.8133 65.182 17.344C65.6513 16.8747 66.2593 16.64 67.006 16.64C67.7527 16.64 68.3607 16.8747 68.83 17.344C69.2993 17.8133 69.534 18.4213 69.534 19.168C69.534 19.8933 69.2887 20.5013 68.798 20.992C68.3287 21.4613 67.7313 21.696 67.006 21.696ZM65.086 40C64.9793 40 64.8833 39.968 64.798 39.904C64.734 39.8187 64.702 39.7227 64.702 39.616V23.904C64.702 23.7973 64.734 23.712 64.798 23.648C64.8833 23.5627 64.9793 23.52 65.086 23.52H68.83C68.9367 23.52 69.022 23.5627 69.086 23.648C69.1713 23.712 69.214 23.7973 69.214 23.904V39.616C69.214 39.7227 69.1713 39.8187 69.086 39.904C69.022 39.968 68.9367 40 68.83 40H65.086ZM79.0342 40.256C77.2422 40.256 75.7062 39.7867 74.4262 38.848C73.1462 37.9093 72.2716 36.6293 71.8022 35.008C71.5036 34.0053 71.3542 32.9173 71.3542 31.744C71.3542 30.4853 71.5036 29.3547 71.8022 28.352C72.2929 26.7733 73.1782 25.536 74.4582 24.64C75.7382 23.744 77.2742 23.296 79.0662 23.296C80.8156 23.296 82.3089 23.744 83.5462 24.64C84.7836 25.5147 85.6582 26.7413 86.1702 28.32C86.5116 29.3867 86.6822 30.5067 86.6822 31.68C86.6822 32.832 86.5329 33.9093 86.2342 34.912C85.7649 36.576 84.8902 37.888 83.6102 38.848C82.3516 39.7867 80.8262 40.256 79.0342 40.256ZM79.0342 36.384C79.7382 36.384 80.3356 36.1707 80.8262 35.744C81.3169 35.3173 81.6689 34.7307 81.8822 33.984C82.0529 33.3013 82.1382 32.5547 82.1382 31.744C82.1382 30.848 82.0529 30.0907 81.8822 29.472C81.6476 28.7467 81.2849 28.1813 80.7942 27.776C80.3036 27.3707 79.7062 27.168 79.0022 27.168C78.2769 27.168 77.6689 27.3707 77.1782 27.776C76.7089 28.1813 76.3676 28.7467 76.1542 29.472C75.9836 29.984 75.8982 30.7413 75.8982 31.744C75.8982 32.704 75.9729 33.4507 76.1222 33.984C76.3356 34.7307 76.6876 35.3173 77.1782 35.744C77.6902 36.1707 78.3089 36.384 79.0342 36.384Z",fill:"#2D3748"}),N.exports.createElement("path",{d:"M5.9 3.186C6.516 3.186 7.05733 3.312 7.524 3.564C7.99067 3.816 8.35 4.17533 8.602 4.642C8.86333 5.09933 8.994 5.62667 8.994 6.224C8.994 6.812 8.85867 7.33 8.588 7.778C8.32667 8.226 7.95333 8.576 7.468 8.828C6.992 9.07067 6.44133 9.192 5.816 9.192H3.828C3.78133 9.192 3.758 9.21533 3.758 9.262V12.832C3.758 12.8787 3.73933 12.9207 3.702 12.958C3.674 12.986 3.63667 13 3.59 13H1.952C1.90533 13 1.86333 12.986 1.826 12.958C1.798 12.9207 1.784 12.8787 1.784 12.832V3.354C1.784 3.30733 1.798 3.27 1.826 3.242C1.86333 3.20467 1.90533 3.186 1.952 3.186H5.9ZM5.606 7.61C6.03533 7.61 6.38067 7.48867 6.642 7.246C6.90333 6.994 7.034 6.66733 7.034 6.266C7.034 5.85533 6.90333 5.524 6.642 5.272C6.38067 5.02 6.03533 4.894 5.606 4.894H3.828C3.78133 4.894 3.758 4.91733 3.758 4.964V7.54C3.758 7.58667 3.78133 7.61 3.828 7.61H5.606ZM16.0035 13C15.9102 13 15.8448 12.958 15.8075 12.874L14.0575 8.996C14.0388 8.95867 14.0108 8.94 13.9735 8.94H12.6715C12.6248 8.94 12.6015 8.96333 12.6015 9.01V12.832C12.6015 12.8787 12.5828 12.9207 12.5455 12.958C12.5175 12.986 12.4802 13 12.4335 13H10.7955C10.7488 13 10.7068 12.986 10.6695 12.958C10.6415 12.9207 10.6275 12.8787 10.6275 12.832V3.368C10.6275 3.32133 10.6415 3.284 10.6695 3.256C10.7068 3.21867 10.7488 3.2 10.7955 3.2H14.7995C15.3968 3.2 15.9195 3.32133 16.3675 3.564C16.8248 3.80667 17.1748 4.152 17.4175 4.6C17.6695 5.048 17.7955 5.566 17.7955 6.154C17.7955 6.78867 17.6368 7.33467 17.3195 7.792C17.0022 8.24 16.5588 8.55733 15.9895 8.744C15.9428 8.76267 15.9288 8.79533 15.9475 8.842L17.8515 12.804C17.8702 12.8413 17.8795 12.8693 17.8795 12.888C17.8795 12.9627 17.8282 13 17.7255 13H16.0035ZM12.6715 4.894C12.6248 4.894 12.6015 4.91733 12.6015 4.964V7.358C12.6015 7.40467 12.6248 7.428 12.6715 7.428H14.5055C14.8975 7.428 15.2148 7.31133 15.4575 7.078C15.7095 6.84467 15.8355 6.54133 15.8355 6.168C15.8355 5.79467 15.7095 5.49133 15.4575 5.258C15.2148 5.01533 14.8975 4.894 14.5055 4.894H12.6715ZM19.8151 13C19.7685 13 19.7265 12.986 19.6891 12.958C19.6611 12.9207 19.6471 12.8787 19.6471 12.832V3.368C19.6471 3.32133 19.6611 3.284 19.6891 3.256C19.7265 3.21867 19.7685 3.2 19.8151 3.2H21.4531C21.4998 3.2 21.5371 3.21867 21.5651 3.256C21.6025 3.284 21.6211 3.32133 21.6211 3.368V12.832C21.6211 12.8787 21.6025 12.9207 21.5651 12.958C21.5371 12.986 21.4998 13 21.4531 13H19.8151ZM27.1049 13.112C26.3582 13.112 25.7049 12.9953 25.1449 12.762C24.5849 12.5193 24.1509 12.1787 23.8429 11.74C23.5442 11.292 23.3949 10.774 23.3949 10.186V9.878C23.3949 9.83133 23.4089 9.794 23.4369 9.766C23.4742 9.72867 23.5162 9.71 23.5629 9.71H25.1589C25.2055 9.71 25.2429 9.72867 25.2709 9.766C25.3082 9.794 25.3269 9.83133 25.3269 9.878V10.088C25.3269 10.4613 25.4995 10.7787 25.8449 11.04C26.1902 11.292 26.6569 11.418 27.2449 11.418C27.7395 11.418 28.1082 11.3153 28.3509 11.11C28.5935 10.8953 28.7149 10.634 28.7149 10.326C28.7149 10.102 28.6402 9.91533 28.4909 9.766C28.3415 9.60733 28.1362 9.472 27.8749 9.36C27.6229 9.23867 27.2169 9.07533 26.6569 8.87C26.0315 8.65533 25.4995 8.436 25.0609 8.212C24.6315 7.988 24.2675 7.68467 23.9689 7.302C23.6795 6.91 23.5349 6.42933 23.5349 5.86C23.5349 5.3 23.6795 4.81 23.9689 4.39C24.2582 3.97 24.6595 3.648 25.1729 3.424C25.6862 3.2 26.2789 3.088 26.9509 3.088C27.6602 3.088 28.2902 3.214 28.8409 3.466C29.4009 3.718 29.8349 4.07267 30.1429 4.53C30.4602 4.978 30.6189 5.50067 30.6189 6.098V6.308C30.6189 6.35467 30.6002 6.39667 30.5629 6.434C30.5349 6.462 30.4975 6.476 30.4509 6.476H28.8409C28.7942 6.476 28.7522 6.462 28.7149 6.434C28.6869 6.39667 28.6729 6.35467 28.6729 6.308V6.196C28.6729 5.804 28.5095 5.47267 28.1829 5.202C27.8655 4.922 27.4269 4.782 26.8669 4.782C26.4282 4.782 26.0829 4.87533 25.8309 5.062C25.5882 5.24867 25.4669 5.50533 25.4669 5.832C25.4669 6.06533 25.5369 6.26133 25.6769 6.42C25.8262 6.57867 26.0409 6.72333 26.3209 6.854C26.6102 6.97533 27.0535 7.14333 27.6509 7.358C28.3135 7.60067 28.8315 7.81533 29.2049 8.002C29.5875 8.18867 29.9282 8.46867 30.2269 8.842C30.5349 9.206 30.6889 9.682 30.6889 10.27C30.6889 11.1473 30.3669 11.8427 29.7229 12.356C29.0789 12.86 28.2062 13.112 27.1049 13.112ZM38.791 3.312C38.8377 3.23733 38.903 3.2 38.987 3.2H40.625C40.6717 3.2 40.709 3.21867 40.737 3.256C40.7744 3.284 40.793 3.32133 40.793 3.368V12.832C40.793 12.8787 40.7744 12.9207 40.737 12.958C40.709 12.986 40.6717 13 40.625 13H38.987C38.9404 13 38.8984 12.986 38.861 12.958C38.833 12.9207 38.819 12.8787 38.819 12.832V6.658C38.819 6.62067 38.8097 6.602 38.791 6.602C38.7724 6.602 38.7537 6.616 38.735 6.644L37.251 8.968C37.2044 9.04267 37.139 9.08 37.055 9.08H36.229C36.145 9.08 36.0797 9.04267 36.033 8.968L34.549 6.644C34.5304 6.616 34.5117 6.60667 34.493 6.616C34.4744 6.616 34.465 6.63467 34.465 6.672V12.832C34.465 12.8787 34.4464 12.9207 34.409 12.958C34.381 12.986 34.3437 13 34.297 13H32.659C32.6124 13 32.5704 12.986 32.533 12.958C32.505 12.9207 32.491 12.8787 32.491 12.832V3.368C32.491 3.32133 32.505 3.284 32.533 3.256C32.5704 3.21867 32.6124 3.2 32.659 3.2H34.297C34.381 3.2 34.4464 3.23733 34.493 3.312L36.593 6.574C36.621 6.63 36.649 6.63 36.677 6.574L38.791 3.312ZM49.1488 13C49.0555 13 48.9948 12.9533 48.9668 12.86L48.5468 11.488C48.5282 11.4507 48.5048 11.432 48.4768 11.432H45.0328C45.0048 11.432 44.9815 11.4507 44.9628 11.488L44.5568 12.86C44.5288 12.9533 44.4682 13 44.3748 13H42.5968C42.5408 13 42.4988 12.986 42.4708 12.958C42.4428 12.9207 42.4382 12.8693 42.4568 12.804L45.4808 3.34C45.5088 3.24667 45.5695 3.2 45.6628 3.2H47.8608C47.9542 3.2 48.0148 3.24667 48.0428 3.34L51.0668 12.804C51.0762 12.8227 51.0808 12.846 51.0808 12.874C51.0808 12.958 51.0295 13 50.9268 13H49.1488ZM45.4668 9.822C45.4575 9.878 45.4762 9.906 45.5228 9.906H47.9868C48.0428 9.906 48.0615 9.878 48.0428 9.822L46.7828 5.664C46.7735 5.62667 46.7595 5.61267 46.7408 5.622C46.7222 5.622 46.7082 5.636 46.6988 5.664L45.4668 9.822Z",fill:"#718096"}))}var ei="_container_vmd0x_1";var ti=_((e=>{var t=e,{className:s,children:i,onClick:a}=t,n=d(t,["className","children","onClick"]);return x.createElement(Qs,l({className:S(ei,s),onClick:a},n),i)}));class si extends x.PureComponent{componentDidMount(){var e;const{target:t,keys:s,preventDefault:i,stopPropagation:a,onMatch:n}=this.props,r=null!=(e=null==t?void 0:t.current)?e:document;this.instance=L(r),this.instance.bind(s,((e,t)=>{i&&e.preventDefault(),a&&e.stopImmediatePropagation(),n(e,t)}),"keydown")}componentWillUnmount(){this.instance.unbind(this.props.keys,"keydown")}render(){return x.createElement(x.Fragment,null)}}si.defaultProps={preventDefault:!0,stopPropagation:!0};var ii={container:"_container_1lyn8_1",input:"_input_1lyn8_6",file:"_file_1lyn8_16"};class ai extends x.PureComponent{constructor(){super(...arguments),this.input=x.createRef(),this.handleChange=e=>{const{disabled:t,onChange:s}=this.props;t||s(e.currentTarget.value)},this.handleBlur=()=>{const{disabled:e,onBlur:t}=this.props;e||t()}}componentDidMount(){var e;const t=null!=(e=this.props.innerRef)?e:this.input;t.current&&this.props.focusOnMount&&t.current.focus()}render(){const e=this.props,{className:t,innerRef:s,style:i,type:a,tabIndex:n,value:r,placeholder:o,disabled:c,focusOnMount:h,onChange:p,onBlur:u,onConfirm:m,onCancel:g}=e,v=d(e,["className","innerRef","style","type","tabIndex","value","placeholder","disabled","focusOnMount","onChange","onBlur","onConfirm","onCancel"]),f=null!=s?s:this.input;return x.createElement("div",l({className:S(ii.container,t),style:i},v),x.createElement("input",{ref:f,lang:"en",tabIndex:n,className:S(ii.input,{[ii.disabled]:c}),type:a,placeholder:o,disabled:c,value:null===r?"":r,onChange:this.handleChange,onBlur:this.handleBlur}),m&&x.createElement(si,{keys:"enter",target:f,onMatch:m}),g&&x.createElement(si,{keys:"esc",target:f,onMatch:g}))}}function ni(e){return N.exports.createElement("svg",Object.assign({width:24,height:24,viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"M8.70711 7.29289C8.31658 6.90237 7.68342 6.90237 7.29289 7.29289C6.90237 7.68342 6.90237 8.31658 7.29289 8.70711L8.70711 7.29289ZM15.7782 17.1924C16.1687 17.5829 16.8019 17.5829 17.1924 17.1924C17.5829 16.8019 17.5829 16.1687 17.1924 15.7782L15.7782 17.1924ZM7.29289 15.7782C6.90237 16.1687 6.90237 16.8019 7.29289 17.1924C7.68342 17.5829 8.31658 17.5829 8.70711 17.1924L7.29289 15.7782ZM17.1924 8.70711C17.5829 8.31658 17.5829 7.68342 17.1924 7.29289C16.8019 6.90237 16.1687 6.90237 15.7782 7.29289L17.1924 8.70711ZM7.29289 8.70711L15.7782 17.1924L17.1924 15.7782L8.70711 7.29289L7.29289 8.70711ZM8.70711 17.1924L17.1924 8.70711L15.7782 7.29289L7.29289 15.7782L8.70711 17.1924Z",fill:"currentColor"}))}function ri(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"m23.968743,20.378119c0,0.634822 -0.252181,1.243672 -0.701129,1.69262c-0.448948,0.448948 -1.057797,0.701129 -1.69262,0.701129l-19.149988,0c-0.634858,0 -1.24372,-0.252181 -1.692632,-0.701129c-0.448924,-0.448948 -0.701117,-1.057797 -0.701117,-1.69262l0,-16.75624c0,-0.634858 0.252193,-1.24372 0.701117,-1.692632c0.448912,-0.448924 1.057774,-0.701117 1.692632,-0.701117l5.984371,0l2.393749,3.590623l10.771868,0c0.634822,0 1.243672,0.252193 1.69262,0.701117c0.448948,0.448912 0.701129,1.057774 0.701129,1.692632l0,13.165617z"}))}function li(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fill:"none",d:"m10.7143,14.96883c2.6036,0 4.7143,-2.1106 4.7143,-4.7143c0,-2.60358 -2.1107,-4.71424 -4.7143,-4.71424c-2.60364,0 -4.7143,2.11066 -4.7143,4.71424c0,2.6037 2.11066,4.7143 4.7143,4.7143z"}),N.exports.createElement("path",{fill:"none",d:"m18,17.54023l-3.4286,-3.4285",strokeLinecap:"round"}))}ai.defaultProps={value:"",disabled:!1,focusOnMount:!1,onChange:()=>{},onBlur:()=>{}};var oi="_header_zfcta_1",di="_search_zfcta_15",ci="_list_zfcta_25",hi="_project_zfcta_32",pi="_projectRemoveButton_zfcta_44",ui="_projectMeta_zfcta_54",mi="_projectName_zfcta_59",gi="_projectDir_zfcta_63";class vi extends x.PureComponent{constructor(e){super(e),this.state={projects:[],searchText:""},this.handleSearch=e=>{this.setState({searchText:e})}}async componentDidMount(){const{error:e,data:t}=await window.transport.request({channel:"project",action:"getAll",payload:{data:null}});if(e)return F({path:"ProjectList.componentDidMount",message:"Failed to fetch all projects",context:{error:e}});this.setState({projects:t||[]})}handleOpen(e){window.transport.request({channel:"project",action:"open",payload:{data:{schemaPath:e}}})}handleRemove(e){this.setState((t=>({projects:t.projects.filter((t=>t.schemaPath!==e))}))),window.transport.request({channel:"project",action:"delete",payload:{data:{schemaPath:e}}})}guessProjectName(e){return e.name?e.name:e.schemaPath.endsWith("/prisma/schema.prisma")?c.exports.capitalize(e.schemaPath.split("/").slice(-3,-2)[0]):e.schemaPath.endsWith("\\prisma\\schema.prisma")?c.exports.capitalize(e.schemaPath.split("\\").slice(-3,-2)[0]):e.schemaPath.endsWith("/schema.prisma")?c.exports.capitalize(e.schemaPath.split("/").slice(-2,-1)[0]):e.schemaPath.endsWith("\\schema.prisma")?c.exports.capitalize(e.schemaPath.split("\\").slice(-2,-1)[0]):"Untitled Project"}render(){const{projects:e,searchText:t}=this.state,s=e.filter((e=>{var s,i;return(null==(s=e.name)?void 0:s.toLowerCase().includes(null==t?void 0:t.toLowerCase()))||(null==(i=e.schemaPath)?void 0:i.toLowerCase().includes(null==t?void 0:t.toLowerCase()))}));return x.createElement(x.Fragment,null,x.createElement("div",{className:oi},x.createElement(li,null),x.createElement(ai,{type:"text",className:di,value:t,placeholder:"Search",onChange:this.handleSearch})),x.createElement("div",{className:ci},s.map((e=>x.createElement("div",{key:e.schemaPath,className:hi,onClick:()=>this.handleOpen(e.schemaPath)},x.createElement(ri,null),x.createElement("div",{className:ui},x.createElement("span",{className:mi},this.guessProjectName(e)),x.createElement("span",{className:gi},e.schemaPath)),x.createElement(ti,{className:pi,onClick:()=>this.handleRemove(e.schemaPath)},x.createElement(ni,null)))))))}}var fi={container:"_container_1qwck_1",left:"_left_1qwck_8",right:"_right_1qwck_9",logotype:"_logotype_1qwck_19",logo:"_logo_1qwck_19",fadeIn:"_fadeIn_1qwck_1",type:"_type_1qwck_33",updateContainer:"_updateContainer_1qwck_42",downloadIcon:"_downloadIcon_1qwck_51",alertIcon:"_alertIcon_1qwck_57",updateText:"_updateText_1qwck_63",readMoreLink:"_readMoreLink_1qwck_69",links:"_links_1qwck_75",link:"_link_1qwck_75",slideInRight:"_slideInRight_1qwck_1",footer:"_footer_1qwck_101"};class yi extends x.Component{constructor(){super(...arguments),this.handleOpen=async()=>{const{error:e,data:t}=await window.transport.request({channel:"window",action:"pickPrismaSchema",payload:{data:null}});e||await window.transport.request({channel:"project",action:"open",payload:{data:{schemaPath:t.path}}})},this.handleInstallUpdate=async()=>{await js.download(),await js.install()},this.handleCancelDownload=async()=>{await js.cancelDownload()}}async componentDidMount(){await js.check()}render(){return x.createElement("div",{className:S(fi.container,{"theme--light":"light"===Bt.theme,"theme--dark":"dark"===Bt.theme})},x.createElement("div",{className:fi.container},x.createElement("div",{className:fi.left},x.createElement("div",{className:fi.logotype},x.createElement("img",{src:"./icon-1024.png",className:fi.logo}),x.createElement(Xs,{className:fi.type})),js.hasCheckedForUpdates&&!js.isUpToDate&&x.createElement("div",{className:fi.updateContainer},x.createElement(Ys,{className:fi.downloadIcon}),!js.isDownloading&&!js.isInstalling&&x.createElement(x.Fragment,null,x.createElement("span",{className:fi.updateText},"New version available"),x.createElement(Qs,{green:!0,onClick:this.handleInstallUpdate},"Update")),js.isDownloading&&x.createElement(x.Fragment,null,x.createElement("span",{className:fi.updateText},"Downloading .."),x.createElement(Qs,{red:!0,onClick:this.handleCancelDownload},"Cancel")),js.isInstalling&&x.createElement(x.Fragment,null,x.createElement("span",{className:fi.updateText},"Installing .."),x.createElement(Qs,{red:!0,onClick:this.handleCancelDownload},"Cancel"))),x.createElement("div",{className:fi.links},x.createElement("a",{className:fi.link},"0.503.0"),"|",x.createElement("a",{className:fi.link,href:"https://github.com/prisma/studio/releases",target:"_blank",rel:"noreferrer noopener"},"Changelog"))),x.createElement("div",{className:fi.right},x.createElement(vi,null),x.createElement("div",{className:fi.footer},x.createElement(Qs,{className:fi.button,onClick:this.handleOpen},"Select Schema")))))}}var Ii=_(yi);function Ci(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("rect",{fillRule:"evenodd",clipRule:"evenodd",y:10,width:24,height:4,rx:2}))}function wi(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M23.3327 2.54004C24.1705 3.30678 24.227 4.60642 23.4591 5.44287L9.78844 20.3338C9.3987 20.7583 8.8484 21 8.27163 21C7.69485 21 7.14456 20.7583 6.75481 20.3338L0.540861 13.5652C-0.227044 12.7287 -0.170453 11.4291 0.667261 10.6624C1.50497 9.89561 2.80659 9.95212 3.57449 10.7886L8.27163 15.9049L20.4255 2.66625C21.1934 1.82981 22.495 1.7733 23.3327 2.54004Z"}))}var bi="_container_me63q_1",Ei="_checkbox_me63q_8",_i="_checked_me63q_22";var xi=_((({className:e,checked:t=!1,indeterminate:s=!1,onChange:i})=>x.createElement("div",{"data-testid":"checkbox","data-test-selected":t,className:S(bi,e),onClick:()=>i&&i(!t)},x.createElement("div",{className:S(Ei,{[_i]:t})},t&&(s?x.createElement(Ci,null):x.createElement(wi,null))))));var Si="_mask_u1smr_1";var Ni=_((({className:e,onClick:t})=>x.createElement("div",{"data-testid":"mask",className:S(Si,e),onClick:t})));var Li="_container_1t3y0_1",Ri="_transparent_1t3y0_8";class Mi extends x.PureComponent{constructor(){super(...arguments),this.state={top:0,bottom:0,left:0,right:0,minWidth:void 0,maxWidth:void 0,minHeight:void 0,maxHeight:void 0}}componentDidMount(){this.setPosition(this.props)}componentWillReceiveProps(e){this.setPosition(e)}setPosition({target:e,targetOffsetX:t,targetOffsetY:s}){e&&this.setState(((e,t={})=>{const s=e.current.getBoundingClientRect(),i={top:0,bottom:0,left:0,right:0};return t.x||(t.x=0),t.y||(t.y=0),window.innerWidth-s.right<100?(i.right=window.innerWidth-s.right-t.x,i.left=void 0,i.maxWidth=window.innerWidth-i.right-20):(i.left=s.left+t.x,i.right=void 0,i.maxWidth=window.innerWidth-i.left-20),window.innerHeight-s.bottom<100?(i.bottom=s.top-t.y,i.top=20,i.maxHeight=window.innerHeight-i.bottom-20):(i.top=s.bottom+t.y,i.bottom=void 0,i.maxHeight=window.innerHeight-i.top-20),i})(e,{x:t,y:s}))}render(){const e=this.props,{className:t,maskClassName:s,domElementId:i,target:a,targetOffsetX:n,targetOffsetY:r,darken:o,children:c,onClickOutside:h}=e,p=d(e,["className","maskClassName","domElementId","target","targetOffsetX","targetOffsetY","darken","children","onClickOutside"]);return x.createElement(x.Fragment,null,R.createPortal(x.createElement(x.Fragment,null,x.createElement(Ni,{className:S({[Ri]:!o},s),onClick:h}),x.createElement("div",l({"data-testid":"modal",className:S(Li,t),style:a&&this.state},p),c)),document.getElementById(i)))}}Mi.defaultProps={domElementId:"modal-root",targetOffsetX:0,targetOffsetY:5,darken:!1};var Oi=_(Mi);var ki="_container_58c43_1";var Ai=_((({className:e,target:t,targetOffsetX:s,targetOffsetY:i,darken:a,children:n,onClickOutside:r})=>x.createElement(Oi,{className:S(ki,e),target:t,targetOffsetX:s,targetOffsetY:i,darken:a,onClickOutside:r},n)));var Di="_container_bc1do_1",Ti="_pill_bc1do_8",Pi="_label_bc1do_26",Vi="_open_bc1do_33",ji="_exists_bc1do_43";var Fi="_tooltip_1bhvr_1",Bi="_search_1bhvr_7",Hi="_checkbox_1bhvr_30",Zi="_name_1bhvr_39",qi="_type_1bhvr_50";class Ui extends x.PureComponent{constructor(){super(...arguments),this.button=x.createRef(),this.state={isOpen:!1,searchValue:""},this.handleToggle=()=>{this.setState((e=>({isOpen:!e.isOpen})))},this.handleSelect=async e=>{var t,s;null==(t=Tt.activeTab)||t.update({preview:!1});const i=null==(s=Tt.activeTab)?void 0:s.session.script;let a;a=i.fieldIds.includes(e)?i.fieldIds.filter((t=>t!==e)):i.fieldIds.concat(e),i.update({frozen:!1,fieldIds:a})},this.handleSearch=e=>{this.setState({searchValue:e})},this.handleSelectAll=async()=>{var e,t;null==(e=Tt.activeTab)||e.update({preview:!1});const s=null==(t=Tt.activeTab)?void 0:t.session.script,i=s.fieldIds.length>0?[]:s.model.fieldIds;s.update({frozen:!1,fieldIds:i})}}render(){const{isOpen:e,searchValue:t}=this.state,{model:s,fieldIds:i}=Tt.activeTab.session.script,a=s.fields.filter((e=>e.name.toLowerCase().includes(t.toLowerCase()))),n=s.fieldIds.filter((e=>i.includes(e)));return x.createElement("div",{className:Di},x.createElement("div",{"data-testid":"field-filter",ref:this.button,className:S(Ti,{[Vi]:e,[ji]:n.length<s.fields.length}),onClick:this.handleToggle},x.createElement("span",{className:Pi},"Fields"),x.createElement("span",null,n.length===s.fields.length?"All":n.length)),e&&x.createElement(Ai,{className:Fi,target:this.button,onClickOutside:this.handleToggle},x.createElement(x.Fragment,null,x.createElement(xi,{className:Hi,checked:n.length>0,indeterminate:n.length<s.fields.length,onChange:()=>this.handleSelectAll()}),x.createElement(ai,{type:"text",className:Bi,placeholder:"Search",value:t,onChange:this.handleSearch}),a.map((e=>{const t=n.includes(e.id);return x.createElement(x.Fragment,{key:e.id},x.createElement(xi,{className:Hi,checked:t,onChange:()=>this.handleSelect(e.id)}),x.createElement("div",{"data-testid":"filter-option",className:Zi,onClick:()=>this.handleSelect(e.id)},e.name),x.createElement("div",{className:qi,onClick:()=>this.handleSelect(e.id)},e.typeAsLabel))})))))}}var zi=_(Ui);var $i={tooltip:"_tooltip_13qj3_1",tooltipBody:"_tooltipBody_13qj3_4",text:"_text_13qj3_8",separator:"_separator_13qj3_15",input:"_input_13qj3_19"};class Ji extends x.PureComponent{constructor(){super(...arguments),this.button=x.createRef(),this.tooltipBody=x.createRef(),this.runScriptDebounced=c.exports.debounce((()=>{var e;(null==(e=Tt.activeTab)?void 0:e.session.script).run()}),300,{leading:!1,trailing:!0}),this.state={isOpen:!1},this.handleToggle=()=>{this.setState((e=>({isOpen:!e.isOpen})))},this.handleChangeTake=e=>{var t,s;null==(t=Tt.activeTab)||t.update({preview:!1});(null==(s=Tt.activeTab)?void 0:s.session.script).update({frozen:!1,pagination:{take:Number(e)}}),this.runScriptDebounced(),os.send({command:"pagination_change",commandDetails:{take_change:!0,skip_change:!1}})},this.handleChangeSkip=e=>{var t,s;null==(t=Tt.activeTab)||t.update({preview:!1});const i=Number(e);if(isNaN(i)||i<0)return;(null==(s=Tt.activeTab)?void 0:s.session.script).update({frozen:!1,pagination:{skip:i}}),this.runScriptDebounced(),os.send({command:"pagination_change",commandDetails:{take_change:!1,skip_change:!0}})}}render(){var e;const{isOpen:t}=this.state,{recordIds:s,model:i,pagination:a}=Tt.activeTab.session.script;return x.createElement("div",{className:Di},x.createElement("div",{"data-testid":"page-filter",ref:this.button,className:S(Ti,{[Vi]:t}),onClick:this.handleToggle},x.createElement("span",{className:Pi}," Showing "),x.createElement("span",null,x.createElement("span",{className:$i.noPadding,"data-testid":"pagination__take"},s.length)," of ",x.createElement("span",{className:$i.noPadding,"data-testid":"pagination__total"},null!=(e=i.count)?e:"?"))),t&&x.createElement(Ai,{className:$i.tooltip,target:this.button,onClickOutside:this.handleToggle},x.createElement("div",{className:$i.tooltipBody,ref:this.tooltipBody},x.createElement("span",{className:$i.text},"Take"),x.createElement(ai,{"data-testid":"pagination__take-input",type:"number",tabIndex:0,focusOnMount:!0,className:$i.input,value:String(a.take),onChange:this.handleChangeTake}),x.createElement("div",{className:$i.separator}),x.createElement("span",{className:$i.text},"Skip"),x.createElement(ai,{"data-testid":"pagination__skip-input",type:"number",tabIndex:0,focusOnMount:!0,className:$i.input,value:String(a.skip),onChange:this.handleChangeSkip}))),t&&x.createElement(si,{keys:"esc",target:this.tooltipBody,onMatch:()=>this.handleToggle()}))}}var Wi=_(Ji);class Ki extends x.PureComponent{constructor(){super(...arguments),this.button=x.createRef(),this.handleAddWhere=()=>{var e,t;null==(e=Tt.activeTab)||e.update({preview:!1});const s=null==(t=Tt.activeTab)?void 0:t.session.script;s.update({frozen:!1});const i=s.where.add({fieldIds:[s.model.uniqueIdentifier.fields[0].id],value:null});s.pagination.reset(),os.send({command:"filter_change",commandDetails:{total_filters_count:s.where.size,field_types:i.fields.map((e=>e.type)),operation:i.operation}})}}render(){const{where:e}=Tt.activeTab.session.script;return x.createElement("div",{className:Di},x.createElement("div",{"data-testid":"where-filter",ref:this.button,className:S(Ti,{[ji]:e.size>0}),onClick:()=>Tt.activeTab.toggleFilterPanel()}," ",x.createElement("span",{className:S(Pi)},"Filters"),x.createElement("span",null,e.size||"None"," ")))}}var Gi=_(Ki);var Qi={container:"_container_1n6lm_1",separator:"_separator_1n6lm_6",action:"_action_1n6lm_13",discardBtn:"_discardBtn_1n6lm_16"};class Yi extends x.PureComponent{constructor(){super(...arguments),this.handleDiscard=()=>{const{onSuccess:e}=this.props;xs.discard(),null==e||e(),os.send({command:"action_discard",commandDetails:{pending_action_count:xs.actions.length}})},this.handleCommit=async()=>{const{onSuccess:e,onFailure:t}=this.props;try{await xs.commit(),null==e||e()}catch(s){null==t||t()}os.send({command:"action_commit",commandDetails:{pending_action_count:xs.actions.length}})}}render(){const e=xs.actions.filter((e=>"create"!==e.type||!xs.actions.find((t=>t.recordId===e.recordId)))).length,t=xs.invalidActions.length;return 0===e?null:x.createElement(x.Fragment,null,x.createElement("div",{className:Qi.separator}),x.createElement("div",{"data-testid":"pending-actions",className:S(Qi.container,this.props.className)},x.createElement(Qs,{"data-testid":"commit-actions",green:!0,className:S(Qi.action,Qi.confirmBtn),disabled:t>0||xs.committing,onClick:this.handleCommit},xs.committing?"Saving Changes":`Save ${e} change${e>1?"s":""}`),x.createElement(Qs,{"data-testid":"discard-actions",ghost:!0,disabled:xs.committing,className:S(Qi.action,Qi.discardBtn),onClick:this.handleDiscard},"Discard changes"),x.createElement(si,{keys:"mod+s",onMatch:()=>xs.commit()})))}}var Xi=_(Yi);function ea(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14 1.71429C14 0.767512 13.1046 0 12 0C10.8954 0 10 0.767512 10 1.71429V10L1.71429 10C0.767512 10 0 10.8954 0 12C0 13.1046 0.767512 14 1.71429 14L10 14V22.2857C10 23.2325 10.8954 24 12 24C13.1046 24 14 23.2325 14 22.2857V14L22.2857 14C23.2325 14 24 13.1046 24 12C24 10.8954 23.2325 10 22.2857 10L14 10V1.71429Z"}))}var ta="_filters_kgv6c_1",sa="_cell_kgv6c_29",ia="_fields_kgv6c_35",aa="_field_kgv6c_35",na="_dropdown_kgv6c_43",ra="_operation_kgv6c_49",la="_value_kgv6c_50",oa="_deleteButton_kgv6c_106",da="_addButton_kgv6c_129",ca="_removeButton_kgv6c_177",ha="_infoBox_kgv6c_188",pa="_container_kgv6c_192",ua="_containerHidden_kgv6c_200",ma="_containerWithoutFilters_kgv6c_204",ga="_filterControlsRow_kgv6c_209",va="_filterControlsRowWithoutFilters_kgv6c_214",fa="_invalid_kgv6c_218",ya="_relationType_kgv6c_222";function Ia(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("g",null,N.exports.createElement("circle",{cx:6,cy:12,r:2}),N.exports.createElement("circle",{cx:12,cy:12,r:2}),N.exports.createElement("circle",{cx:18,cy:12,r:2})))}var Ca="_container_1u411_1",wa="_button_1u411_7",ba="_open_1u411_18",Ea="_textButton_1u411_27",_a="_caret_1u411_46",xa="_select_1u411_56";var Sa="_container_32t9m_1",Na="_mask_32t9m_25",La="_item_32t9m_29",Ra="_highlighted_32t9m_37";class Ma extends x.PureComponent{constructor(e){super(e),this.menuRef=x.createRef(),this.itemRefs=[],this.handleHighlightNext=()=>{const{items:e}=this.props;this.setState((t=>t.highlightIndex===e.length-1?{highlightIndex:0}:{highlightIndex:t.highlightIndex+1}),(()=>{var e,t;null==(e=this.itemRefs[this.state.highlightIndex].current)||e.scrollIntoView(!1),null==(t=this.itemRefs[this.state.highlightIndex].current)||t.scrollIntoView({block:"nearest"})}))},this.handleHighlightPrevious=()=>{const{items:e}=this.props;this.setState((t=>0===t.highlightIndex||-1===t.highlightIndex?{highlightIndex:e.length-1}:{highlightIndex:t.highlightIndex-1}),(()=>{var e,t;null==(e=this.itemRefs[this.state.highlightIndex].current)||e.scrollIntoView(!1),null==(t=this.itemRefs[this.state.highlightIndex].current)||t.scrollIntoView({block:"nearest"})}))},this.handleSelect=()=>{const{highlightIndex:e}=this.state,{items:t,onSelect:s}=this.props;e<0||e>=t.length||s(t[e])},this.handleCancel=e=>{e.preventDefault()},this.state={highlightIndex:-1},this.itemRefs=e.items.map((()=>x.createRef()))}componentDidMount(){var e,t;(null==(e=this.props.target.current)?void 0:e.getBoundingClientRect())&&(null==(t=this.menuRef.current)||t.focus())}render(){const{highlightIndex:e}=this.state,{target:t,items:s,onSelect:i,onBlur:a}=this.props;return x.createElement(Oi,{className:Sa,maskClassName:Na,domElementId:"dropdown-root",target:t,targetOffsetY:0,darken:!1,onClickOutside:a},x.createElement(x.Fragment,null,x.createElement("div",{ref:this.menuRef,className:Sa,tabIndex:1},s.map(((t,s)=>x.createElement("div",{ref:this.itemRefs[s],key:t.id,"data-testid":"dropdown-menu__item",className:S(La,{[Ra]:e===s}),onClick:()=>i(t)},t.label)))),x.createElement(si,{keys:"up",target:this.menuRef,onMatch:this.handleHighlightPrevious}),x.createElement(si,{keys:"down",target:this.menuRef,onMatch:this.handleHighlightNext}),x.createElement(si,{keys:"esc",target:this.menuRef,onMatch:()=>null==a?void 0:a()}),x.createElement(si,{keys:"enter",target:this.menuRef,onMatch:this.handleSelect})))}}var Oa=_(Ma);class ka extends x.PureComponent{constructor(){super(...arguments),this.button=x.createRef(),this.state={isOpen:!1},this.handleToggle=()=>{this.state.isOpen?this.handleClose():this.handleOpen()},this.handleSelect=e=>{const{onSelect:t}=this.props;t&&t(e),setTimeout((()=>this.handleToggle()),0)},this.handleOpen=()=>{setTimeout((()=>this.setState({isOpen:!0})),0)},this.handleClose=()=>{setTimeout((()=>this.setState({isOpen:!1})),0)}}render(){const e=this.props,{className:t,type:s,nativeSelect:i,items:a,selectedItem:n,onSelect:r,innerRef:o,buttonClassName:c}=e,h=d(e,["className","type","nativeSelect","items","selectedItem","onSelect","innerRef","buttonClassName"]),{isOpen:p}=this.state;return x.createElement("div",{className:S(Ca,t)},x.createElement("div",l({ref:this.button,className:S(wa,{[ba]:p},c),onClick:this.handleToggle},h),"button"===s&&x.createElement(Ia,null),"text"===s&&x.createElement("div",{"data-testid":"dropdown__item--selected",className:Ea,title:null==n?void 0:n.label},x.createElement("span",null,(null==n?void 0:n.label)||""),x.createElement("div",{className:_a}))),i&&x.createElement("select",{ref:o,className:xa,value:null==n?void 0:n.id,onChange:e=>this.handleSelect(a.find((t=>t.id===e.currentTarget.value)))},a.map((e=>x.createElement("option",{key:e.id,value:e.id},e.label)))),p&&!i&&x.createElement(Oa,{target:this.button,items:a,onSelect:this.handleSelect,onBlur:this.handleClose}))}}var Aa=_(ka);class Da extends x.PureComponent{constructor(){super(...arguments),this.runScriptDebounced=c.exports.debounce((()=>{var e;(null==(e=Tt.activeTab)?void 0:e.session.script).run()}),500,{leading:!1,trailing:!0}),this.handleChangeField=({id:e},t)=>{var s,i,a,n;const{whereId:r}=this.props;null==(s=Tt.activeTab)||s.update({preview:!1});const l=null==(i=Tt.activeTab)?void 0:i.session.script,o=l.where.get(r);if(o){if(l.update({frozen:!1}),0===t){const t=pe.get(e);if(null==t?void 0:t.isRelation){const t=null==(n=null==(a=pe.get(e))?void 0:a.typeAsModel)?void 0:n.uniqueIdentifier.fields[0].id;o.update({fieldIds:[e,t]})}else o.update({fieldIds:[e]})}else{const s=[...o.fieldIds];s[t]=e,o.update({fieldIds:s})}o.supportedOperations.includes(o.operation)||this.handleChangeOperation({id:o.supportedOperations[0]}),["in","notIn"].includes(o.operation)&&this.handleChangeValue("[]"),this.runScriptDebounced(),os.send({command:"filter_change",commandDetails:{total_filters_count:l.where.size,field_types:o.fields.map((e=>e.type)),operation:o.operation}})}},this.handleChangeOperation=({id:e})=>{var t,s;const{whereId:i}=this.props;null==(t=Tt.activeTab)||t.update({preview:!1});const a=null==(s=Tt.activeTab)?void 0:s.session.script,n=a.where.get(i);n&&(a.update({frozen:!1}),n.update({operation:e}),["in","notIn"].includes(n.operation)&&this.handleChangeValue("[]"),["isNull","isNotNull"].includes(n.operation)&&this.handleChangeValue(""),this.runScriptDebounced(),os.send({command:"filter_change",commandDetails:{total_filters_count:a.where.size,field_types:n.fields.map((e=>e.type)),operation:n.operation}}))},this.handleChangeValue=e=>{var t,s;const{whereId:i}=this.props;null==(t=Tt.activeTab)||t.update({preview:!1});const a=null==(s=Tt.activeTab)?void 0:s.session.script;a.update({frozen:!1});const n=a.where.get(i);n&&(n.update({value:e}),this.runScriptDebounced())},this.handleDelete=()=>{var e,t;const{whereId:s}=this.props;null==(e=Tt.activeTab)||e.update({preview:!1});const i=null==(t=Tt.activeTab)?void 0:t.session.script;Object.values(i.where.values).length,i.where.remove(s),this.runScriptDebounced()}}render(){const{whereId:e,rowIndex:t}=this.props,s=Tt.activeTab.session.script.where.get(e);return s?x.createElement(x.Fragment,null,x.createElement("div",{className:S(ia,sa)},x.createElement("div",{className:aa},x.createElement("div",{className:ya},0===t?"where":"and"))),x.createElement("div",{className:S(ia,sa)},s.fields.slice(0,2).map(((e,t)=>x.createElement("div",{className:aa,key:t},x.createElement(Aa,{"data-testid":"where-filter__row__field"+(t>0?"_relation_scalars":""),"data-test-invalid":!s.isValid,buttonClassName:na,type:"text",items:s.getFilterableFieldsAtIndex(t).map((e=>({id:e.id,label:e.name}))),selectedItem:{id:e.id,label:e.name},onSelect:e=>this.handleChangeField(e,t)}))))),x.createElement("div",{className:S(ra,sa)},x.createElement(Aa,{"data-testid":"where-filter__row__operation",buttonClassName:na,type:"text",items:s.supportedOperations.map((e=>({id:e,label:e}))),selectedItem:{id:s.operation,label:s.operation},onSelect:this.handleChangeOperation})),x.createElement("div",{className:S(la,sa)},x.createElement("input",{className:S({[fa]:!s.isValid}),"data-testid":"where-filter__row__value",type:"text",disabled:"isNull"===s.operation||"isNotNull"===s.operation,placeholder:"enter value...",value:null===s.value?"":s.value,onChange:e=>this.handleChangeValue(e.currentTarget.value)})),x.createElement("div",{className:S(oa,sa)},x.createElement(ti,{"data-testid":"where-filter__row__delete-btn",onClick:this.handleDelete},x.createElement(ea,null)))):null}}var Ta=_(Da);class Pa extends x.PureComponent{constructor(){super(...arguments),this.handleAddWhere=()=>{var e,t;null==(e=Tt.activeTab)||e.update({preview:!1});const s=null==(t=Tt.activeTab)?void 0:t.session.script;s.update({frozen:!1});const i=s.where.add({fieldIds:[s.model.uniqueIdentifier.fields[0].id],value:null});s.pagination.reset(),os.send({command:"filter_change",commandDetails:{total_filters_count:s.where.size,field_types:i.fields.map((e=>e.type)),operation:i.operation}})},this.handleResetFilters=()=>{var e,t;null==(e=Tt.activeTab)||e.update({preview:!1});const s=null==(t=Tt.activeTab)?void 0:t.session.script;s.where.clear(),s.run()}}render(){const{where:e}=Tt.activeTab.session.script,t=Object.values(e.values).length>0;return x.createElement("div",{className:S(pa,{[ma]:0===Object.values(e.values).length},{[ua]:!Tt.activeTab.isFiltersOpen})},t?x.createElement("div",{className:ta},Object.values(e.values).map(((e,t)=>x.createElement(Ta,{rowIndex:t,key:e.id,whereId:e.id})))):x.createElement("div",{className:ha},"ℹ️ Use filters to narrow your search results. Multiple filters show results at their intersection (AND)."),x.createElement("div",{className:t?ga:va},x.createElement(Qs,{"data-testid":"create-where-filter-btn",className:da,blue:!0,onClick:this.handleAddWhere},x.createElement(x.Fragment,null,x.createElement(ea,null),"Add a new filter")),t&&x.createElement(x.Fragment,null,x.createElement(Qs,{className:ca,onClick:this.handleResetFilters},"Clear all"))))}}var Va=_(Pa);var ja="_container_u5odf_1",Fa="_firstCommittedRow_u5odf_7",Ba="_tableCell_u5odf_11",Ha="_dirty_u5odf_11",Za="_invalid_u5odf_15",qa="_empty_u5odf_19";var Ua="_input_1ebqz_1";class za extends x.PureComponent{constructor(e){super(e),this.input=x.createRef(),this.getValue=()=>this.state.value,this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=e=>{const t=e.currentTarget.value;if(""!==t)try{const e=BigInt(t);this.setState({value:e})}catch(s){}else this.setState({value:null})};const{initialValue:t}=e,s=null==t?null:BigInt(t);this.state={value:s}}render(){const{value:e}=this.state;return x.createElement("input",{"data-testid":"input--bigint",ref:this.input,className:Ua,type:"string",value:null==e?"":e.toString(),placeholder:"null",onChange:this.handleChange})}}var $a=_(za);var Ja="_container_1f6qf_1";class Wa extends x.PureComponent{constructor(){super(...arguments),this.handleDragStart=e=>{e.dataTransfer.effectAllowed="move",e.dataTransfer.setData("text/html",e.target.parentNode),e.dataTransfer.setDragImage(e.target.parentNode,20,20),this.props.onDragStart(e)},this.handleDragEnd=e=>{this.props.onDragEnd(e)}}render(){const e=this.props,{className:t,children:s}=e,i=d(e,["className","children"]);return x.createElement("div",o(l({draggable:!0,className:S(Ja,t)},i),{onDragStart:e=>this.handleDragStart(e),onDragEnd:e=>this.handleDragEnd(e)}),s)}}var Ka=_(Wa);function Ga(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{opacity:.5,fillRule:"evenodd",clipRule:"evenodd",d:"M24 10H0V6H24V10Z",fill:"currentColor"}),N.exports.createElement("path",{opacity:.5,fillRule:"evenodd",clipRule:"evenodd",d:"M24 18H0V14H24V18Z",fill:"currentColor"}))}var Qa="_container_i5u05_1",Ya="_input_i5u05_7",Xa="_itemContainer_i5u05_27",en="_itemScrollContainer_i5u05_45",tn="_item_i5u05_27",sn="_invalid_i5u05_53",an="_dragButton_i5u05_63",nn="_closeButton_i5u05_77",rn="_addScalarListItemBtn_i5u05_101",ln="_separator_i5u05_109",on="_draggedOver_i5u05_113";class dn extends x.PureComponent{constructor(e){super(e),this.items=[],this.draggedItem=null,this.getValue=()=>this.state.value,this.focus=()=>{var e;const{value:t}=this.state;t&&(null==(e=this.items[Math.max(t.length,0)].current)||e.focus())},this.handleChange=e=>{try{const t=JSON.parse(e.currentTarget.value);Array.isArray(t)&&this.setState({value:t.map((e=>BigInt(e)))})}catch(t){}},this.handleChangeItem=(e,t)=>{const{value:s}=this.state;try{let i=BigInt(t.currentTarget.value);if(!Array.isArray(s))throw F({path:"BigIntListInput.handleChangeItem",message:"Invalid value",context:{value:s,idx:e,changedItem:i}});const a=[...s];return a.splice(e,1,i),this.setState({value:a})}catch(i){}},this.handleChangeNewItem=e=>{try{let t=BigInt(e.currentTarget.value);this.setState({newItem:t})}catch(t){}},this.handleAddNewItem=()=>{const{value:e,newItem:t}=this.state;if(!Array.isArray(e))throw F({path:"BigIntListInput.handleAddItem",message:"Invalid value",context:{value:e,newItem:t}});this.items.push(x.createRef()),this.setState({value:[...e,t||BigInt(0)],newItem:null},(()=>{var e,t;null==(e=this.items[this.items.length-1].current)||e.focus(),null==(t=this.items[this.items.length-2].current)||t.scrollIntoView(!1)}))},this.handleEnterKeydown=e=>{const{value:t}=this.state;if(!Array.isArray(t))throw F({path:"BigIntListInput.handleEnterKeydown",message:"Invalid value",context:{value:t,idx:e}});let s=[...t];s.splice(e+1,0,null),this.items.push(x.createRef()),this.setState({value:s,newItem:null},(()=>{var t,s,i;null==(t=this.items[e+1].current)||t.focus(),null==(s=this.items[e+1].current)||s.scrollIntoView(!1),null==(i=this.items[e+1].current)||i.scrollIntoView({block:"nearest"})}))},this.handleRemoveItem=e=>{var t,s,i,a;const{value:n}=this.state;if(!Array.isArray(n))throw F({path:"BigIntListInput.handleRemoveClick",message:"Invalid Value",context:{value:n,idx:e}});this.items.splice(e,1);const r=[...n];r.splice(e,1),null==(s=null==(t=this.items[e-1])?void 0:t.current)||s.focus(),null==(a=null==(i=this.items[e-1])?void 0:i.current)||a.scrollIntoView(),setTimeout((()=>this.setState({value:r})),0)},this.handleTabKeydown=(e,t)=>{var s;const{value:i}=this.state;if(!Array.isArray(i))throw F({path:"BigIntListInput.handleTabKeydown",message:"Invalid value",context:{value:i,idx:e}});e===i.length||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e+1].current)||s.focus())},this.handleShiftTabKeydown=(e,t)=>{var s;0===e||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e-1].current)||s.focus())},this.handleFinishEditing=()=>{this.props.stopEditing()},this.handleDragStart=e=>{this.draggedItem=this.items[e]},this.handleDragOver=(e,t)=>{t.preventDefault(),this.setState({draggedOverIdx:e})},this.handleDragEnd=e=>{var t,s;if(!this.draggedItem)return;const{value:i=[]}=this.state;if(!Array.isArray(i))throw F({path:"BigIntListInput.HandleDragEnd",message:"Invalid value",context:{value:i,idx:e}});const a=[...i];let n=BigInt(null!=(s=null==(t=this.draggedItem.current)?void 0:t.value)?s:0);this.items.splice(e,1),a.splice(e,1),this.state.draggedOverIdx>e?(this.items.splice(this.state.draggedOverIdx-1,0,this.draggedItem),a.splice(this.state.draggedOverIdx-1,0,n)):(this.items.splice(this.state.draggedOverIdx,0,this.draggedItem),a.splice(this.state.draggedOverIdx,0,n)),this.draggedItem=null,this.setState({value:a,draggedOverIdx:-1})};const{initialValue:t}=e;if(!Array.isArray(t))throw F({path:"BigIntListInput.constructor",message:"Invalid initialValue",context:{initialValue:t}});this.state={value:t.map((e=>BigInt(e))),newItem:null,draggedOverIdx:-1},this.items=Array.from({length:Math.max(t.length,1)}).map((()=>x.createRef())),this.items.push(x.createRef())}render(){const{value:e}=this.state;if(!Array.isArray(e))throw F({path:"BigIntListInput.render",message:"Invalid value",context:{value:e}});return x.createElement(x.Fragment,null,x.createElement("div",{"data-testid":"input--bigint-list",className:Qa},x.createElement("input",{type:"text",className:Ya,value:e.map((e=>(null==e?void 0:e.toString())||"null")),onChange:this.handleChange}),x.createElement("ul",{className:S(Xa,"ag-theme-dark")},x.createElement("div",{className:en},x.createElement("div",{className:S(ln,{[on]:0===this.state.draggedOverIdx})}),e.map(((e,t)=>x.createElement(x.Fragment,{key:t},x.createElement(cn,{innerRef:this.items[t],value:null===e?"":e.toString(),invalid:null===e||Number.isNaN(e),onChange:this.handleChangeItem.bind(this,t),onRemove:this.handleRemoveItem.bind(this,t),onEnter:this.handleEnterKeydown.bind(this,t),onTab:this.handleTabKeydown.bind(this,t),onShiftTab:this.handleShiftTabKeydown.bind(this,t),onModEnter:this.handleFinishEditing.bind(this,t),onDragStart:this.handleDragStart.bind(this,t),onDragEnd:this.handleDragEnd.bind(this,t),onDragOver:this.handleDragOver.bind(this,t)}),x.createElement("div",{className:S(ln,{[on]:this.state.draggedOverIdx===t+1})}))))),x.createElement(cn,{innerRef:this.items[e.length],value:null===this.state.newItem?"":this.state.newItem.toString(),invalid:!1,onChange:this.handleChangeNewItem,onAdd:this.handleAddNewItem,onEnter:this.handleAddNewItem,onShiftTab:this.handleShiftTabKeydown.bind(this,e.length),onDragOver:this.handleDragOver.bind(this,e.length)}))))}}class cn extends x.Component{render(){return x.createElement("li",{"data-testid":"input--bigint-list-item",className:S(tn,{[sn]:this.props.invalid}),onDragOver:this.props.onDragOver},this.props.onDragStart&&this.props.onDragEnd&&x.createElement(Ka,{className:an,onDragStart:this.props.onDragStart,onDragEnd:this.props.onDragEnd},x.createElement(Ga,null)),x.createElement("input",{ref:this.props.innerRef,className:Ya,type:"text",value:this.props.value,tabIndex:this.props.tabIndex,onChange:this.props.onChange}),this.props.onAdd&&x.createElement(Qs,{"data-testid":"input--bigint-list-item__add-btn",onClick:this.props.onAdd,className:rn},"Add"),this.props.onRemove&&x.createElement(Qs,{"data-testid":"input--bigint-list-item__remove-btn",onClick:e=>{var t,s;return null==(s=(t=this.props).onRemove)?void 0:s.call(t,e)},className:nn},x.createElement(ea,null)),this.props.onEnter&&x.createElement(si,{keys:"enter",target:this.props.innerRef,onMatch:this.props.onEnter}),this.props.onTab&&x.createElement(si,{keys:["tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onTab}),this.props.onShiftTab&&x.createElement(si,{keys:["shift+tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onShiftTab}),this.props.onModEnter&&x.createElement(si,{keys:["mod+enter"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onModEnter}))}}var hn=_(dn);var pn="_container_z3tbz_1",un="_input_z3tbz_7",mn="_dropdown_z3tbz_19",gn="_match_z3tbz_37",vn="_highlight_z3tbz_45";class fn extends x.Component{constructor(e){super(e),this.input=x.createRef(),this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=e=>{const{suggestions:t,onChange:s}=this.props,i=e.currentTarget.value,a=t.findIndex((e=>e.toUpperCase().startsWith(i.toUpperCase())));this.setState({phrase:i,highlightIdx:a}),s(t[a]||t[0])},this.handleSuggestionClick=e=>{const{suggestions:t,onChange:s,onSubmit:i}=this.props,a=t[e];s(a),null==i||i(a)},this.handleSubmit=()=>{const{highlightIdx:e}=this.state,{suggestions:t,onChange:s,onSubmit:i}=this.props,a=t[e]||t[0];s(a),null==i||i(a)},this.state={phrase:String(e.value),highlightIdx:0}}render(){const{phrase:e,highlightIdx:t}=this.state,{className:s,style:i,suggestions:a,dataTestId:n}=this.props;return x.createElement("div",{"data-testid":n,className:S(pn,s),style:i},x.createElement("input",{ref:this.input,type:"text",className:un,value:e,onChange:this.handleChange}),x.createElement("ul",{className:mn},a.map(((e,s)=>x.createElement("li",{key:e,"data-testid":"suggestion",className:S(gn,{[vn]:t===s}),onClick:()=>this.handleSuggestionClick(s)},e)))),x.createElement(si,{target:this.input,keys:"up",onMatch:()=>this.setState({highlightIdx:Math.max(t-1,0)})}),x.createElement(si,{target:this.input,keys:"down",onMatch:()=>this.setState((e=>({highlightIdx:Math.min(e.highlightIdx+1,a.length-1)})))}),x.createElement(si,{target:this.input,preventDefault:!1,stopPropagation:!1,keys:"enter",onMatch:this.handleSubmit}))}}var yn=_(fn);var In="_input_j8mok_1";class Cn extends x.PureComponent{constructor(e){super(e),this.input=x.createRef(),this.getValue=()=>this.state.value,this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=e=>{this.setState({value:"true"===e})},this.handleSubmit=e=>{this.setState({value:"true"===e},(()=>{this.props.stopEditing()}))},this.state={value:!!e.initialValue}}render(){const{value:e}=this.state;return x.createElement(yn,{ref:this.input,dataTestId:"input--boolean",className:In,value:null==e?"":String(e),suggestions:["true","false"],onChange:this.handleChange,onSubmit:this.handleSubmit})}}var wn=_(Cn);var bn="_itemContainer_1k9ef_1",En="_itemDropdown_1k9ef_5";class _n extends x.PureComponent{constructor(e){var t;super(e),this.items=[],this.draggedItem=null,this.getValue=()=>this.state.value,this.focus=()=>{var e;const{value:t}=this.state;t&&(null==(e=this.items[Math.max(t.length-1,0)].current)||e.focus())},this.handleChangeItem=(e,t)=>{const{value:s}=this.state;if(!Array.isArray(s))throw F({path:"BooleanListInput.handleChangeItem",message:"Invalid value",context:{value:s,changedItem:e}});const i=[...s];return i.splice(t,1,"true"===e.id),this.setState({value:i})},this.handleChangeNewItem=e=>{this.setState({newItem:e},(()=>setTimeout((()=>this.handleAddNewItem()),0)))},this.handleAddNewItem=()=>{const{value:e,newItem:t}=this.state;if(t){if(!Array.isArray(e))throw F({path:"BooleanListInput.handleAddNewItem",message:"Invalid value",context:{value:e,newItem:t}});this.items.push(x.createRef()),this.setState({value:[...e,"true"===t.id],newItem:null},(()=>{var e,t,s;null==(t=null==(e=this.items[this.items.length-2])?void 0:e.current)||t.focus(),null==(s=this.items[this.items.length-2].current)||s.scrollIntoView(!1)}))}},this.handleRemoveItem=e=>{const{value:t}=this.state;if(!Array.isArray(t))throw F({path:"BooleanListInput.handleRemoveClick",message:"Invalid Value",context:{value:t,idx:e}});this.items.splice(e,1);const s=[...t];s.splice(e,1),setTimeout((()=>this.setState({value:s})),0)},this.handleDragStart=e=>{this.draggedItem=this.items[e]},this.handleDragOver=(e,t)=>{t.preventDefault(),this.setState({draggedOverIdx:e})},this.handleDragEnd=e=>{var t,s;if(!this.draggedItem)return;const i=null!=(t=this.state.value)?t:[];if(!Array.isArray(i))throw F({path:"BooleanListInput.HandleDragEnd",message:"Invalid value",context:{value:i,idx:e}});const a=String(null==(s=this.draggedItem.current)?void 0:s.value),n=[...i];this.items.splice(e,1),n.splice(e,1),this.state.draggedOverIdx>e?(this.items.splice(this.state.draggedOverIdx-1,0,this.draggedItem),n.splice(this.state.draggedOverIdx-1,0,"true"===a)):(this.items.splice(this.state.draggedOverIdx,0,this.draggedItem),n.splice(this.state.draggedOverIdx,0,"true"===a)),this.draggedItem=null,this.setState({value:n,draggedOverIdx:-1})};const{initialValue:s}=e;if(!Array.isArray(s))throw F({path:"BooleanListInput.constructor",message:"Invalid initialValue",context:{initialValue:s}});this.state={value:s,newItem:null,draggedOverIdx:-1},this.items=Array.from({length:Math.max(null!=(t=null==s?void 0:s.length)?t:0,1)}).map((()=>x.createRef())),this.items.push(x.createRef())}render(){const{value:e}=this.state;if(!Array.isArray(e))throw F({path:"BooleanListInput.render",message:"Invalid value",context:{value:e}});return x.createElement(x.Fragment,null,x.createElement("div",{"data-testid":"input--boolean-list",className:Qa},x.createElement("input",{type:"text",className:Ya,value:JSON.stringify(e),readOnly:!0}),x.createElement("ul",{className:S(Xa,bn,"ag-theme-dark")},x.createElement("div",{className:en},x.createElement("div",{className:S(ln,{[on]:0===this.state.draggedOverIdx})}),e.map(((e,t)=>x.createElement(x.Fragment,{key:t},x.createElement(xn,{innerRef:this.items[t],suggestions:[{id:"true",label:"true"},{id:"false",label:"false"}],value:{id:String(e),label:String(e)},invalid:!0!==e&&!1!==e,onChange:e=>this.handleChangeItem(e,t),onRemove:this.handleRemoveItem.bind(this,t),onDragStart:this.handleDragStart.bind(this,t),onDragEnd:this.handleDragEnd.bind(this,t),onDragOver:this.handleDragOver.bind(this,t)}),x.createElement("div",{className:S(ln,{[on]:this.state.draggedOverIdx===t+1})}))))),x.createElement(xn,{key:e.length,innerRef:this.items[e.length],suggestions:[{id:"",label:""},{id:"true",label:"true"},{id:"false",label:"false"}],value:{id:"",label:""},invalid:!1,onChange:e=>this.handleChangeNewItem(e),onAdd:this.handleAddNewItem,onDragOver:this.handleDragOver.bind(this,e.length)}))))}}class xn extends x.Component{render(){return x.createElement("li",{"data-testid":"input--boolean-list-item",className:S(tn,{[sn]:this.props.invalid}),onDragOver:this.props.onDragOver||void 0},this.props.onDragStart&&this.props.onDragEnd&&x.createElement(Ka,{className:an,onDragStart:this.props.onDragStart,onDragEnd:this.props.onDragEnd},x.createElement(Ga,null)),x.createElement(Aa,{innerRef:this.props.innerRef,"data-testid":"input",className:En,type:"text",nativeSelect:!0,items:this.props.suggestions,selectedItem:this.props.value,onSelect:e=>this.props.onChange(e)}),this.props.onAdd&&x.createElement(Qs,{"data-testid":"input--boolean-list-item__add-btn",onClick:this.props.onAdd,className:rn},"Add"),this.props.onRemove&&x.createElement(Qs,{"data-testid":"input--boolean-list-item__remove-btn",onClick:e=>{var t,s;return null==(s=(t=this.props).onRemove)?void 0:s.call(t,e)},className:nn},x.createElement(ea,null)))}}var Sn=_(_n);class Nn extends x.PureComponent{constructor(e){super(e),this.input=x.createRef(),this.getValue=()=>this.state.value?b.Buffer.from(this.state.value,"base64"):null,this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=e=>{const{field:t}=this.props,s=e.currentTarget.value;t.isRequired||""!==s?this.setState({value:s}):this.setState({value:null})};const{initialValue:t}=e,s=null==t?null:(e.initialValue instanceof Uint8Array?b.Buffer.from(e.initialValue):e.initialValue||b.Buffer.from("")).toString("base64");this.state={value:s}}render(){const{value:e}=this.state;return x.createElement("input",{"data-testid":"input--bytes",ref:this.input,className:Ua,type:"text",value:null==e?"":e,placeholder:"null",onChange:this.handleChange})}}var Ln=_(Nn);class Rn extends x.PureComponent{constructor(e){super(e),this.items=[],this.draggedItem=null,this.getValue=()=>{var e;return null==(e=this.state.value)?void 0:e.map((e=>b.Buffer.from(e||"","base64")))},this.focus=()=>{var e;const{value:t}=this.state;t&&(null==(e=this.items[Math.max(t.length,0)].current)||e.focus())},this.handleChange=e=>{try{const t=JSON.parse(e.currentTarget.value);Array.isArray(t)&&this.setState({value:t})}catch(t){}},this.handleChangeItem=(e,t)=>{const{value:s}=this.state,i=t.currentTarget.value;if(!Array.isArray(s))throw F({path:"BytesListInput.handleChangeItem",message:"Invalid value",context:{value:s,idx:e,changedItem:i}});const a=[...s];return a.splice(e,1,i),this.setState({value:a})},this.handleChangeNewItem=e=>{const t=e.currentTarget.value;this.setState({newItem:t})},this.handleAddNewItem=()=>{const{value:e,newItem:t}=this.state;if(!Array.isArray(e))throw F({path:"BytesListInput.handleAddItem",message:"Invalid value",context:{value:e,newItem:t}});this.items.push(x.createRef()),this.setState({value:[...e,t],newItem:null},(()=>{var e,t;null==(e=this.items[this.items.length-1].current)||e.focus(),null==(t=this.items[this.items.length-2].current)||t.scrollIntoView(!1)}))},this.handleEnterKeydown=e=>{const{value:t}=this.state;if(!Array.isArray(t))throw F({path:"BytesListInput.handleEnterKeydown",message:"Invalid value",context:{value:t,idx:e}});let s=[...t];s.splice(e+1,0,""),this.items.push(x.createRef()),this.setState({value:s,newItem:null},(()=>{var t,s,i;null==(t=this.items[e+1].current)||t.focus(),null==(s=this.items[e+1].current)||s.scrollIntoView(!1),null==(i=this.items[e+1].current)||i.scrollIntoView({block:"nearest"})}))},this.handleRemoveItem=(e,t)=>{var s,i,a,n;const{value:r}=this.state;if(t.preventDefault(),t.stopPropagation(),!Array.isArray(r))throw F({path:"BytesListInput.handleRemoveClick",message:"Invalid Value",context:{value:r,idx:e}});const l=[...r];l.splice(e,1),this.items.splice(e,1),null==(i=null==(s=this.items[e-1])?void 0:s.current)||i.focus(),null==(n=null==(a=this.items[e-1])?void 0:a.current)||n.scrollIntoView(),setTimeout((()=>this.setState({value:l})),0)},this.handleTabKeydown=(e,t)=>{var s;const{value:i}=this.state;if(!Array.isArray(i))throw F({path:"BytesListInput.handleTabKeydown",message:"Invalid value",context:{value:i,idx:e}});e===i.length||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e+1].current)||s.focus())},this.handleShiftTabKeydown=(e,t)=>{var s;0===e||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e-1].current)||s.focus())},this.handleFinishEditing=()=>{this.props.stopEditing()},this.handleDragStart=e=>{this.draggedItem=this.items[e]},this.handleDragOver=(e,t)=>{t.preventDefault(),this.setState({draggedOverIdx:e})},this.handleDragEnd=e=>{var t;if(!this.draggedItem)return;const{value:s=[]}=this.state;if(!Array.isArray(s))throw F({path:"BytesListInput.HandleDragEnd",message:"Invalid value",context:{value:s,idx:e}});const i=[...s],a=(null==(t=this.draggedItem.current)?void 0:t.value)||"";this.items.splice(e,1),i.splice(e,1),this.state.draggedOverIdx>e?(this.items.splice(this.state.draggedOverIdx-1,0,this.draggedItem),i.splice(this.state.draggedOverIdx-1,0,a)):(this.items.splice(this.state.draggedOverIdx,0,this.draggedItem),i.splice(this.state.draggedOverIdx,0,a)),this.draggedItem=null,this.setState({value:i,draggedOverIdx:-1})};const{initialValue:t}=e;if(!Array.isArray(t))throw F({path:"BytesListInput.constructor",message:"Invalid initialValue",context:{initialValue:t}});this.state={value:t.map((e=>(e instanceof Uint8Array?b.Buffer.from(e):e).toString("base64"))),newItem:null,draggedOverIdx:-1},this.items=Array.from({length:Math.max(t.length,1)}).map((()=>x.createRef())),this.items.push(x.createRef())}render(){const{value:e}=this.state;if(!Array.isArray(e))throw F({path:"BytesListInput.render",message:"Invalid value",context:{value:e}});return x.createElement(x.Fragment,null,x.createElement("div",{"data-testid":"input--bytes-list",className:Qa},x.createElement("input",{type:"text",className:Ya,value:e.join(", "),onChange:this.handleChange}),x.createElement("ul",{className:S(Xa,"ag-theme-dark")},x.createElement("div",{className:en},x.createElement("div",{className:S(ln,{[on]:0===this.state.draggedOverIdx})}),e.map(((e,t)=>x.createElement(x.Fragment,{key:t},x.createElement(Mn,{innerRef:this.items[t],value:null===e?"":e,onChange:this.handleChangeItem.bind(this,t),onRemove:this.handleRemoveItem.bind(this,t),onEnter:this.handleEnterKeydown.bind(this,t),onTab:this.handleTabKeydown.bind(this,t),onShiftTab:this.handleShiftTabKeydown.bind(this,t),onModEnter:this.handleFinishEditing.bind(this,t),onDragStart:this.handleDragStart.bind(this,t),onDragEnd:this.handleDragEnd.bind(this,t),onDragOver:this.handleDragOver.bind(this,t)}),x.createElement("div",{className:S(ln,{[on]:this.state.draggedOverIdx===t+1})}))))),x.createElement(Mn,{innerRef:this.items[e.length],value:this.state.newItem||"",onChange:this.handleChangeNewItem,onAdd:this.handleAddNewItem,onEnter:this.handleAddNewItem,onShiftTab:this.handleShiftTabKeydown.bind(this,e.length),onDragOver:this.handleDragOver.bind(this,e.length)}))))}}class Mn extends x.Component{render(){return x.createElement("li",{"data-testid":"input--bytes-list-item",className:tn,onDragOver:this.props.onDragOver},this.props.onDragStart&&this.props.onDragEnd&&x.createElement(Ka,{className:an,onDragStart:this.props.onDragStart,onDragEnd:this.props.onDragEnd},x.createElement(Ga,null)),x.createElement("input",{ref:this.props.innerRef,className:Ya,type:"text",value:this.props.value,tabIndex:this.props.tabIndex,onChange:this.props.onChange}),this.props.onAdd&&x.createElement(Qs,{"data-testid":"input--bytes-list-item__add-btn",onClick:this.props.onAdd,className:rn},"Add"),this.props.onRemove&&x.createElement(Qs,{"data-testid":"input--bytes-list-item__remove-btn",onClick:this.props.onRemove,className:nn},x.createElement(ea,null)),this.props.onEnter&&x.createElement(si,{keys:"enter",target:this.props.innerRef,onMatch:this.props.onEnter}),this.props.onTab&&x.createElement(si,{keys:["tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onTab}),this.props.onShiftTab&&x.createElement(si,{keys:["shift+tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onShiftTab}),this.props.onModEnter&&x.createElement(si,{keys:["mod+enter"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onModEnter}))}}var On=_(Rn);class kn extends x.PureComponent{constructor(e){super(e),this.input=x.createRef(),this.getValue=()=>{const{value:e}=this.state;if(!e)return e;try{return new Date(e).toISOString()}catch(t){return e}},this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=e=>{const{field:t}=this.props,s=e.currentTarget.value;t.isRequired||""!==s?this.setState({value:s}):this.setState({value:null})};const{initialValue:t}=e,s=null==t?null:t;this.state={value:s}}render(){const{value:e}=this.state;return x.createElement("input",{"data-testid":"input--datetime",ref:this.input,className:Ua,type:"text",value:null==e?"":String(e),placeholder:"null",onChange:this.handleChange})}}var An=_(kn);class Dn extends x.PureComponent{constructor(e){var t;super(e),this.input=x.createRef(),this.getValue=()=>this.state.value,this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=e=>{this.setState({value:e})},this.handleSubmit=e=>{this.setState({value:e},(()=>{this.props.stopEditing()}))},this.state={value:null!=(t=e.initialValue)?t:null}}render(){const{value:e}=this.state,{field:t}=this.props;return x.createElement(yn,{ref:this.input,dataTestId:"input--enum",className:In,value:null==e?"":String(e),suggestions:t.typeAsEnum.values,onChange:this.handleChange,onSubmit:this.handleSubmit})}}var Tn=_(Dn);class Pn extends x.PureComponent{constructor(e){super(e),this.items=[],this.draggedItem=null,this.getValue=()=>this.state.value,this.focus=()=>{var e;const{value:t}=this.state;t&&(null==(e=this.items[Math.max(t.length-1,0)].current)||e.focus())},this.handleChangeItem=(e,t)=>{const{value:s}=this.state,i=String(e.id);if(!Array.isArray(s))throw F({path:"EnumListInput.handleChangeItem",message:"Invalid value",context:{value:s,changedItem:i}});const a=[...s];return a.splice(t,1,i),this.setState({value:a})},this.handleChangeNewItem=e=>{this.setState({newItem:e},(()=>setTimeout((()=>this.handleAddNewItem()),0)))},this.handleAddNewItem=()=>{const{value:e,newItem:t}=this.state;if(t){if(!Array.isArray(e))throw F({path:"EnumListInput.handleAddNewItem",message:"Invalid value",context:{value:e,newItem:t}});this.items.push(x.createRef()),this.setState({value:[...e,String(t.id)],newItem:null},(()=>{var e,t,s;null==(t=null==(e=this.items[this.items.length-2])?void 0:e.current)||t.focus(),null==(s=this.items[this.items.length-2].current)||s.scrollIntoView(!1)}))}},this.handleRemoveItem=e=>{var t,s,i,a;const{value:n}=this.state;if(!Array.isArray(n))throw F({path:"EnumListInput.handleRemoveClick",message:"Invalid Value",context:{value:n,idx:e}});this.items.splice(e,1);const r=[...n];r.splice(e,1),null==(s=null==(t=this.items[e-1])?void 0:t.current)||s.focus(),null==(a=null==(i=this.items[e-1])?void 0:i.current)||a.scrollIntoView(),setTimeout((()=>this.setState({value:r})),0)},this.handleDragStart=e=>{this.draggedItem=this.items[e]},this.handleDragOver=(e,t)=>{t.preventDefault(),this.setState({draggedOverIdx:e})},this.handleDragEnd=e=>{var t;if(!this.draggedItem)return;const{value:s=[]}=this.state;if(!Array.isArray(s))throw F({path:"EnumListInput.HandleDragEnd",message:"Invalid value",context:{value:s,idx:e}});const i=String(null==(t=this.draggedItem.current)?void 0:t.value),a=[...s];this.items.splice(e,1),a.splice(e,1),this.state.draggedOverIdx>e?(this.items.splice(this.state.draggedOverIdx-1,0,this.draggedItem),a.splice(this.state.draggedOverIdx-1,0,i)):(this.items.splice(this.state.draggedOverIdx,0,this.draggedItem),a.splice(this.state.draggedOverIdx,0,i)),this.draggedItem=null,this.setState({value:a,draggedOverIdx:-1})};const{initialValue:t}=e;if(!Array.isArray(t))throw F({path:"EnumListInput.constructor",message:"Invalid initialValue",context:{initialValue:t}});this.state={value:t,newItem:null,draggedOverIdx:-1},this.items=Array.from({length:Math.max(t.length,1)}).map((()=>x.createRef())),this.items.push(x.createRef())}render(){const{value:e}=this.state,{field:t}=this.props;if(!Array.isArray(e))throw F({path:"EnumListInput.render",message:"Invalid value",context:{value:e}});return x.createElement(x.Fragment,null,x.createElement("div",{"data-testid":"input--enum-list",className:Qa},x.createElement("input",{type:"text",className:Ya,value:JSON.stringify(e),readOnly:!0}),x.createElement("ul",{className:S(Xa,bn,"ag-theme-dark")},x.createElement("div",{className:en},x.createElement("div",{className:S(ln,{[on]:0===this.state.draggedOverIdx})}),e.map(((e,s)=>x.createElement(x.Fragment,{key:s},x.createElement(Vn,{innerRef:this.items[s],suggestions:t.typeAsEnum.values.map((e=>({id:e,label:e}))),value:{id:String(e),label:String(e)},onChange:e=>this.handleChangeItem(e,s),onRemove:this.handleRemoveItem.bind(this,s),onDragStart:this.handleDragStart.bind(this,s),onDragEnd:this.handleDragEnd.bind(this,s),onDragOver:this.handleDragOver.bind(this,s)}),x.createElement("div",{className:S(ln,{[on]:this.state.draggedOverIdx===s+1})}))))),x.createElement(Vn,{key:e.length,innerRef:this.items[e.length],suggestions:[{id:"",label:""},...t.typeAsEnum.values.map((e=>({id:e,label:e})))],value:{id:"",label:""},onChange:e=>this.handleChangeNewItem(e),onAdd:this.handleAddNewItem.bind(this),onDragOver:this.handleDragOver.bind(this,e.length)}))))}}class Vn extends x.Component{render(){return x.createElement("li",{className:tn,onDragOver:this.props.onDragOver||void 0},this.props.onDragStart&&this.props.onDragEnd&&x.createElement(Ka,{className:an,onDragStart:this.props.onDragStart,onDragEnd:this.props.onDragEnd},x.createElement(Ga,null)),x.createElement(Aa,{innerRef:this.props.innerRef,"data-testid":"input",className:En,type:"text",nativeSelect:!0,items:this.props.suggestions,selectedItem:this.props.value,onSelect:e=>this.props.onChange(e)}),this.props.onAdd&&x.createElement(Qs,{"data-testid":"input--enum-list__add-btn",onClick:this.props.onAdd,className:rn},"Add"),this.props.onRemove&&x.createElement(Qs,{"data-testid":"input--enum-list__remove-btn",onClick:e=>{var t,s;return null==(s=(t=this.props).onRemove)?void 0:s.call(t,e)},className:nn},x.createElement(ea,null)))}}var jn=_(Pn);var Fn="_input_c6cnd_1";class Bn extends x.PureComponent{constructor(e){var t;super(e),this.input=x.createRef(),this.getValue=()=>this.state.value,this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=(e=!1)=>{var t;const{api:s,field:i}=this.props,a=(null==(t=this.input.current)?void 0:t.value)||"";if(!i.isRequired&&""===a)return this.setState({value:null});try{const t=JSON.parse(a);if(i.isList&&!Array.isArray(t))throw new Error;return this.setState({value:t},(()=>e&&(null==s?void 0:s.stopEditing())))}catch(n){return this.setState({value:a},(()=>e&&(null==s?void 0:s.stopEditing())))}},this.state={value:null!=(t=e.initialValue)?t:null}}render(){const{value:e}=this.state;return x.createElement(x.Fragment,null,x.createElement("textarea",{"data-testid":"input--json",ref:this.input,className:Fn,value:"string"==typeof e?e:JSON.stringify(e),onChange:e=>this.handleChange()}))}}var Hn=_(Bn);class Zn extends x.PureComponent{constructor(e){super(e),this.items=[],this.draggedItem=null,this.getValue=()=>this.state.value,this.focus=()=>{var e;const{value:t}=this.state;t&&(null==(e=this.items[Math.max(t.length,0)].current)||e.focus())},this.handleChange=e=>{try{const t=JSON.parse(e.currentTarget.value);Array.isArray(t)&&this.setState({value:t.map((e=>String(e)))})}catch(t){}},this.handleChangeItem=(e,t)=>{var s;const{value:i}=this.state;if(!Array.isArray(i))throw F({path:"JsonListInput.handleChangeItem",message:"Invalid value",context:{value:i,idx:e}});let a=null!=(s=t.currentTarget.value)?s:"";try{a=JSON.parse(a)}catch(r){}const n=[...i];return n.splice(e,1,a),this.setState({value:n})},this.handleChangeNewItem=e=>{var t;let s=null!=(t=e.currentTarget.value)?t:"";try{s=JSON.parse(s)}catch(i){}this.setState({newItem:s})},this.handleAddNewItem=()=>{const{value:e,newItem:t}=this.state;if(!Array.isArray(e))throw F({path:"JsonListInput.handleAddItem",message:"Invalid value",context:{value:e,newItem:t}});this.items.push(x.createRef()),this.setState({value:[...e,t||""],newItem:null},(()=>{var e,t;null==(e=this.items[this.items.length-1].current)||e.focus(),null==(t=this.items[this.items.length-2].current)||t.scrollIntoView(!1)}))},this.handleEnterKeydown=e=>{const{value:t}=this.state;if(!Array.isArray(t))throw F({path:"JsonListInput.handleEnterKeydown",message:"Invalid value",context:{value:t,idx:e}});let s=[...t];s.splice(e+1,0,""),this.items.push(x.createRef()),this.setState({value:s,newItem:null},(()=>{var t,s,i;null==(t=this.items[e+1].current)||t.focus(),null==(s=this.items[e+1].current)||s.scrollIntoView(!1),null==(i=this.items[e+1].current)||i.scrollIntoView({block:"nearest"})}))},this.handleRemoveItem=e=>{var t,s,i,a;const{value:n}=this.state;if(!Array.isArray(n))throw F({path:"JsonListInput.handleRemoveClick",message:"Invalid Value",context:{value:n,idx:e}});const r=[...n];r.splice(e,1),this.items.splice(e,1),null==(s=null==(t=this.items[e-1])?void 0:t.current)||s.focus(),null==(a=null==(i=this.items[e-1])?void 0:i.current)||a.scrollIntoView(),setTimeout((()=>this.setState({value:r})),0)},this.handleTabKeydown=(e,t)=>{var s;const{value:i}=this.state;if(!Array.isArray(i))throw F({path:"JsonListInput.handleTabKeydown",message:"Invalid value",context:{value:i,idx:e}});e===i.length||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e+1].current)||s.focus())},this.handleShiftTabKeydown=(e,t)=>{var s;0===e||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e-1].current)||s.focus())},this.handleFinishEditing=()=>{this.props.stopEditing()},this.handleDragStart=e=>{this.draggedItem=this.items[e]},this.handleDragOver=(e,t)=>{t.preventDefault(),this.setState({draggedOverIdx:e})},this.handleDragEnd=e=>{var t,s;if(!this.draggedItem)return;const{value:i=[]}=this.state;if(!Array.isArray(i))throw F({path:"JsonListInput.HandleDragEnd",message:"Invalid value",context:{value:i,idx:e}});let a=null!=(s=null==(t=this.draggedItem.current)?void 0:t.value)?s:"";const n=[...i];try{a=JSON.parse(a)}catch(r){}this.items.splice(e,1),n.splice(e,1),this.state.draggedOverIdx>e?(this.items.splice(this.state.draggedOverIdx-1,0,this.draggedItem),n.splice(this.state.draggedOverIdx-1,0,a)):(this.items.splice(this.state.draggedOverIdx,0,this.draggedItem),n.splice(this.state.draggedOverIdx,0,a)),this.draggedItem=null,this.setState({value:n,draggedOverIdx:-1})};const{initialValue:t}=e;if(!Array.isArray(t))throw F({path:"JsonListInput.constructor",message:"Invalid initialValue",context:{initialValue:t}});this.state={value:t,newItem:null,draggedOverIdx:-1},this.items=Array.from({length:Math.max(t.length,1)}).map((()=>x.createRef())),this.items.push(x.createRef())}render(){const{value:e,newItem:t}=this.state;if(!Array.isArray(e))throw F({path:"JsonListInput.render",message:"Invalid value",context:{value:e}});return x.createElement(x.Fragment,null,x.createElement("div",{"data-testid":"input--json-list",className:Qa},x.createElement("input",{type:"text",className:Ya,value:JSON.stringify(e),onChange:this.handleChange}),x.createElement("ul",{className:S(Xa,"ag-theme-dark")},x.createElement("div",{className:en},x.createElement("div",{className:S(ln,{[on]:0===this.state.draggedOverIdx})}),e.map(((e,t)=>x.createElement(x.Fragment,{key:t},x.createElement(qn,{innerRef:this.items[t],value:e,invalid:"string"==typeof e,onChange:this.handleChangeItem.bind(this,t),onRemove:this.handleRemoveItem.bind(this,t),onEnter:this.handleEnterKeydown.bind(this,t),onTab:this.handleTabKeydown.bind(this,t),onShiftTab:this.handleShiftTabKeydown.bind(this,t),onModEnter:this.handleFinishEditing.bind(this,t),onDragStart:this.handleDragStart.bind(this,t),onDragEnd:this.handleDragEnd.bind(this,t),onDragOver:this.handleDragOver.bind(this,t)}),x.createElement("div",{className:S(ln,{[on]:this.state.draggedOverIdx===t+1})}))))),x.createElement(qn,{innerRef:this.items[e.length],value:t,invalid:!1,onChange:this.handleChangeNewItem,onAdd:this.handleAddNewItem,onEnter:this.handleAddNewItem,onShiftTab:this.handleShiftTabKeydown.bind(this,e.length),onDragOver:this.handleDragOver.bind(this,e.length)}))))}}class qn extends x.Component{render(){return x.createElement("li",{className:S(tn,{[sn]:this.props.invalid}),onDragOver:this.props.onDragOver},this.props.onDragStart&&this.props.onDragEnd&&x.createElement(Ka,{className:an,onDragStart:this.props.onDragStart,onDragEnd:this.props.onDragEnd},x.createElement(Ga,null)),x.createElement("input",{ref:this.props.innerRef,className:Ya,type:"text",value:null===this.props.value?"":"string"==typeof this.props.value?this.props.value:JSON.stringify(this.props.value),tabIndex:this.props.tabIndex,onChange:this.props.onChange}),this.props.onAdd&&x.createElement(Qs,{"data-testid":"input--json-list__add-btn",onClick:this.props.onAdd,className:rn},"Add"),this.props.onRemove&&x.createElement(Qs,{"data-testid":"input--json-list__remove-btn",onClick:e=>{var t,s;return null==(s=(t=this.props).onRemove)?void 0:s.call(t,e)},className:nn},x.createElement(ea,null)),this.props.onEnter&&x.createElement(si,{keys:"enter",target:this.props.innerRef,onMatch:this.props.onEnter}),this.props.onTab&&x.createElement(si,{keys:["tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onTab}),this.props.onShiftTab&&x.createElement(si,{keys:["shift+tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onShiftTab}),this.props.onModEnter&&x.createElement(si,{keys:["mod+enter"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onModEnter}))}}var Un=_(Zn);class zn extends x.PureComponent{constructor(e){super(e),this.input=x.createRef(),this.getValue=()=>this.state.value,this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=e=>{const t=e.currentTarget.value;if(""===t)return void this.setState({value:null});const s=parseFloat(t);this.setState({value:s})};const{initialValue:t}=e,s=null==t?null:parseFloat(`${t}`);this.state={value:s}}render(){const{value:e}=this.state;return x.createElement("input",{"data-testid":"input--number",ref:this.input,className:Ua,type:"number",value:null==e?"":String(e),placeholder:"null",onChange:this.handleChange})}}var $n=_(zn);class Jn extends x.PureComponent{constructor(e){super(e),this.items=[],this.draggedItem=null,this.getValue=()=>this.state.value,this.focus=()=>{var e;const{value:t}=this.state;t&&(null==(e=this.items[Math.max(t.length,0)].current)||e.focus())},this.handleChange=e=>{try{const t=JSON.parse(e.currentTarget.value);Array.isArray(t)&&this.setState({value:t.map((e=>parseInt(e))).filter((e=>!isNaN(e)))})}catch(t){}},this.handleChangeItem=(e,t)=>{const{value:s}=this.state,{field:i}=this.props;let a=i.isInt?parseInt(t.currentTarget.value):parseFloat(t.currentTarget.value);if(isNaN(a)&&(a=null),!Array.isArray(s))throw F({path:"NumberListInput.handleChangeItem",message:"Invalid value",context:{value:s,idx:e,changedItem:a}});const n=[...s];return n.splice(e,1,a),this.setState({value:n})},this.handleChangeNewItem=e=>{const{field:t}=this.props;let s=t.isInt?parseInt(e.currentTarget.value):parseFloat(e.currentTarget.value);isNaN(s)&&(s=null),this.setState({newItem:s})},this.handleAddNewItem=()=>{const{value:e,newItem:t}=this.state;if(!Array.isArray(e))throw F({path:"NumberListInput.handleAddItem",message:"Invalid value",context:{value:e,newItem:t}});this.items.push(x.createRef()),this.setState({value:[...e,t||0],newItem:null},(()=>{var e,t;null==(e=this.items[this.items.length-1].current)||e.focus(),null==(t=this.items[this.items.length-2].current)||t.scrollIntoView(!1)}))},this.handleEnterKeydown=e=>{const{value:t}=this.state;if(!Array.isArray(t))throw F({path:"NumberListInput.handleEnterKeydown",message:"Invalid value",context:{value:t,idx:e}});let s=[...t];s.splice(e+1,0,null),this.items.push(x.createRef()),this.setState({value:s,newItem:null},(()=>{var t,s,i;null==(t=this.items[e+1].current)||t.focus(),null==(s=this.items[e+1].current)||s.scrollIntoView(!1),null==(i=this.items[e+1].current)||i.scrollIntoView({block:"nearest"})}))},this.handleRemoveItem=e=>{var t,s,i,a;const{value:n}=this.state;if(!Array.isArray(n))throw F({path:"NumberListInput.handleRemoveClick",message:"Invalid Value",context:{value:n,idx:e}});this.items.splice(e,1);const r=[...n];r.splice(e,1),null==(s=null==(t=this.items[e-1])?void 0:t.current)||s.focus(),null==(a=null==(i=this.items[e-1])?void 0:i.current)||a.scrollIntoView(),setTimeout((()=>this.setState({value:r})),0)},this.handleTabKeydown=(e,t)=>{var s;const{value:i}=this.state;if(!Array.isArray(i))throw F({path:"NumberListInput.handleTabKeydown",message:"Invalid value",context:{value:i,idx:e}});e===i.length||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e+1].current)||s.focus())},this.handleShiftTabKeydown=(e,t)=>{var s;0===e||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e-1].current)||s.focus())},this.handleFinishEditing=()=>{this.props.stopEditing()},this.handleDragStart=e=>{this.draggedItem=this.items[e]},this.handleDragOver=(e,t)=>{t.preventDefault(),this.setState({draggedOverIdx:e})},this.handleDragEnd=e=>{var t,s,i,a;if(!this.draggedItem)return;const{field:n}=this.props,{value:r=[]}=this.state;if(!Array.isArray(r))throw F({path:"NumberListInput.HandleDragEnd",message:"Invalid value",context:{value:r,idx:e}});const l=[...r];let o=n.isInt?parseInt(null!=(s=null==(t=this.draggedItem.current)?void 0:t.value)?s:""):parseFloat(null!=(a=null==(i=this.draggedItem.current)?void 0:i.value)?a:"");isNaN(o)&&(o=null),this.items.splice(e,1),l.splice(e,1),this.state.draggedOverIdx>e?(this.items.splice(this.state.draggedOverIdx-1,0,this.draggedItem),l.splice(this.state.draggedOverIdx-1,0,o)):(this.items.splice(this.state.draggedOverIdx,0,this.draggedItem),l.splice(this.state.draggedOverIdx,0,o)),this.draggedItem=null,this.setState({value:l,draggedOverIdx:-1})};const{initialValue:t}=e;if(!Array.isArray(t))throw F({path:"NumberListInput.constructor",message:"Invalid initialValue",context:{initialValue:t}});this.state={value:t,newItem:null,draggedOverIdx:-1},this.items=Array.from({length:Math.max(t.length,1)}).map((()=>x.createRef())),this.items.push(x.createRef())}render(){const{value:e}=this.state;if(!Array.isArray(e))throw F({path:"NumberListInput.render",message:"Invalid value",context:{value:e}});return x.createElement(x.Fragment,null,x.createElement("div",{"data-testid":"input--number-list",className:Qa},x.createElement("input",{type:"text",className:Ya,value:JSON.stringify(e),onChange:this.handleChange}),x.createElement("ul",{className:S(Xa,"ag-theme-dark")},x.createElement("div",{className:en},x.createElement("div",{className:S(ln,{[on]:0===this.state.draggedOverIdx})}),e.map(((e,t)=>x.createElement(x.Fragment,{key:t},x.createElement(Wn,{innerRef:this.items[t],value:null===e?"":String(e),invalid:null===e||Number.isNaN(e),onChange:this.handleChangeItem.bind(this,t),onRemove:this.handleRemoveItem.bind(this,t),onEnter:this.handleEnterKeydown.bind(this,t),onTab:this.handleTabKeydown.bind(this,t),onShiftTab:this.handleShiftTabKeydown.bind(this,t),onModEnter:this.handleFinishEditing.bind(this,t),onDragStart:this.handleDragStart.bind(this,t),onDragEnd:this.handleDragEnd.bind(this,t),onDragOver:this.handleDragOver.bind(this,t)}),x.createElement("div",{className:S(ln,{[on]:this.state.draggedOverIdx===t+1})}))))),x.createElement(Wn,{innerRef:this.items[e.length],value:null===this.state.newItem?"":String(this.state.newItem),invalid:!1,onChange:this.handleChangeNewItem,onAdd:this.handleAddNewItem,onEnter:this.handleAddNewItem,onShiftTab:this.handleShiftTabKeydown.bind(this,e.length),onDragOver:this.handleDragOver.bind(this,e.length)}))))}}class Wn extends x.Component{render(){return x.createElement("li",{"data-testid":"input--number-list-item",className:S(tn,{[sn]:this.props.invalid}),onDragOver:this.props.onDragOver},this.props.onDragStart&&this.props.onDragEnd&&x.createElement(Ka,{className:an,onDragStart:this.props.onDragStart,onDragEnd:this.props.onDragEnd},x.createElement(Ga,null)),x.createElement("input",{ref:this.props.innerRef,className:Ya,type:"number",value:this.props.value,tabIndex:this.props.tabIndex,onChange:this.props.onChange}),this.props.onAdd&&x.createElement(Qs,{"data-testid":"input--number-list-item__add-btn",onClick:this.props.onAdd,className:rn},"Add"),this.props.onRemove&&x.createElement(Qs,{"data-testid":"input--number-list-item__remove-btn",onClick:e=>{var t,s;return null==(s=(t=this.props).onRemove)?void 0:s.call(t,e)},className:nn},x.createElement(ea,null)),this.props.onEnter&&x.createElement(si,{keys:"enter",target:this.props.innerRef,onMatch:this.props.onEnter}),this.props.onTab&&x.createElement(si,{keys:["tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onTab}),this.props.onShiftTab&&x.createElement(si,{keys:["shift+tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onShiftTab}),this.props.onModEnter&&x.createElement(si,{keys:["mod+enter"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onModEnter}))}}var Kn=_(Jn);var Gn="_container_hrw1c_1",Qn="_table_hrw1c_11",Yn="_footer_hrw1c_23",Xn="_footerBtnUnderline_hrw1c_30",er="_footerBtnMargin_hrw1c_41";var tr="_pill_14n8h_1",sr="_modelName_14n8h_18",ir="_isNull_14n8h_21",ar="_isPressed_14n8h_21",nr="_count_14n8h_30";class rr extends x.Component{render(){const{type:e,count:t=0,isList:s=!1,isNull:i=!1,isPressed:a=!1,onClick:n}=this.props;return x.createElement("div",{"data-testid":"relation-pill","data-test-null":`${i}`,className:S(tr,{[ir]:i,[ar]:a}),onClick:n},s&&x.createElement("span",{className:nr},t),x.createElement("span",{className:sr},e))}}var lr="_container_dd10p_1",or="_title_dd10p_11",dr="_input_dd10p_16",cr="_inputPlaceholder_dd10p_32";class hr extends N.exports.Component{constructor(e){super(e),this.handleChangeSearch=e=>{const{field:t,script:s}=this.props,i=Object.values(s.where.values).find((e=>c.exports.last(e.fieldIds)===t.id));if(!i)throw F({path:"TableCellHeaderWithSearch.handleChangeSearch",message:"Unable to find script `where` to update",context:{field:t.serialize(),script:s.serialize(),where:s.where.values}});i.update({enabled:!0,value:e.currentTarget.value}),this.handleSearchDebounced()},this.handleSearch=async()=>{this.props.onSearch()},this.handleSearchDebounced=c.exports.debounce(this.handleSearch,500,{leading:!1,trailing:!0})}render(){var e,t;const{script:s,field:i}=this.props,a=Object.values(s.where.values).find((e=>c.exports.last(e.fieldIds)===i.id));return N.exports.createElement("div",{"data-testid":"header-field-search",className:lr},N.exports.createElement("div",{className:or},i.name),a&&((null==(e=c.exports.last(a.fields))?void 0:e.isScalar)||(null==(t=c.exports.last(a.fields))?void 0:t.isEnum))?N.exports.createElement("input",{className:dr,type:"text",placeholder:`search ${i.name}...`,value:a.value||"",onChange:this.handleChangeSearch}):N.exports.createElement("div",{className:cr}))}}var pr=_(hr);var ur="_container_1lgfw_1",mr="_placeholder_1lgfw_8",gr="_relation_1lgfw_11",vr="_loading_1lgfw_16";class fr extends x.Component{constructor(){super(...arguments),this.handleClickRelation=()=>{const{api:e,node:t,field:s}=this.props;this.props.resizeRowForRelation(t,s);const i=e.getFocusedCell();e.startEditingCell({rowIndex:i.rowIndex,colKey:i.column})},this.getRenderedValue=()=>{const{node:{data:e},field:t}=this.props,s=e,i=s.value[t.name];return s?t.isRelation?null:t.isJson?JSON.stringify(i||null):void 0===i?t.defaultAsString:null===i?"null":t.isBigInt?t.isList?"["+i.map((e=>e.toString())).join(",")+"]":i.toString():t.isBytes?t.isList?"["+i.map((e=>`"${(e instanceof Uint8Array?b.Buffer.from(e):e).toString("base64")}"`)).join(",")+"]":(i instanceof Uint8Array?b.Buffer.from(i):i).toString("base64"):t.isList?JSON.stringify(i):String(i):""}}render(){const{node:{data:e},field:t}=this.props,s=e;if(!s)return x.createElement("div",{className:vr});const i=s.value[t.name],a=void 0===i,n=null===i;return x.createElement("div",{className:S(ur,{[mr]:a||n,[gr]:t.isRelation})},t.isRelation&&x.createElement(rr,{type:t.type,isList:t.isList,isNull:!i,count:(i||[]).length,onClick:this.handleClickRelation}),this.getRenderedValue())}}var yr=_(fr);var Ir="_container_av89p_1";class Cr extends x.Component{render(){return x.createElement("div",{"data-testid":"empty-overlay",className:Ir},"There are no rows in this table")}}class wr extends x.Component{constructor(e){var t;if(super(e),this.container=x.createRef(),this.table=x.createRef(),this.getValue=()=>this.state.value,this.focus=()=>{},this.afterGuiAttached=async()=>{if(!this.container.current||!this.table.current)return;const{api:e,node:t,getTableDimensions:s}=this.props;if(!e)return;const i=s(),a=this.container.current.getBoundingClientRect(),n=t.rowHeight-Gr;this.table.current.style.height=`${n}px`,this.table.current.style.width=i.width-(V.readonly?0:32)-20+"px",this.table.current.style.left=i.left-a.left+(V.readonly?0:32)+10+"px"},this.handleGridReady=async e=>{const{value:t}=this.state,{field:s}=this.props;this.gridApi=e.api,V.readonly||await this.loadedScript.run();const i=t?Ie(s.typeAsModel.id,t):null,a=at.get(i);this.gridApi.setRowData([...a?[a]:[],...this.loadedScript.records.filter((e=>e.id!==i))]),this.selectConnectedRecords()},this.handleScroll=async e=>{const{node:t}=this.props;if(this.gridApi.stopEditing(),"horizontal"===e.direction)return;if(this.loadedScript.recordIds.length>=(this.loadedScript.model.count||0))return;const s=t.rowHeight-Gr,i=this.loadedScript.pagination.take;!this.loadedScript.running&&e.top+s>=Gr*(i-20)&&(this.gridApi.applyTransaction({add:await this.loadedScript.loadMore()}),this.selectConnectedRecords())},this.selectConnectedRecords=async()=>{var e;const{value:t}=this.state,{field:s}=this.props;if(null===t)return;const i=Ie(s.typeAsModel.id,t);null==(e=this.gridApi.getRowNode(i))||e.setSelected(!0,!0,!0)},this.handleSelectionChanged=e=>{var t;const{value:s}=this.state,{field:i}=this.props,a=s&&Ie(i.typeAsModel.id,s),n=e.api.getSelectedNodes().map((e=>e.id))[0]||null;n?a!==n&&(null==(t=this.gridApi.getRowNode(n))||t.setSelected(!0,!0),this.setState({value:at.get(n).value})):this.setState({value:null})},this.handleSearch=async()=>{const{value:e}=this.state,{field:t}=this.props;await this.loadedScript.run();if(!!Object.values(this.loadedScript.where.values).find((e=>null!==e.value&&""!==e.value)))this.gridApi.setRowData(this.loadedScript.records);else{const s=e?Ie(t.typeAsModel.id,e):null;this.gridApi.setRowData([...e?[at.get(s)]:[],...this.loadedScript.records.filter((e=>e.id!==s))])}this.selectConnectedRecords()},this.handleClickRelation=()=>{var e;null==(e=this.props.api)||e.stopEditing()},this.handleRowDoubleClicked=e=>{this.handleSelectionChanged(e),this.props.stopEditing()},this.handleViewConnections=()=>{const{field:e}=this.props,{value:t}=this.state;if(null===t)return;if(!e.typeAsModel)return;const s=e.typeAsModel,i=t?Ie(e.typeAsModel.id,t):null,a=at.get(i);if(!a)return;const n=Tt.add({modelId:s.id,preview:!1});s.uniqueIdentifier.fields.map((e=>{n.session.script.where.add({fieldIds:[e.id],operation:"equals",value:a.value[e.name]})})),Tt.switch({id:n.id})},!e.field.typeAsModel)throw F({path:"RelationInput.constructor",message:"Invalid field.typeAsModel",context:{field:e.field.serialize()}});this.state={value:null!=(t=e.initialValue)?t:null},this.loadedScript=St.add({name:null,frozen:!0,modelId:e.field.typeAsModel.id,fieldIds:e.field.typeAsModel.fieldIds},{skipPersist:!0}),this.loadedScript.fields.forEach((e=>{e.isScalar&&this.loadedScript.where.add({fieldIds:[e.id],operation:e.isInt||e.isFloat||e.isDecimal?"equals":"contains",value:null})}))}componentWillUnmount(){var e,t;null==(e=this.props.api)||e.resetRowHeights(),null==(t=this.props.api)||t.onRowHeightChanged()}render(){const{value:e}=this.state,{field:t}=this.props,s=t.typeAsModel.fields;return x.createElement(x.Fragment,null,x.createElement("div",{ref:this.container,className:Gn},x.createElement(rr,{type:t.type,isNull:!e,isPressed:!0,onClick:this.handleClickRelation})),x.createElement("div",{"data-testid":"input--relation",ref:this.table,className:S(Qn,"ag-theme-relations")},x.createElement(M,{rowSelection:"single",getRowNodeId:e=>e.id,suppressCellSelection:!0,headerHeight:64,frameworkComponents:{TableCellRenderer:yr,TableCellHeaderWithSearch:pr,TableEmptyOverlay:Cr},onGridReady:this.handleGridReady,onBodyScroll:this.handleScroll,onSelectionChanged:this.handleSelectionChanged,noRowsOverlayComponent:"TableEmptyOverlay"},x.createElement(O,{colId:"checkbox",headerName:"",editable:!1,resizable:!1,sortable:!1,hide:V.readonly,pinned:!0,suppressNavigable:!0,suppressMovable:!0,maxWidth:32,checkboxSelection:!0}),s.map((e=>x.createElement(O,{key:e.id,editable:!1,resizable:!1,sortable:!1,suppressMovable:!0,headerComponent:"TableCellHeaderWithSearch",headerComponentParams:{field:e,script:this.loadedScript,onSearch:this.handleSearch},cellRenderer:"TableCellRenderer",cellRendererParams:{field:e}})))),x.createElement("div",{className:Yn},x.createElement(Qs,{className:er,green:!0,"data-testid":"open-in-new-tab",disabled:null===e,onClick:this.handleViewConnections},"Open in new tab"))))}}var br=_(wr);class Er extends x.Component{constructor(e){if(super(e),this.container=x.createRef(),this.table=x.createRef(),this.getValue=()=>this.state.value,this.focus=()=>{},this.afterGuiAttached=async()=>{if(!this.container.current||!this.table.current)return;const{api:e,node:t,getTableDimensions:s}=this.props;if(!e)return;const i=s(),a=this.container.current.getBoundingClientRect(),n=t.rowHeight-Gr;this.table.current.style.height=`${n}px`,this.table.current.style.width=i.width-(V.readonly?0:32)-20+"px",this.table.current.style.left=i.left-a.left+(V.readonly?0:32)+10+"px"},this.handleGridReady=async e=>{const{value:t=[]}=this.state,{field:s}=this.props;this.gridApi=e.api,V.readonly||await this.loadedScript.run();const i=t.map((e=>Ie(s.typeAsModel.id,e))),a=s.getRelationIDFieldName;if(a){const e=t.map((e=>e[`${a}`]));let{error:i,data:n}=await window.transport.request({channel:"prisma",action:"clientRequest",payload:{data:{schemaHash:Kt.activeProject.schemaHash,modelName:s.typeAsModel.id,operation:"findMany",args:{where:{[a]:{in:e}}}}}});if(i)throw F({path:"runQuery",code:i.code,type:i.type,stack:i.stack,message:`Error in Prisma Client request: \n\n${i.message}`,context:{error:i}});n.forEach((e=>at.add({modelId:s.typeAsModel.id,value:e})))}this.gridApi.setRowData([...i.map((e=>at.get(e))),...this.loadedScript.records.filter((e=>!i.includes(e.id)))]),this.selectConnectedRecords()},this.handleScroll=async e=>{const{value:t=[]}=this.state,{node:s,field:i}=this.props;if("horizontal"===e.direction)return;if(this.loadedScript.recordIds.length>=(this.loadedScript.model.count||0))return;const a=!!Object.values(this.loadedScript.where.values).find((e=>{var t;return e.id!==(null==(t=this.whereFilterThatFetchesUnconnectedRecords)?void 0:t.id)&&null!==e.value&&""!==e.value})),n=s.rowHeight-Gr,r=(a?0:t.length)+this.loadedScript.pagination.take;if(!this.loadedScript.running&&e.top+n>=Gr*(r-20)){const e=await this.loadedScript.loadMore(),s=t.map((e=>Ie(i.typeAsModel.id,e)));this.gridApi.applyTransaction({add:e.filter((e=>!s.includes(e.id)))}),this.selectConnectedRecords()}},this.selectConnectedRecords=async()=>{const{value:e=[]}=this.state,{field:t}=this.props,s=e.map((e=>Ie(t.typeAsModel.id,e)));this.selectedRecordIds=[],this.gridApi.forEachNode((e=>{s.includes(e.id)&&(e.setSelected(!0,!1,!0),this.selectedRecordIds.push(e.id))}))},this.handleSelectionChanged=()=>{var e,t;const{value:s=[]}=this.state,{field:i}=this.props,a=s.map((e=>Ie(i.typeAsModel.id,e))),n=this.selectedRecordIds,r=this.gridApi.getSelectedNodes().map((e=>e.id));if(c.exports.isEqual(n,r))return;let l;if(n.length>r.length){const t=c.exports.difference(n,r);this.selectedRecordIds=this.selectedRecordIds.filter((e=>e!==t[0])),null==(e=this.gridApi.getRowNode(t[0]))||e.setSelected(!1),l=a.filter((e=>e!==t[0]))}else{const e=c.exports.difference(r,n);this.selectedRecordIds.push(e[0]),null==(t=this.gridApi.getRowNode(e[0]))||t.setSelected(!0),l=a.concat([e[0]])}this.setState({value:l.map((e=>at.get(e).value))})},this.handleClickRelation=()=>{var e;null==(e=this.props.api)||e.stopEditing()},this.handleSearch=async()=>{var e,t;const{value:s=[]}=this.state,{field:i}=this.props,a=!!Object.values(this.loadedScript.where.values).find((e=>{var t;return e.id!==(null==(t=this.whereFilterThatFetchesUnconnectedRecords)?void 0:t.id)&&null!==e.value&&""!==e.value}));if(a?null==(e=this.whereFilterThatFetchesUnconnectedRecords)||e.update({enabled:!1}):null==(t=this.whereFilterThatFetchesUnconnectedRecords)||t.update({enabled:!0}),await this.loadedScript.run(),a)this.gridApi.setRowData(this.loadedScript.records);else{const e=s.map((e=>Ie(i.typeAsModel.id,e)));this.gridApi.setRowData([...e.map((e=>at.get(e))),...this.loadedScript.records.filter((t=>!e.includes(t.id)))])}this.selectConnectedRecords()},this.handleSkipToUnconnected=()=>{const{value:e=[]}=this.state,{field:t}=this.props,s=e.length-1;if(-1===s)return;if(!t.typeAsModel)return;const i=at.get(Ie(t.typeAsModel.id,e[s]));if(!i)return;const a=this.gridApi.getRowNode(i.id);this.gridApi.ensureNodeVisible(a,"top")},this.handleViewConnections=()=>{const{field:e,node:t}=this.props,{value:s=[]}=this.state;if(0===s.length)return;if(!e.isRelation||!e.typeAsModel)return;const i=at.get(t.id);if(!i)return;const a=e.typeAsModel,n=a.fields.find((t=>t.isRelation&&t.relationName===e.relationName));if(!n)return;const r=i.model.uniqueIdentifier.fields[0],l=Tt.add({modelId:a.id,preview:!1});l.session.script.where.add({fieldIds:[n.id,r.id],operation:"equals",value:String(i.value[r.name])}),Tt.switch({id:l.id})},!e.field.typeAsModel)throw F({path:"RelationListInput.constructor",message:"Invalid field.typeAsModel",context:{field:e.field.serialize()}});const{initialValue:t}=e;this.state={value:null!=t?t:[]},this.loadedScript=St.add({name:null,frozen:!0,modelId:e.field.typeAsModel.id,fieldIds:e.field.typeAsModel.fieldIds},{skipPersist:!0}),this.loadedScript.fields.forEach((e=>{e.isScalar&&this.loadedScript.where.add({fieldIds:[e.id],operation:e.isInt||e.isFloat||e.isDecimal?"equals":"contains",value:null})}));const s=e.field.typeAsModel,i=at.get(e.node.id),a=s.fields.find((t=>t.isRelation&&t.relationName===e.field.relationName));if(s&&i&&a&&i.isCommitted){const e=JSON.stringify(i.model.uniqueIdentifier.fields.reduce(((e,t)=>(t.isBigInt?e[t.name]=i.value[t.name].toString():e[t.name]=i.value[t.name],e)),{}));this.whereFilterThatFetchesUnconnectedRecords=this.loadedScript.where.add({fieldIds:[a.id],operation:"equals",value:a.isList?`{ none: ${e} }`:`{ NOT: ${e} }`})}else this.whereFilterThatFetchesUnconnectedRecords=null;this.selectedRecordIds=[]}componentWillUnmount(){var e,t;null==(e=this.props.api)||e.resetRowHeights(),null==(t=this.props.api)||t.onRowHeightChanged()}render(){const{value:e=[]}=this.state,{field:t}=this.props,s=t.typeAsModel.fields;return x.createElement(x.Fragment,null,x.createElement("div",{ref:this.container,className:Gn},x.createElement(rr,{type:t.type,isList:!0,count:e.length,isNull:!1,isPressed:!0,onClick:this.handleClickRelation})),x.createElement("div",{"data-testid":"input--relation-list",ref:this.table,className:S(Qn,"ag-theme-relations")},x.createElement(M,{rowSelection:"multiple",getRowNodeId:e=>e.id,suppressCellSelection:!0,headerHeight:64,frameworkComponents:{TableCellRenderer:yr,TableCellHeaderWithSearch:pr,TableEmptyOverlay:Cr},rowMultiSelectWithClick:!0,onGridReady:this.handleGridReady,onBodyScroll:this.handleScroll,onSelectionChanged:this.handleSelectionChanged,noRowsOverlayComponent:"TableEmptyOverlay"},x.createElement(O,{colId:"checkbox",headerName:"",editable:!1,resizable:!1,sortable:!1,pinned:!0,hide:V.readonly,suppressNavigable:!0,maxWidth:32,checkboxSelection:!0}),s.map((e=>x.createElement(O,{key:e.id,editable:!1,resizable:!1,sortable:!1,suppressMovable:!0,headerComponent:"TableCellHeaderWithSearch",headerComponentParams:{field:e,script:this.loadedScript,onSearch:this.handleSearch},cellRenderer:"TableCellRenderer",cellRendererParams:{field:e}})))),x.createElement("div",{className:Yn},x.createElement(Qs,{"data-testid":"open-in-new-tab",disabled:0===e.length,className:er,green:!0,onClick:this.handleViewConnections},"Open in new tab"),x.createElement("button",{className:Xn,onClick:this.handleSkipToUnconnected,hidden:V.readonly},"Skip to unconnected records"))))}}var _r=_(Er);class xr extends x.PureComponent{constructor(e){var t;super(e),this.input=x.createRef(),this.getValue=()=>this.state.value,this.focus=()=>{var e;null==(e=this.input.current)||e.focus()},this.handleChange=e=>{const{field:t}=this.props,s=e.currentTarget.value;t.isRequired||""!==s?this.setState({value:s}):this.setState({value:null})},this.state={value:null!=(t=e.initialValue)?t:null}}render(){const{value:e}=this.state;return x.createElement("input",{"data-testid":"input--string",ref:this.input,className:Ua,type:"text",value:null==e?"":String(e),placeholder:"null",onChange:this.handleChange})}}var Sr=_(xr);class Nr extends x.PureComponent{constructor(e){super(e),this.items=[],this.draggedItem=null,this.getValue=()=>this.state.value,this.focus=()=>{var e;const{value:t}=this.state;t&&(null==(e=this.items[Math.max(t.length,0)].current)||e.focus())},this.handleChange=e=>{try{const t=JSON.parse(e.currentTarget.value);Array.isArray(t)&&this.setState({value:t.map((e=>String(e)))})}catch(t){}},this.handleChangeItem=(e,t)=>{const{value:s}=this.state,i=String(t.currentTarget.value);if(!Array.isArray(s))throw F({path:"StringListInput.handleChangeItem",message:"Invalid value",context:{value:s,idx:e,changedItem:i}});const a=[...s];return a.splice(e,1,i),this.setState({value:a})},this.handleChangeNewItem=e=>{const t=String(e.currentTarget.value);this.setState({newItem:t})},this.handleAddNewItem=()=>{const{value:e,newItem:t}=this.state;if(!Array.isArray(e))throw F({path:"StringListInput.handleAddItem",message:"Invalid value",context:{value:e,newItem:t}});this.items.push(x.createRef()),this.setState({value:[...e,t||""],newItem:null},(()=>{var e,t;null==(e=this.items[this.items.length-1].current)||e.focus(),null==(t=this.items[this.items.length-2].current)||t.scrollIntoView(!1)}))},this.handleEnterKeydown=e=>{const{value:t}=this.state;if(!Array.isArray(t))throw F({path:"StringListInput.handleEnterKeydown",message:"Invalid value",context:{value:t,idx:e}});let s=[...t];s.splice(e+1,0,""),this.items.push(x.createRef()),this.setState({value:s,newItem:null},(()=>{var t,s,i;null==(t=this.items[e+1].current)||t.focus(),null==(s=this.items[e+1].current)||s.scrollIntoView(!1),null==(i=this.items[e+1].current)||i.scrollIntoView({block:"nearest"})}))},this.handleRemoveItem=(e,t)=>{var s,i,a,n;const{value:r}=this.state;if(t.preventDefault(),t.stopPropagation(),!Array.isArray(r))throw F({path:"StringListInput.handleRemoveClick",message:"Invalid Value",context:{value:r,idx:e}});const l=[...r];l.splice(e,1),this.items.splice(e,1),null==(i=null==(s=this.items[e-1])?void 0:s.current)||i.focus(),null==(n=null==(a=this.items[e-1])?void 0:a.current)||n.scrollIntoView(),setTimeout((()=>this.setState({value:l})),0)},this.handleTabKeydown=(e,t)=>{var s;const{value:i}=this.state;if(!Array.isArray(i))throw F({path:"StringListInput.handleTabKeydown",message:"Invalid value",context:{value:i,idx:e}});e===i.length||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e+1].current)||s.focus())},this.handleShiftTabKeydown=(e,t)=>{var s;0===e||(t.preventDefault(),t.stopPropagation(),null==(s=this.items[e-1].current)||s.focus())},this.handleFinishEditing=()=>{this.props.stopEditing()},this.handleDragStart=e=>{this.draggedItem=this.items[e]},this.handleDragOver=(e,t)=>{t.preventDefault(),this.setState({draggedOverIdx:e})},this.handleDragEnd=e=>{var t;if(!this.draggedItem)return;const{value:s=[]}=this.state;if(!Array.isArray(s))throw F({path:"StringListInput.HandleDragEnd",message:"Invalid value",context:{value:s,idx:e}});const i=[...s],a=String(null==(t=this.draggedItem.current)?void 0:t.value);this.items.splice(e,1),i.splice(e,1),this.state.draggedOverIdx>e?(this.items.splice(this.state.draggedOverIdx-1,0,this.draggedItem),i.splice(this.state.draggedOverIdx-1,0,a)):(this.items.splice(this.state.draggedOverIdx,0,this.draggedItem),i.splice(this.state.draggedOverIdx,0,a)),this.draggedItem=null,this.setState({value:i,draggedOverIdx:-1})};const{initialValue:t}=e;if(!Array.isArray(t))throw F({path:"StringListInput.constructor",message:"Invalid initialValue",context:{initialValue:t}});this.state={value:t,newItem:null,draggedOverIdx:-1},this.items=Array.from({length:Math.max(t.length,1)}).map((()=>x.createRef())),this.items.push(x.createRef())}render(){const{value:e}=this.state;if(!Array.isArray(e))throw F({path:"StringListInput.render",message:"Invalid value",context:{value:e}});return x.createElement(x.Fragment,null,x.createElement("div",{"data-testid":"input--string-list",className:Qa},x.createElement("input",{type:"text",className:Ya,value:JSON.stringify(e),onChange:this.handleChange}),x.createElement("ul",{className:S(Xa,"ag-theme-dark")},x.createElement("div",{className:en},x.createElement("div",{className:S(ln,{[on]:0===this.state.draggedOverIdx})}),e.map(((e,t)=>x.createElement(x.Fragment,{key:t},x.createElement(Lr,{innerRef:this.items[t],value:e,onChange:this.handleChangeItem.bind(this,t),onRemove:this.handleRemoveItem.bind(this,t),onEnter:this.handleEnterKeydown.bind(this,t),onTab:this.handleTabKeydown.bind(this,t),onShiftTab:this.handleShiftTabKeydown.bind(this,t),onModEnter:this.handleFinishEditing.bind(this,t),onDragStart:this.handleDragStart.bind(this,t),onDragEnd:this.handleDragEnd.bind(this,t),onDragOver:this.handleDragOver.bind(this,t)}),x.createElement("div",{className:S(ln,{[on]:this.state.draggedOverIdx===t+1})}))))),x.createElement(Lr,{innerRef:this.items[e.length],value:this.state.newItem||"",onChange:this.handleChangeNewItem,onAdd:this.handleAddNewItem,onEnter:this.handleAddNewItem,onShiftTab:this.handleShiftTabKeydown.bind(this,e.length),onDragOver:this.handleDragOver.bind(this,e.length)}))))}}class Lr extends x.Component{render(){return x.createElement("li",{"data-testid":"input--string-list-item",className:tn,onDragOver:this.props.onDragOver},this.props.onDragStart&&this.props.onDragEnd&&x.createElement(Ka,{className:an,onDragStart:this.props.onDragStart,onDragEnd:this.props.onDragEnd},x.createElement(Ga,null)),x.createElement("input",{ref:this.props.innerRef,className:Ya,type:"text",value:this.props.value,tabIndex:this.props.tabIndex,onChange:this.props.onChange}),this.props.onAdd&&x.createElement(Qs,{"data-testid":"input--string-list-item__add-btn",onClick:this.props.onAdd,className:rn},"Add"),this.props.onRemove&&x.createElement(Qs,{"data-testid":"input--string-list-item__remove-btn",onClick:this.props.onRemove,className:nn},x.createElement(ea,null)),this.props.onEnter&&x.createElement(si,{keys:"enter",target:this.props.innerRef,onMatch:this.props.onEnter}),this.props.onTab&&x.createElement(si,{keys:["tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onTab}),this.props.onShiftTab&&x.createElement(si,{keys:["shift+tab"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onShiftTab}),this.props.onModEnter&&x.createElement(si,{keys:["mod+enter"],preventDefault:!1,stopPropagation:!1,target:this.props.innerRef,onMatch:this.props.onModEnter}))}}var Rr=_(Nr);var Mr="_container_1mf95_1",Or="_invalid_1mf95_6",kr="_phantom_1mf95_10";class Ar extends x.Component{constructor(e){super(e),this.container=x.createRef(),this.input=x.createRef(),this.afterGuiAttached=()=>{var e,t,s;const{api:i,node:a,field:n,resizeRowForRelation:r}=this.props;this.stopEditingImmediately?null==i||i.stopEditing():(r(a,n),null==(t=null==(e=this.input.current)?void 0:e.afterGuiAttached)||t.call(e),null==(s=this.input.current)||s.focus())},this.getValue=()=>{var e,t;return null==(t=null==(e=this.input.current)?void 0:e.getValue)?void 0:t.call(e)},this.isPopup=()=>!0,this.handleClickOutside=e=>{var t,s;(null==(t=this.container.current)?void 0:t.contains(e.target))||null==(s=this.props.api)||s.stopEditing()};const{node:{data:t},field:s}=e,i=t;this.stopEditingImmediately=!1,8===e.keyPress||127===e.keyPress?(this.initialValue=s.lowestValidValue,this.stopEditingImmediately=!0):e.charPress?e.field.isScalar&&!e.field.isList&&(this.initialValue=e.charPress):this.initialValue=i.value[s.name]}componentDidMount(){document.addEventListener("click",this.handleClickOutside)}componentWillUnmount(){document.removeEventListener("click",this.handleClickOutside)}render(){var e,t;const{node:{data:s},columnApi:i,column:a,field:n}=this.props,r=s,d=null!=(t=null==(e=i.getColumnState().find((e=>e.colId===a.getColId())))?void 0:e.width)?t:200,c=r.invalidFields.find((e=>e.field.id===n.id));return x.createElement("div",{ref:this.container,className:S(Mr,{[Or]:!!c}),style:{width:d}},n.isString&&(n.isList?x.createElement(Rr,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement(Sr,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),(n.isInt||n.isFloat||n.isDecimal)&&(n.isList?x.createElement(Kn,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement($n,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),n.isBigInt&&(n.isList?x.createElement(hn,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement($a,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),n.isBoolean&&(n.isList?x.createElement(Sn,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement(wn,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),n.isDateTime&&(n.isList?x.createElement(Rr,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement(An,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),n.isJson&&(n.isList?x.createElement(Un,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement(Hn,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),n.isBytes&&(n.isList?x.createElement(On,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement(Ln,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),n.isEnum&&(n.isList?x.createElement(jn,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement(Tn,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),n.isRelation&&(n.isList?x.createElement(_r,o(l({ref:this.input},this.props),{initialValue:this.initialValue})):x.createElement(br,o(l({ref:this.input},this.props),{initialValue:this.initialValue}))),c&&x.createElement("div",{className:kr},c.reason))}}var Dr=_(Ar);function Tr(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.99999 0H0V24H7.99999V21H4.00001V3H7.99999V0ZM16 0V3H19.9999V21H16V24H24V0H16Z"}))}function Pr(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.85714 5C3.07006 5 0 8.13402 0 12C0 15.866 3.07006 19 6.85714 19H17.143C20.9299 19 24 15.866 24 12C24 8.13402 20.9299 5 17.143 5H6.85714ZM6.85714 17.25C9.69746 17.25 12 14.8995 12 12C12 9.10051 9.69746 6.75 6.85714 6.75C4.01683 6.75 1.7143 9.10051 1.7143 12C1.7143 14.8995 4.01683 17.25 6.85714 17.25Z"}))}function Vr(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 10V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V10H4ZM0 6C0 2.68629 2.68629 0 6 0H18C21.3137 0 24 2.68629 24 6V18C24 21.3137 21.3137 24 18 24H6C2.68629 24 0 21.3137 0 18V6Z"}))}function jr(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24 4.8H0V0H24V4.8Z"}),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.8 14.4H0V9.60001H16.8V14.4Z"}),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M24 24H0V19.2H24V24Z"}))}function Fr(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0 8.0001C0 6.89554 0.89544 6.00015 1.99999 6.00015H22.0001C23.1046 6.00015 24 6.89554 24 8.0001C24 9.10465 23.1046 10.0001 22.0001 10.0001H1.99999C0.89544 10.0001 0 9.10465 0 8.0001Z"}),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0 15.9999C0 14.8954 0.89544 14 1.99999 14H22.0001C23.1046 14 24 14.8954 24 15.9999C24 17.1046 23.1046 17.9998 22.0001 17.9998H1.99999C0.89544 17.9998 0 17.1046 0 15.9999Z"}),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.29671 0.0223986C10.389 0.186247 11.1418 1.20459 10.9779 2.2969L7.97789 22.2965C7.81404 23.3887 6.7957 24.1414 5.70334 23.9777C4.611 23.8138 3.85829 22.7954 4.02216 21.7032L7.02216 1.70355C7.18601 0.611239 8.20435 -0.141449 9.29671 0.0223986Z"}),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.2967 0.0223986C19.3891 0.186247 20.1418 1.20459 19.9779 2.2969L16.9779 22.2965C16.8139 23.3887 15.7957 24.1414 14.7033 23.9777C13.611 23.8138 12.8583 22.7954 13.0222 21.7032L16.0222 1.70355C16.186 0.611239 17.2044 -0.141449 18.2967 0.0223986Z"}))}function Br(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"M9.5452 24V21.1961H8.44912C6.82779 21.1961 6.50809 20.7754 6.50809 18.6723V15.1291C6.50809 13.3829 5.38916 12.2613 3.32256 12.1211V11.8917C5.38916 11.7515 6.50809 10.6171 6.50809 8.88371V5.32767C6.50809 3.22464 6.82779 2.80404 8.44912 2.80404H9.5452V0H7.70696C4.40725 0 3.33397 1.15985 3.33397 4.72863V7.54542C3.33397 9.55924 2.51189 10.3367 0 10.222V13.778C2.51189 13.6761 3.33397 14.4535 3.33397 16.4674V19.2713C3.33397 22.8401 4.40725 24 7.70696 24H9.5452Z"}),N.exports.createElement("path",{d:"M16.293 24C19.5929 24 20.6659 22.8401 20.6659 19.2713V16.4674C20.6659 14.4535 21.4882 13.6761 24 13.778V10.222C21.4882 10.3367 20.6659 9.55924 20.6659 7.54542V4.72863C20.6659 1.15985 19.5929 0 16.293 0H14.4548V2.80404H15.5509C17.1722 2.80404 17.4919 3.22464 17.4919 5.32767V8.88371C17.4919 10.6171 18.6108 11.7515 20.6774 11.8917V12.1211C18.6108 12.2613 17.4919 13.3829 17.4919 15.1291V18.6723C17.4919 20.7754 17.1722 21.1961 15.5509 21.1961H14.4548V24H16.293Z"}))}function Hr(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"M18.327 24L16.5266 18.3105H7.4735L5.67304 24H0L8.76437 0H15.2018L24 24H18.327ZM15.2697 14.06C13.6051 8.90463 12.6653 5.98911 12.4502 5.31336C12.2463 4.63761 12.0991 4.10355 12.0085 3.71118C11.6349 5.10627 10.5648 8.55585 8.79833 14.06H15.2697Z"}))}var Zr="_icon_f25b9_1",qr="_required_f25b9_8";var Ur=_((({className:e,string:t=!1,int:s=!1,float:i=!1,boolean:a=!1,dateTime:n=!1,enumerable:r=!1,array:l=!1,required:o=!1})=>{let d;return d=l?Tr:t?Hr:s||i?Fr:a?Pr:r?jr:n?Vr:Br,x.createElement(x.Fragment,null,x.createElement(d,{className:S(Zr,e)}),x.createElement("span",{className:qr},!o&&"?"))}));function zr(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 9 7",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"M4.5 7L8.39711 0.25H0.602886L4.5 7Z",fill:"currentColor"}))}var $r={container:"_container_e5y85_1",sortable:"_sortable_e5y85_11",title:"_title_e5y85_15",spacer:"_spacer_e5y85_22",sortIndicator:"_sortIndicator_e5y85_26",visible:"_visible_e5y85_34",asc:"_asc_e5y85_37"};class Jr extends x.Component{constructor(){super(...arguments),this.state={wasReordered:!1},this.handleReorder=()=>{this.setState({wasReordered:!0})},this.handleSort=()=>{var e;const{wasReordered:t}=this.state,{field:s,script:i}=this.props;this.setState({wasReordered:!1}),s&&s.isSortable&&(t||(null==(e=Tt.activeTab)||e.update({preview:!1}),i.sort.fieldId===s.id&&"asc"===i.sort.order?i.sort.update({fieldId:s.id,order:"desc"}):i.sort.fieldId===s.id&&"desc"===i.sort.order?i.sort.update({fieldId:null,order:"asc"}):i.sort.update({fieldId:s.id,order:"asc"}),i.run(),os.send({command:"sort_change",commandDetails:{field_type:s.type}})))}}componentDidMount(){this.props.column.addEventListener("movingChanged",this.handleReorder)}componentWillUnmount(){this.props.column.removeEventListener("movingChanged",this.handleReorder)}render(){var e,t;const{field:s,script:i,displayName:a,columnApi:n,column:r}=this.props,l=null!=(t=null==(e=n.getColumnState().find((e=>e.colId===r.getColId())))?void 0:e.width)?t:200;return x.createElement("div",{"data-testid":"table__header__cell",className:S($r.container,{[$r.sortable]:s.isSortable}),style:{width:l},title:`${s.name} (${s.type})`,onClick:this.handleSort},x.createElement("span",{"data-testid":"table__header__cell__title",className:$r.title},a),x.createElement(Ur,{required:s.isRequired,array:s.isList,object:s.isRelation,string:s.isString,int:s.isInt||s.isBigInt,float:s.isFloat||s.isDecimal,boolean:s.isBoolean,dateTime:s.isDateTime,enumerable:s.isEnum}),x.createElement("div",{className:$r.spacer}),x.createElement(zr,{"data-test-asc":i.sort.fieldId===s.id&&"asc"===i.sort.order,"data-test-desc":i.sort.fieldId===s.id&&"desc"===i.sort.order,className:S($r.sortIndicator,{[$r.visible]:i.sort.fieldId===s.id,[$r.asc]:"asc"===i.sort.order,[$r.desc]:"desc"===i.sort.order})}))}}var Wr=_(Jr);class Kr extends x.Component{render(){return x.createElement("div",{"data-testid":"loading-overlay",className:Ir},"Fetching rows in this table...")}}const Gr=32;class Qr extends x.Component{constructor(e){super(e),this.table=x.createRef(),this.recordOrder=new Map,this.reactionDisposers=[],this.focus=()=>{var e;null==(e=this.table.current)||e.focus()},this.refresh=()=>{this.loadGridDataDebounced()},this.selectRows=(e=[])=>{this.gridApi.deselectAll(),e.forEach((e=>{var t,s;null==(s=null==(t=this.gridApi)?void 0:t.getRowNode(e))||s.setSelected(!0)}))},this.deleteSelectedRows=()=>{var e;const t=null==(e=Tt.activeTab)?void 0:e.sessionId;if(!t)return;this.gridApi.getSelectedNodes().map((e=>at.get(e.id))).filter((e=>!!e)).forEach((e=>xs.add({type:"delete",recordId:e.id,sessionId:t,value:{}})))},this.getDimensions=()=>{var e;return null==(e=this.table.current)?void 0:e.getBoundingClientRect()},this.resizeRowForRelation=(e,t)=>{if(!t.typeAsModel)return!1;const s=this.getDimensions();if(!s)return;const i=2*Gr,a=64+(s.height-4*Gr-64-15-36)+15+36,n=64+i+15+36,r=64+Gr*(t.typeAsModel.count||0)+15+36,l=Math.min(Math.max(n,r),a);e.setRowHeight(Gr+l),this.gridApi.onRowHeightChanged(),this.gridApi.ensureNodeVisible(e,"top")},this.loadGridData=async()=>{var e;const t=null==(e=Tt.activeTab)?void 0:e.session;t&&t.isScript&&(await t.script.run(),0!==t.script.model.count||0!==t.script.recordIds.length||this.gridApi.showNoRowsOverlay())},this.configureGridColumns=()=>{var e;const t=null==(e=Tt.activeTab)?void 0:e.session;t&&t.isScript&&(this.gridApi.stopEditing(),this.gridApi.setColumnDefs([{colId:"checkbox",headerName:"",editable:!1,resizable:!1,sortable:!1,hide:V.readonly,pinned:!0,suppressMovable:!0,maxWidth:32,checkboxSelection:!0,headerCheckboxSelection:!0},...t.script.model.fields.map((e=>{const s=!(e.isScalarListTwoWayMNRelation||V.readonly&&!e.isRelation);return{colId:e.id,headerName:e.name,editable:s,resizable:!0,tooltipValueGetter:s?void 0:()=>"This field is not editable",sortable:!0,hide:!t.script.fieldIds.includes(e.id),valueGetter:t=>t.data.value[e.name],valueSetter:t=>this.handleCellValueChanged(t,e),comparator:(e,t,s,i,a)=>(this.recordOrder.get(s.id)-this.recordOrder.get(i.id)||0)*(a?-1:1),headerComponent:"TableCellHeader",headerComponentParams:{field:e,script:t.script},cellRenderer:"TableCellRenderer",cellRendererParams:{field:e,resizeRowForRelation:this.resizeRowForRelation},cellClassRules:{[Ba]:()=>!0,[Ha]:({data:t})=>t.dirtyFieldNames.includes(e.name),[Za]:({data:t})=>!!t.invalidFields.find((t=>t.field.id===e.id)),[qa]:({data:t})=>void 0===t.value[e.name]||null===t.value[e.name]},cellEditor:"TableCellEditor",cellEditorParams:{field:e,sessionId:t.id,getTableDimensions:this.getDimensions,resizeRowForRelation:this.resizeRowForRelation}}}))]),this.gridApi.ensureColumnVisible(t.script.model.fieldIds[0]))},this.configureReactions=()=>{this.disposeReactions();let e=v((()=>{var e;return[Tt.activeTab,null==(e=Tt.activeTab)?void 0:e.session]})).observe((()=>{this.configureGridColumns(),this.loadGridDataDebounced()}));this.reactionDisposers.push(e),e=v((()=>{var e,t,s,i,a;return(null==(t=null==(e=Tt.activeTab)?void 0:e.session)?void 0:t.isScript)?null==(a=null==(i=null==(s=Tt.activeTab)?void 0:s.session)?void 0:i.script)?void 0:a.fields:[]})).observe((({oldValue:e=[],newValue:t=[]})=>{var s,i;if(!(null==(i=null==(s=Tt.activeTab)?void 0:s.session)?void 0:i.isScript))return;let a=[],n=[];const r=new Map;e.forEach((e=>r.set(e.id,e)));const l=new Map;t.forEach((e=>l.set(e.id,e))),l.forEach(((e,t)=>{r.has(t)||a.push(e),r.delete(t)})),r.forEach((e=>n.push(e))),a.forEach((e=>this.columnApi.setColumnVisible(e.id,!0))),n.forEach((e=>this.columnApi.setColumnVisible(e.id,!1)))})),this.reactionDisposers.push(e),e=v((()=>{var e,t,s,i,a;return(null==(t=null==(e=Tt.activeTab)?void 0:e.session)?void 0:t.isScript)?null==(a=null==(i=null==(s=Tt.activeTab)?void 0:s.session)?void 0:i.script)?void 0:a.records:[]})).observe((({oldValue:e=[],newValue:t=[]})=>{var s,i,a,n,r,l;if(!(null==(i=null==(s=Tt.activeTab)?void 0:s.session)?void 0:i.isScript))return;const o=[],d=[],h=[],p=new Map;e.forEach((e=>p.set(e.id,e)));const u=new Map;t.forEach((e=>u.set(e.id,e)));let m=0;this.recordOrder.clear(),u.forEach((e=>{this.recordOrder.set(e.id,m++),p.has(e.id)&&this.gridApi.getRowNode(e.id)?d.push(e):o.push(e),p.delete(e.id)})),p.forEach((e=>{this.gridApi.getRowNode(e.id)&&h.push(e)}));c.exports.findLast(o,(e=>!1===e.isCommitted))&&(this.gridApi.deselectAll(),this.gridApi.ensureIndexVisible(0),this.gridApi.setFocusedCell(Tt.activeTab.session.script.uncommittedRecords.length-1,c.exports.last(o).model.fieldIds[0])),this.gridApi.setSortModel(null),this.gridApi.applyTransaction({add:o,update:d,remove:h}),this.gridApi.setSortModel([{colId:null==(n=null==(a=Tt.activeTab)?void 0:a.session)?void 0:n.script.fieldIds[0],sort:null==(l=null==(r=Tt.activeTab)?void 0:r.session)?void 0:l.script.sort.order}])}),!0),this.reactionDisposers.push(e)},this.disposeReactions=()=>{this.reactionDisposers.forEach((e=>e())),this.reactionDisposers=[]},this.handleGridReady=async e=>{this.columnApi=e.columnApi,this.gridApi=e.api,this.configureReactions(),this.configureGridColumns(),this.loadGridDataDebounced()},this.handleScroll=async e=>{var t,s;if("horizontal"===e.direction)return;const i=null==(s=null==(t=Tt.activeTab)?void 0:t.session)?void 0:s.script;if(i.recordIds.length>=(i.model.count||0))return;const a=this.props.height;!i.running&&e.top+a>=32*(i.pagination.take-20)&&i.loadMore()},this.handleSelectionChanged=e=>{var t;null==(t=Tt.activeTab)||t.session.selection.table.update({selectedRecordIds:e.api.getSelectedRows().map((e=>e.id))})},this.handleCellEditingStopped=e=>{const t=this.gridApi.getFocusedCell(),s=e;if(!t||!s)return;if(t.rowIndex===s.rowIndex&&t.column.getColId()!==s.column.getColId())return;if(t.rowIndex===s.rowIndex)return;const i=pe.get(t.column.getColId()),a=pe.get(e.column.getColId());(null==a?void 0:a.isRelation)&&(null==i?void 0:i.isRelation)&&this.gridApi.startEditingCell({rowIndex:t.rowIndex,colKey:t.column.getColId()})},this.handleCellValueChanged=(e,t)=>{var s,i,a,n;const{oldValue:r,newValue:l,node:{data:o}}=e,d=o,h=null==(s=Tt.activeTab)?void 0:s.sessionId;if(!h)return!1;if(c.exports.isEqual(r,l))return!1;if(!(null==(a=null==(i=Tt.activeTab)?void 0:i.session)?void 0:a.isScript))return!1;if(Tt.activeTab.session.script.update({frozen:!1}),xs.add({type:"update",recordId:d.id,sessionId:h,value:{[t.name]:l}}),t.isRelation&&!t.isList){if(!t.typeAsModel)return!1;const e=t.relationFromFieldNames,s=t.relationToFieldNames;let i;i=null==l?null:at.get(Ie(t.typeAsModel.id,l)),xs.add({type:"update",sessionId:h,recordId:null!=(n=d.id)?n:null,value:e.reduce(((e,t,a)=>{var n;return e[t]=null!=(n=null==i?void 0:i.value[s[a]])?n:null,e}),{})})}if(t.isPartOfRelation&&t.relationItIsPartOf){if(!t.relationItIsPartOf.typeAsModel)return!1;const e=t.relationItIsPartOf.relationFromFieldNames,s=t.relationItIsPartOf.relationToFieldNames;xs.add({type:"update",sessionId:h,recordId:d.id,value:{[t.relationItIsPartOf.name]:null==l?null:s.reduce(((t,s,i)=>(t[s]=d.value[e[i]],t)),{})}})}return!0},this.loadGridDataDebounced=c.exports.debounce(this.loadGridData,300,{leading:!1,trailing:!0}),this.handleScrollThrottled=c.exports.throttle(this.handleScroll,100,{leading:!0,trailing:!0})}componentWillUnmount(){this.disposeReactions()}copyToClipboard(){const e=this.gridApi.getFocusedCell(),t=this.gridApi.getDisplayedRowAtIndex(e.rowIndex),s=this.gridApi.getValue(e.column,t);if("object"!=typeof s||null===s)if(s)navigator.clipboard.writeText(s);else{const e=this.gridApi.getSelectedRows().map((e=>y(e.value))),t=JSON.stringify(e);if(0===e.length)return;navigator.clipboard.writeText(t)}}render(){var e;const t=null==(e=Tt.activeTab)?void 0:e.session;return x.createElement(x.Fragment,null,x.createElement("div",{ref:this.table,className:S(ja,"ag-theme-main")},x.createElement(Va,null),x.createElement(M,{rowSelection:"multiple",rowDeselection:!0,suppressRowClickSelection:!0,frameworkComponents:{TableLoadingOverlay:Kr,TableEmptyOverlay:Cr,TableCellHeader:Wr,TableCellRenderer:yr,TableCellEditor:Dr},rowClassRules:{[Fa]:e=>!!t.script.uncommittedRecords.length&&e.node.id===t.script.recordIds[t.script.uncommittedRecords.length]},loadingOverlayComponent:"TableLoadingOverlay",noRowsOverlayComponent:"TableEmptyOverlay",suppressColumnVirtualisation:!!window.Cypress,getRowNodeId:e=>e.id,onGridReady:this.handleGridReady,onBodyScroll:this.handleScrollThrottled,onSelectionChanged:this.handleSelectionChanged,onCellEditingStopped:this.handleCellEditingStopped})),x.createElement(si,{keys:"mod+c",target:this.table,preventDefault:!1,stopPropagation:!1,onMatch:e=>{e.preventDefault(),e.stopPropagation(),this.copyToClipboard()}}),x.createElement(si,{keys:"mod+up",target:this.table,preventDefault:!1,stopPropagation:!1,onMatch:e=>{this.gridApi.getEditingCells().length>0||(e.preventDefault(),e.stopPropagation(),this.gridApi.setFocusedCell(0,this.gridApi.getFocusedCell().column))}}),x.createElement(si,{keys:"mod+down",target:this.table,preventDefault:!1,stopPropagation:!1,onMatch:e=>{this.gridApi.getEditingCells().length>0||(e.preventDefault(),e.stopPropagation(),this.gridApi.setFocusedCell(Tt.activeTab.session.script.recordIds.length-1,this.gridApi.getFocusedCell().column))}}),x.createElement(si,{keys:"mod+left",target:this.table,preventDefault:!1,stopPropagation:!1,onMatch:e=>{this.gridApi.getEditingCells().length>0||(e.preventDefault(),e.stopPropagation(),this.gridApi.setFocusedCell(this.gridApi.getFocusedCell().rowIndex,this.columnApi.getColumnState()[1].colId))}}),x.createElement(si,{keys:"mod+right",target:this.table,preventDefault:!1,stopPropagation:!1,onMatch:e=>{if(this.gridApi.getEditingCells().length>0)return;e.preventDefault(),e.stopPropagation();const t=this.columnApi.getColumnState();this.gridApi.setFocusedCell(this.gridApi.getFocusedCell().rowIndex,t[t.length-1].colId)}}),x.createElement(si,{keys:"enter",target:this.table,preventDefault:!1,stopPropagation:!1,onMatch:e=>{const t=this.gridApi.getFocusedCell();if("checkbox"===t.column.getColId()){e.preventDefault(),e.stopPropagation();const s=this.gridApi.getRowNode(Tt.activeTab.session.script.recordIds[t.rowIndex]),i=this.gridApi.getSelectedNodes().find((e=>e.id===s.id));null==s||s.setSelected(!i)}}}))}}var Yr="_stickyItems_1qvgu_1",Xr="_outer_1qvgu_8";const el=x.createContext({});class tl extends x.PureComponent{constructor(e){super(e),this.list=x.createRef(),this.lastScrollTop=0,this.isLoading=!1,this.handleScroll=()=>{const e=this.list.current._outerRef;e.scrollTop!==this.lastScrollTop&&(this.lastScrollTop=e.scrollTop,this.handleHorizontalScrollThrottled())},this.handleHorizontalScroll=()=>{const{itemSize:e,onLoad:t}=this.props;if(this.isLoading||!t)return;const s=this.list.current._outerRef;s.scrollHeight-(s.scrollTop+s.clientHeight)<15*e&&(this.isLoading=!0,t())},this.handleHorizontalScrollThrottled=c.exports.throttle(this.handleHorizontalScroll,100,{leading:!0,trailing:!0})}componentDidUpdate(){this.isLoading=!1}render(){const{className:e,outerElementRef:t,innerElementRef:s,height:i,width:a,itemCount:n,itemKey:r,itemSize:l,itemData:o,itemRenderer:d,stickyIndices:c,stickyItemsClassName:h,overscan:p}=this.props;return x.createElement(el.Provider,{value:{stickyIndices:c,stickyItemsClassName:h,itemSize:l,itemData:o,itemKey:r,itemRenderer:d}},x.createElement("div",{"data-testid":"infinite-list",className:e,onScroll:this.handleScroll},x.createElement(k,{ref:this.list,outerRef:t,innerRef:s,height:i,width:a,itemCount:n,itemSize:l,itemKey:r,itemData:o,overscanCount:p,outerElementType:il,innerElementType:sl},d)))}}tl.defaultProps={stickyIndices:[],stickyItemsClassName:"",overscan:10};const sl=x.forwardRef(((e,t)=>{var s=e,{children:i}=s,a=d(s,["children"]);return x.createElement(el.Consumer,null,(({stickyItemsClassName:e,stickyIndices:s=[],itemSize:n,itemData:r,itemKey:d,itemRenderer:c})=>x.createElement("div",l({ref:t},a),x.createElement("div",{className:S(Yr,e)},s.map((e=>x.createElement(c,{key:d(e),index:e,style:{display:"flex",position:"absolute",top:e*n,left:0,width:"100%",height:n},data:o(l({},r),{sticky:!0})})))),x.Children.map(i,((e,t)=>s.includes(t)?null:e)))))})),il=x.forwardRef(((e,t)=>{var s=e,{children:i,className:a}=s,n=d(s,["children","className"]);return x.createElement("div",l({ref:t,tabIndex:1,className:S(Xr,a)},n),i)}));var al=Object.defineProperty,nl=Object.getOwnPropertyDescriptor,rl=(e,t,s,i)=>{for(var a,n=i>1?void 0:i?nl(t,s):t,r=e.length-1;r>=0;r--)(a=e[r])&&(n=(i?a(t,s,n):a(n))||n);return i&&n&&al(t,s,n),n};class ll{constructor(e){w((()=>{this.sessionId=e.sessionId}))}get session(){return He.get(this.sessionId)}}rl([h],ll.prototype,"sessionId",2),rl([v],ll.prototype,"session",1);const ol=new ll({sessionId:"model-list-session"}),dl=N.exports.createContext(ol),cl=dl.Provider;dl.Consumer;const hl=dl,pl=e=>(e.contextType=hl,e);var ul="_container_wwbam_1";var ml="_container_fkswg_1",gl="_one_fkswg_21",vl="_two_fkswg_22",fl="_three_fkswg_23";var yl=_((({className:e,size:t=3}={})=>x.createElement("div",{className:S(ml,e)},x.createElement("div",{className:gl,style:{height:t,width:t}}),x.createElement("div",{className:vl,style:{height:t,width:t}}),x.createElement("div",{className:fl,style:{height:t,width:t}}))));class Il extends x.PureComponent{constructor(){super(...arguments),this.clickStartCoords={x:0,y:0},this.startClick=e=>{this.clickStartCoords={x:e.clientX,y:e.clientY}},this.finishClick=e=>{if(this.props.onClickButNotDrag&&Math.abs(this.clickStartCoords.x-e.clientX)<5&&Math.abs(this.clickStartCoords.y-e.clientY)<5)return this.props.onClickButNotDrag(e);this.props.onClick&&this.props.onClick(e)}}render(){const e=this.props,{onClickButNotDrag:t,children:s}=e,i=d(e,["onClickButNotDrag","children"]);return x.createElement("div",o(l({},i),{onMouseDown:this.startClick,onMouseUp:this.finishClick,onClick:void 0}),s)}}function Cl(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"m2.314892,2.715466c0,-2.097726 2.320696,-3.364698 4.085258,-2.230334l13.864359,8.912782c1.89413,1.21769 1.89413,3.986482 0,5.204172l-13.864359,8.912782c-1.764597,1.134364 -4.085258,-0.132608 -4.085258,-2.230334l0,-18.569068z"}))}const wl=(e,{maxLength:t=100}={})=>e.length<=t?e:`${e.slice(0,t-1)}…`,bl=(e,{maxLength:t}={maxLength:100})=>{if(void 0===e)return"undefined";if(null===e)return"null";if("string"==typeof e)return`"${wl(e,{maxLength:t-2})}"`;if("number"==typeof e||"boolean"==typeof e)return wl(`${e}`,{maxLength:t});const s=e=>"string"==typeof e?`"${e}"`:"number"==typeof e||"boolean"==typeof e?`${e}`:Array.isArray(e)?"[ … ]":"{ … }";if(Array.isArray(e)){t-="[  ]".length;const i=[];return e.some((e=>{const a=s(e),n=a.length+", ".length;return!(n<t)||(i.push(a),t-=n,!1)})),0===i.length?"[ … ]":i.length===e.length?`[ ${i.join(", ")} ]`:`[ ${i.join(", ")}, … ]`}const i=Object.keys(e);t-="{  }".length;const a=[];return i.some((i=>{const n=`${i}: ${s(e[i])}`,r=n.length+", ".length;return!(r<t)||(a.push(n),t-=r,!1)})),0===a.length?"{ … }":a.length===i.length?`{ ${a.join(", ")} }`:`{ ${a.join(", ")}, … }`};var El={container:"_container_oc259_1",meta:"_meta_oc259_9",fieldValue:"_fieldValue_oc259_14",active:"_active_oc259_19",expanded:"_expanded_oc259_28",expandButton:"_expandButton_oc259_28",loader:"_loader_oc259_51",icon:"_icon_oc259_65",fieldName:"_fieldName_oc259_68",fieldType:"_fieldType_oc259_74"};class _l extends x.PureComponent{constructor(){super(...arguments),this.handleToggleCollapse=e=>{const{path:t,isLoading:s,onToggleCollapse:i}=this.props;e.stopPropagation(),s||i(t)},this.handleExpand=e=>{const{isExpanded:t}=this.props;t||this.handleToggleCollapse(e)}}render(){const{path:e,isSelected:t,isExpanded:s,fieldName:i,modelName:a,value:n,isLoading:r,style:l}=this.props,o=e.split("::").length-1;return x.createElement("div",{tabIndex:1,"data-testid":"tree__array",className:S(El.container,{[El.expanded]:s,[El.active]:!s&&t}),style:l},x.createElement("div",{className:El.depthGutter,style:{width:16*o}}),x.createElement("div",{"data-testid":"expand-btn",className:El.expandButton,onClick:this.handleToggleCollapse},r&&x.createElement(yl,{size:3,className:El.loader}),!r&&n&&n.length>0&&x.createElement(Cl,null)),x.createElement("div",{className:S(El.meta,{[El.active]:s&&t}),onClick:this.handleToggleCollapse},x.createElement(Ur,{className:El.icon,array:!0,required:!0}),x.createElement("div",{className:El.fieldName},i,":"),x.createElement("div",{className:El.fieldType},a)),x.createElement(Il,{className:El.fieldValue,onClickButNotDrag:this.handleExpand},x.createElement(x.Fragment,null,`(${n?n.length:0}) `,r||0===(null==n?void 0:n.length)?"[ ]":bl(n,{maxLength:150}))))}}var xl=_(_l);class Sl extends x.PureComponent{constructor(){super(...arguments),this.handleToggleCollapse=e=>{const{path:t,isLoading:s,onToggleCollapse:i}=this.props;e.stopPropagation(),s||i(t)},this.handleExpand=e=>{const{isExpanded:t}=this.props;t||this.handleToggleCollapse(e)}}render(){const{path:e,isSelected:t,isExpanded:s,startIndex:i,endIndex:a,value:n,isLoading:r,style:l}=this.props,o=e.split("::").length-1;return x.createElement("div",{tabIndex:1,"data-testid":"tree__array",className:S(El.container,{[El.expanded]:s,[El.active]:!s&&t}),style:l},x.createElement("div",{className:El.depthGutter,style:{width:16*o}}),x.createElement("div",{"data-testid":"expand-btn",className:El.expandButton},r?x.createElement(yl,{size:3,className:El.loader}):x.createElement(Cl,null)),x.createElement("div",{className:S(El.meta,{[El.active]:s&&t}),onClick:this.handleToggleCollapse},x.createElement(Ur,{className:El.icon,array:!0,required:!0}),x.createElement("div",{className:El.fieldName},`[${i}-${a}]:`)),x.createElement(Il,{className:El.fieldValue,onClickButNotDrag:this.handleExpand},x.createElement(x.Fragment,null,`(${n&&n.length}) `,bl(n,{maxLength:150}))))}}var Nl=_(Sl);var Ll={container:"_container_1m4mj_1",meta:"_meta_1m4mj_8",fieldValue:"_fieldValue_1m4mj_14",active:"_active_1m4mj_20",icon:"_icon_1m4mj_36",fieldName:"_fieldName_1m4mj_39"};class Rl extends x.PureComponent{constructor(){super(...arguments),this.handleClick=e=>{const{path:t,onClick:s}=this.props;e&&e.stopPropagation(),s(t)}}render(){const{path:e,isSelected:t,fieldName:s,value:i,style:a}=this.props,n=e.split("::").length-1;return x.createElement("div",{"data-testid":"tree__enum",style:a,className:S(Ll.container,{[Ll.active]:t}),tabIndex:1,onClick:this.handleClick},x.createElement("div",{className:Ll.depthGutter,style:{width:16*n}}),x.createElement("div",{className:Ll.meta},x.createElement(Ur,{className:Ll.icon,enumerable:!0,required:!0}),s&&x.createElement("div",{className:Ll.fieldName},s,":")),x.createElement("div",{className:Ll.fieldValue},bl(i)))}}var Ml=_(Rl);var Ol={container:"_container_1rxo6_1",meta:"_meta_1rxo6_9",fieldValue:"_fieldValue_1rxo6_14",active:"_active_1rxo6_19",expanded:"_expanded_1rxo6_28",expandButton:"_expandButton_1rxo6_28",loader:"_loader_1rxo6_51",icon:"_icon_1rxo6_65",fieldName:"_fieldName_1rxo6_68",fieldType:"_fieldType_1rxo6_73"};class kl extends x.PureComponent{constructor(){super(...arguments),this.handleToggleCollapse=e=>{const{path:t,isLoading:s,onToggleCollapse:i}=this.props;e.stopPropagation(),s||i(t)},this.handleExpand=e=>{const{isExpanded:t}=this.props;t||this.handleToggleCollapse(e)}}render(){const{path:e,isSelected:t,isExpanded:s,fieldName:i,modelName:a,value:n,isLoading:r,style:l}=this.props,o=e.split("::").length-1;return x.createElement("div",{"data-testid":"tree__object","data-test-expanded":"true",style:l,className:S(Ol.container,{[Ol.expanded]:s,[Ol.active]:!s&&t}),tabIndex:1},x.createElement("div",{className:Ol.depthGutter,style:{width:16*o}}),x.createElement("div",{"data-testid":"expand-btn",className:Ol.expandButton,onClick:this.handleToggleCollapse},r&&x.createElement(yl,{size:3,className:Ol.loader}),!r&&n&&x.createElement(Cl,null)),x.createElement("div",{className:S(Ol.meta,{[Ol.active]:s&&t}),onClick:this.handleToggleCollapse},x.createElement(Ur,{className:Ol.icon,object:!0,required:!0}),i&&x.createElement("div",{className:Ol.fieldName},i,":"),x.createElement("div",{className:Ol.fieldType},a)),x.createElement(Il,{className:Ol.fieldValue,onClickButNotDrag:this.handleExpand},n?bl(n,{maxLength:150}):"null"))}}var Al=_(kl);var Dl={container:"_container_w0wc7_1",meta:"_meta_w0wc7_8",fieldValue:"_fieldValue_w0wc7_14",active:"_active_w0wc7_20",icon:"_icon_w0wc7_36",fieldName:"_fieldName_w0wc7_39"};class Tl extends x.PureComponent{constructor(){super(...arguments),this.handleClick=e=>{const{path:t,onClick:s}=this.props;e&&e.stopPropagation(),s(t)}}render(){const{path:e,isSelected:t,fieldName:s,value:i,isString:a,isInt:n,isFloat:r,isBoolean:l,isDateTime:o,style:d}=this.props,c=e.split("::").length-1;return x.createElement("div",{"data-testid":"tree__scalar",style:d,className:S(Dl.container,{[Dl.active]:t}),tabIndex:1,onClick:this.handleClick},x.createElement("div",{className:Dl.depthGutter,style:{width:16*c}}),x.createElement("div",{className:Dl.meta},((h=this.props).isString||h.isInt||h.isFloat||h.isBoolean||h.isDateTime)&&x.createElement(Ur,{className:Dl.icon,required:!0,string:a,int:n,float:r,boolean:l,dateTime:o}),x.createElement("div",{className:Dl.fieldName},s,":")),x.createElement("div",{className:Dl.fieldValue},bl(i)));var h}}var Pl=_(Tl);class Vl extends x.PureComponent{constructor(){super(...arguments),this.isArraySlice=e=>null===e.record&&Array.isArray(e.arraySliceIndices)&&Array.isArray(e.value),this.handleToggleCollapse=e=>{const{selection:t}=this.context.session;this.handleSelect(e),t.tree.isExpanded(e)?t.tree.collapse():t.tree.expand()},this.handleSelect=e=>{const{selection:t}=this.context.session;t.tree.select(e)}}render(){var e,t,s,i,a,n,r,l,o,d,c,h,p;const{selection:u}=this.context.session,{style:m,index:g,data:{pathsToRender:v,recordIds:f}}=this.props,y=v[g],I=_e(y,f),C=u.tree.activePath,w=u.tree.isExpanded(y);return this.isArraySlice(I)?x.createElement(Nl,{path:y,isSelected:y===C,isExpanded:w,startIndex:I.arraySliceIndices[0],endIndex:I.arraySliceIndices[1],value:I.value,isLoading:!I.value,style:m,onToggleCollapse:this.handleToggleCollapse}):(null==(e=I.field)?void 0:e.isList)||Array.isArray(I.value)?x.createElement(xl,{path:y,isSelected:y===C,isExpanded:w,fieldName:I.field.name,modelName:I.field.typeAsLabel,value:I.value,isLoading:!I.value,style:m,onToggleCollapse:this.handleToggleCollapse}):(null==(t=I.field)?void 0:t.isRelation)||I.record?x.createElement(Al,{path:y,isSelected:y===C,isExpanded:w,fieldName:null==(s=I.field)?void 0:s.name,modelName:I.model.name,value:I.value,isLoading:null===(null==(i=I.record)?void 0:i.value),style:m,onToggleCollapse:this.handleToggleCollapse}):(null==(a=I.field)?void 0:a.isEnum)?x.createElement(Ml,{path:y,isSelected:y===C,fieldName:null==(n=I.field)?void 0:n.name,value:I.value,style:m,onClick:this.handleSelect}):x.createElement(Pl,{path:y,isSelected:y===C,fieldName:(null==(r=I.field)?void 0:r.name)||`${I.index}`,value:I.value,isString:(null==(l=I.field)?void 0:l.isString)||!1,isInt:(null==(o=I.field)?void 0:o.isInt)||!1,isFloat:(null==(d=I.field)?void 0:d.isFloat)||(null==(c=I.field)?void 0:c.isDecimal)||!1,isBoolean:(null==(h=I.field)?void 0:h.isBoolean)||!1,isDateTime:(null==(p=I.field)?void 0:p.isDateTime)||!1,style:m,onClick:this.handleSelect})}}var jl=_(pl(Vl));class Fl extends x.PureComponent{constructor(e){super(e),this.tree=x.createRef(),this.refresh=async()=>{const{session:e}=this.context;await e.script.run()},this._scrollIntoView=()=>{const{height:e}=this.props,t=this.getActivePathIndex(),s=this.tree.current,i=23*t,a=i+23;i<=s.scrollTop&&s.scrollTo({top:i}),a>=s.scrollTop+e&&s.scrollTo({top:a-e})},this.getActivePathIndex=()=>{const{session:e}=this.context;return e.selection.tree.selectionOrder.findIndex((t=>t===e.selection.tree.activePath))},this.handleLoadMore=()=>{const{session:e}=this.context;e.script.loadMore(),e.selection.tree.setSelectionOrder()},this.scrollIntoView=c.exports.throttle(this._scrollIntoView,30,{leading:!0,trailing:!0})}focus(){var e;null==(e=this.tree.current)||e.focus()}componentDidMount(){this.context.session.selection.tree.setSelectionOrder()}render(){const{session:e}=this.context,{height:t,width:s,recordIds:i}=this.props,{tree:a}=e.selection,n=a.selectionOrder;return x.createElement(x.Fragment,null,x.createElement(tl,{outerElementRef:this.tree,className:ul,height:t-20,width:s-20,itemCount:n.length,itemSize:23,itemData:{pathsToRender:n,recordIds:i},itemKey:e=>n[e],itemRenderer:jl,onLoad:this.handleLoadMore}),x.createElement(si,{keys:"up",target:this.tree,onMatch:e=>{a.move(1,"up"),this.scrollIntoView()}}),x.createElement(si,{keys:"down",target:this.tree,onMatch:e=>{a.move(1,"down"),this.scrollIntoView()}}),x.createElement(si,{keys:"left",target:this.tree,onMatch:e=>{a.isExpanded(a.activePath)?a.collapse():a.jumpToParent(),this.scrollIntoView()}}),x.createElement(si,{keys:"right",target:this.tree,onMatch:e=>{a.expand(),this.scrollIntoView()}}),x.createElement(si,{keys:"space",target:this.tree,onMatch:e=>{a.expand(),this.scrollIntoView()}}))}}var Bl=_(pl(Fl));var Hl="_container_ks368_1";class Zl extends x.PureComponent{constructor(){super(...arguments),this.table=x.createRef(),this.tree=x.createRef()}scrollIntoView(){this.table.current.scrollIntoView()}focus(){var e,t;const{session:s}=this.context;"table"===s.script.viewMode&&(null==(e=this.table.current)||e.focus()),"tree"===s.script.viewMode&&(null==(t=this.tree.current)||t.focus())}refresh(){var e,t;const{session:s}=this.context;"table"===s.script.viewMode&&(null==(e=this.table.current)||e.refresh()),"tree"===s.script.viewMode&&(null==(t=this.tree.current)||t.refresh())}selectRows(e){var t;const{session:s}=this.context;"table"===s.script.viewMode&&(null==(t=this.table.current)||t.selectRows(e))}deleteSelectedRows(){var e;const{session:t}=this.context;"table"===t.script.viewMode&&(null==(e=this.table.current)||e.deleteSelectedRows())}render(){const{sessionId:e,session:t}=this.context,{className:s,width:i,height:a}=this.props;return x.createElement("div",{className:S(Hl,s),"data-testid":"results"},"tree"===t.script.viewMode&&x.createElement(Bl,{ref:this.tree,key:e,width:i,height:a,recordIds:t.script.recordIds}),"table"===t.script.viewMode&&x.createElement(Qr,{ref:this.table,width:i,height:a}))}}var ql=_(pl(Zl));function Ul(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fill:"currentColor",d:"M8.53033 1.46948C7.62352 0.563232 6.37899 0.000732422 4.99687 0.000732422C2.23265 0.000732422 0 2.23823 0 5.00073C0 7.76323 2.23265 10.0007 4.99687 10.0007C7.32958 10.0007 9.27455 8.40698 9.83115 6.25073H8.53033C8.01751 7.70698 6.62914 8.75073 4.99687 8.75073C2.92683 8.75073 1.24453 7.06948 1.24453 5.00073C1.24453 2.93198 2.92683 1.25073 4.99687 1.25073C6.03502 1.25073 6.9606 1.68198 7.63602 2.36323L5.62226 4.37573H10V0.000732422L8.53033 1.46948Z"}))}var zl={container:"_container_3cx2j_1",header:"_header_3cx2j_7",separator:"_separator_3cx2j_15",refreshBtn:"_refreshBtn_3cx2j_22",spin:"_spin_3cx2j_50",pendingActions:"_pendingActions_3cx2j_57"};function $l(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{d:"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),N.exports.createElement("path",{d:"M12 9V13",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),N.exports.createElement("path",{d:"M12 17H12.01",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}))}var Jl="_container_q775b_1",Wl="_header_q775b_12",Kl="_error_q775b_22",Gl="_closeButton_q775b_34",Ql="_footer_q775b_40";class Yl extends x.Component{componentDidMount(){document.getElementById("dialog-root").style.pointerEvents="initial"}componentWillUnmount(){document.getElementById("dialog-root").style.pointerEvents="none"}render(){const{dataTestId:e,className:t,error:s,title:i,primaryButton:a,secondaryButton:n,children:r,onClose:l}=this.props;return x.createElement(Oi,{"data-testid":null!=e?e:"dialog",className:S(Jl,t),domElementId:"dialog-root",darken:!0,onClickOutside:l},x.createElement(x.Fragment,null,i&&x.createElement("div",{className:S(Wl,{[Kl]:s})},s&&x.createElement($l,null),i),r,(a||n)&&x.createElement("div",{className:Ql},a,n),l&&x.createElement(ti,{className:Gl,onClick:l},x.createElement(ni,null))))}}var Xl=_(Yl);var eo="_container_sfoat_1",to="_content_sfoat_10";class so extends x.PureComponent{constructor(){super(...arguments),this.handleClose=()=>{this.props.onClose()},this.handleDelete=()=>{this.props.onDeleteRecord(),this.handleCommit(),this.props.onClose()},this.handleCommit=async()=>{const{onSuccess:e,onFailure:t}=this.props;try{await xs.commit(),null==e||e()}catch(s){null==t||t()}os.send({command:"action_commit",commandDetails:{pending_action_count:xs.actions.length}})}}render(){return x.createElement(x.Fragment,null,x.createElement(Xl,{error:!0,title:"Confirm record deletion",className:eo,onClose:this.handleClose,primaryButton:x.createElement(Qs,{red:!0,dataTestId:"delete-record-btn-confirm",onClick:this.handleDelete},"Delete")},x.createElement("p",{className:to},"You are about to delete the selected record permanently from the dataset.")),x.createElement(si,{keys:"esc",onMatch:this.handleClose}))}}var io=_(so);class ao extends x.PureComponent{constructor(){super(...arguments),this.results=x.createRef(),this.state={isDeletePromptOpen:!1},this.handleCreateRecord=()=>{var e,t;null==(t=null==(e=Tt.activeTab)?void 0:e.session)||t.createUncommittedRecord(),os.send({command:"record_create",commandDetails:{}})},this.handleOpenDeletePrompt=()=>{this.setState({isDeletePromptOpen:!0})},this.handleDeleteRecords=()=>{this.results.current.deleteSelectedRows(),this.results.current.selectRows([]),this.results.current.focus()},this.handleSuccess=()=>{this.results.current.selectRows([]),this.results.current.focus(),this.results.current.refresh()}}render(){var e;const{isDeletePromptOpen:t}=this.state,s=null==(e=Tt.activeTab)?void 0:e.session;if(!s||!(null==s?void 0:s.isScript))return null;const i="tree"===s.script.viewMode||s.script.model.hasScalarListTwoWayMNRelation;return x.createElement("div",{className:zl.container,"data-testid":"databrowser"},x.createElement("div",{className:zl.header},x.createElement(Qs,{dataTestId:"refresh-btn",className:S(zl.refreshBtn,{[zl.spin]:s.script.running}),disabled:s.script.running,onClick:()=>s.script.run()},x.createElement(Ul,null)),x.createElement(Gi,null),x.createElement(zi,null),x.createElement(Wi,null),x.createElement("div",{className:zl.separator}),!V.readonly&&x.createElement(Qs,{"data-testid":"create-record-btn",onClick:this.handleCreateRecord,disabled:i},"Add record"),s.selection.table.selectedRecordIds.length>0&&x.createElement(x.Fragment,null,x.createElement(Qs,{"data-testid":"delete-record-btn",red:!0,onClick:()=>this.handleOpenDeletePrompt(),disabled:s.script.model.hasScalarListTwoWayMNRelation},x.createElement(x.Fragment,null,"Delete ",s.selection.table.selectedRecordIds.length," ","record",s.selection.table.selectedRecordIds.length>1?"s":"")),t&&x.createElement(io,{onClose:()=>this.setState({isDeletePromptOpen:!1}),onDeleteRecord:this.handleDeleteRecords,onSuccess:this.handleSuccess})),x.createElement(Xi,{className:zl.pendingActions,onSuccess:this.handleSuccess})),x.createElement(ql,{ref:this.results,className:zl.results,width:window.innerWidth,height:window.innerHeight-36-44}))}}var no=_(ao);var ro={container:"_container_1pcqt_1",content:"_content_1pcqt_10",description:"_description_1pcqt_20",reportBtn:"_reportBtn_1pcqt_25",detailsBtn:"_detailsBtn_1pcqt_36",open:"_open_1pcqt_49",dump:"_dump_1pcqt_52"};class lo extends x.PureComponent{constructor(){super(...arguments),this.state={isDetailsOpen:!1},this.handleToggleDetails=()=>{this.setState((e=>({isDetailsOpen:!e.isDetailsOpen})))},this.handleDismiss=()=>{ye.updateError({visible:!1})}}render(){const{isDetailsOpen:e}=this.state;return x.createElement(Xl,{dataTestId:"client-error-dialog",className:ro.container,error:!0,title:"Prisma Client Error",primaryButton:x.createElement(Qs,{dataTestId:"dismiss-btn",className:ro.action,onClick:this.handleDismiss},"Dismiss"),secondaryButton:x.createElement("a",{"data-testid":"error-docs-btn",href:"https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/error-reference#query-engine",target:"_blank",rel:"noreferrer noopener"},"Error Documentation")},x.createElement("div",{className:ro.content,"data-testid":"client-error"},x.createElement("p",{className:ro.description},us.description),x.createElement(Qs,{ghost:!0,className:S(ro.detailsBtn,{[ro.open]:e}),onClick:this.handleToggleDetails},x.createElement(x.Fragment,null,x.createElement(zr,null),e?"Hide":"Show"," details")),e&&x.createElement("pre",{className:ro.dump},us.dump)))}}var oo=_(lo);class co extends x.PureComponent{constructor(){super(...arguments),this.state={isDetailsOpen:!1,isSendingReport:!1,didFailErrorReport:!1,errorReportId:null,previousError:ye.previousError},this.handleToggleDetails=()=>{this.setState((e=>({isDetailsOpen:!e.isDetailsOpen})))},this.handleDiscard=()=>{xs.discard(),setTimeout((()=>{window.transport.request({channel:"project",action:"close",payload:{data:null}}),window.location.reload()}),500)},this.handleRestart=()=>{ye.setPreviousError({type:us.type}),window.location.reload()},this.handleHardRestart=()=>{window.indexedDB.deleteDatabase("Prisma Studio"),ye.setPreviousError({type:null}),window.location.reload()}}render(){const{isDetailsOpen:e}=this.state,t=window.location.origin.includes("localhost")?"https://github.com/prisma/studio/issues/new?template=prisma-cli-studio-bug-report.yml":"https://github.com/prisma/studio/issues/new?template=data-browser-bug-report.yml";return x.createElement(Xl,{"data-testid":"fatal-error-dialog",className:ro.container,error:!0,title:"Fatal Error",primaryButton:this.state.previousError.type?x.createElement(Qs,{dataTestId:"reopen-btn-hard",className:ro.action,onClick:this.handleHardRestart,red:!0},"Force reload Studio"):x.createElement(Qs,{dataTestId:"reopen-btn",className:ro.action,onClick:this.handleRestart},"Reload Studio"),secondaryButton:this.state.previousError.type?void 0:x.createElement(Qs,{dataTestId:"dismiss-btn",ghost:!0,className:S(ro.action,ro.discardBtn),onClick:this.handleDiscard},x.createElement(x.Fragment,null,"Discard all unsaved changes and close Studio"))},x.createElement("div",{className:ro.content},x.createElement("p",{className:ro.description},this.state.previousError.type?"A persistent non-recoverable error has occurred. Force reload to clear temporary data and reopen Studio.\n            Consider reporting this error to us by opening an issue on GitHub and attaching the error details.":"A non-recoverable error has occurred. Reload Studio to continue.\n            Consider reporting this error to us by opening an issue on GitHub\n            and attaching the error details."),x.createElement("a",{href:t,target:"_blank",rel:"noreferrer noopener",className:ro.reportBtn},"Report error in a new GitHub issue"),x.createElement(Qs,{ghost:!0,className:S(ro.detailsBtn,{[ro.open]:e}),onClick:this.handleToggleDetails},x.createElement(x.Fragment,null,x.createElement(zr,null),e?"Hide":"Show"," details")),e&&x.createElement("pre",{className:ro.dump},us.dump)))}}var ho=_(co);class po extends x.PureComponent{constructor(){super(...arguments),this.handleRestart=()=>{window.location.reload()}}render(){return x.createElement(Xl,{className:ro.container,error:!0,title:"Prisma Schema Changed",primaryButton:x.createElement(Qs,{className:ro.action,onClick:this.handleRestart},"Reload"),secondaryButton:x.createElement("a",{href:"https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/error-reference#query-engine",target:"_blank",rel:"noreferrer noopener"},"Error Documentation")},x.createElement("div",{className:ro.content},x.createElement("p",{className:ro.description},us.description)))}}var uo=_(po);var mo="_container_ud69p_1";class go extends x.Component{render(){const e=this.props,{children:t,className:s,style:i}=e,a=d(e,["children","className","style"]);return x.createElement("div",l({className:S(mo,s),style:i},a),t)}}var vo="_container_serom_1";var fo=_((({type:e})=>x.createElement("div",{"data-testid":"shortcuts-key",className:vo},e)));var yo="_container_o8wgd_1",Io="_content_o8wgd_17",Co="_section_o8wgd_22",wo="_sectionName_o8wgd_28",bo="_row_o8wgd_34",Eo="_name_o8wgd_39",_o="_keys_o8wgd_43",xo="_separator_o8wgd_49";class So extends x.PureComponent{constructor(){super(...arguments),this.handleClose=()=>{this.props.onClose()}}render(){const e={sections:[{name:"Results Table",shortcuts:[{name:"Move cell selection",mac:["←","→","↑","↓"],windowsAndLinux:["←","→","↑","↓"]},{name:"Move horizontally",mac:["tab","shift+tab"],windowsAndLinux:["tab","shift+tab"]},{name:"Move vertically",mac:["enter","shift+enter"],windowsAndLinux:["enter","shift+enter"]},{name:"Edit selected cell",mac:["enter"],windowsAndLinux:["enter"]},{name:"Jump to last cell in row",mac:["⌘+→"],windowsAndLinux:["ctrl+→"]},{name:"Jump to first cell in row",mac:["⌘+←"],windowsAndLinux:["ctrl+←"]},{name:"Copy focused cell OR selected rows to clipboard",mac:["⌘+c"],windowsAndLinux:["ctrl+c"]},{name:"Delete selected rows",mac:["⌫"],windowsAndLinux:["del"]},{name:"Commit unsaved changes to Database",mac:["⌘+s"],windowsAndLinux:["ctrl+s"]}]},{name:"Global",shortcuts:[{name:"Show this cheatsheet",mac:["⌘+/"],windowsAndLinux:["ctrl+/"]}]}].filter((e=>!!e))},t=/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"mac":"windowsAndLinux";return x.createElement(x.Fragment,null,x.createElement(Xl,{className:yo,title:"Shortcuts",onClose:this.handleClose},x.createElement("div",{className:Io},e.sections.map((e=>x.createElement(go,{key:e.name,className:Co},x.createElement(x.Fragment,null,x.createElement("div",{className:wo},e.name),e.shortcuts.map((e=>x.createElement("div",{key:e.name,className:bo},x.createElement("div",{className:Eo},e.name),x.createElement("div",{className:_o},e[t].map(((e,t)=>x.createElement(x.Fragment,{key:t},t>0&&x.createElement("span",{className:xo},"/"),e.split("+").map((e=>x.createElement(fo,{key:e,type:e})))))))))))))))),x.createElement(si,{keys:"esc",onMatch:this.handleClose}))}}var No=_(So);var Lo={container:"_container_1z063_1",content:"_content_1z063_10",description:"_description_1z063_17"};class Ro extends x.Component{handleInstall(){js.install()}render(){const{onClose:e}=this.props;return x.createElement(Xl,{className:Lo.container,title:"Updates ready",primaryButton:x.createElement(Qs,{green:!0,disabled:js.isInstalling,onClick:this.handleInstall},"Install and restart"),secondaryButton:x.createElement(Qs,{ghost:!0,className:Lo.laterBtn,onClick:e},"Install Later")},x.createElement("div",{className:Lo.content},x.createElement("p",{className:Lo.description},"New updates have been downloaded and are ready to be installed. Doing this will restart Studio.",x.createElement("br",null),x.createElement("br",null),"Your current session will be saved and restored after the installation.")))}}var Mo=_(Ro);function Oo(e){return N.exports.createElement("svg",Object.assign({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},e),N.exports.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.286 15.9606C19.2271 15.6362 19.2669 15.3017 19.4 15C19.5267 14.7042 19.7373 14.452 20.0055 14.2743C20.2738 14.0966 20.5882 14.0013 20.91 13.9999H21.0001C21.5304 13.9999 22.0391 13.7893 22.4142 13.4142C22.7893 13.0391 23 12.5305 23 12C23 11.4695 22.7893 10.9609 22.4142 10.5858C22.0391 10.2107 21.5304 10.0001 21.0001 10.0001H20.83C20.5082 9.9987 20.1938 9.90341 19.9255 9.72576C19.6572 9.54797 19.4467 9.29579 19.32 9V8.92001C19.1869 8.61839 19.1471 8.28381 19.206 7.95942C19.2648 7.63503 19.4195 7.33569 19.65 7.1L19.71 7.04001C19.8959 6.85426 20.0435 6.63368 20.1441 6.39088C20.2448 6.14809 20.2966 5.88784 20.2966 5.62501C20.2966 5.36218 20.2448 5.10192 20.1441 4.85912C20.0435 4.61632 19.8959 4.39574 19.71 4.21001C19.5243 4.02405 19.3037 3.87653 19.0609 3.77588C18.8181 3.67523 18.5578 3.62343 18.295 3.62343C18.0321 3.62343 17.772 3.67523 17.5292 3.77588C17.2863 3.87653 17.0658 4.02405 16.88 4.21001L16.8201 4.27C16.5844 4.50055 16.285 4.65519 15.9605 4.714C15.6362 4.77282 15.3017 4.73311 15 4.6C14.7042 4.47324 14.452 4.26275 14.2743 3.99446C14.0966 3.72617 14.0013 3.41179 13.9999 3.09V3.00001C13.9999 2.46957 13.7893 1.96086 13.4142 1.58579C13.0391 1.21072 12.5305 1 12 1C11.4695 1 10.9609 1.21072 10.5858 1.58579C10.2107 1.96086 10.0001 2.46957 10.0001 3.00001V3.17C9.99869 3.49179 9.9034 3.80617 9.72575 4.07446C9.54796 4.34275 9.29579 4.55324 9 4.68H8.92C8.61838 4.81311 8.2838 4.85282 7.95941 4.79401C7.63502 4.73519 7.33568 4.58054 7.1 4.35L7.04 4.29001C6.85425 4.10405 6.63368 3.95653 6.39088 3.85588C6.14808 3.75524 5.88784 3.70343 5.625 3.70343C5.36217 3.70343 5.10191 3.75524 4.85912 3.85588C4.61632 3.95653 4.39574 4.10405 4.21001 4.29001C4.02405 4.47576 3.87653 4.69633 3.77588 4.93912C3.67523 5.18191 3.62343 5.44218 3.62343 5.70501C3.62343 5.96784 3.67523 6.22808 3.77588 6.47088C3.87653 6.71368 4.02405 6.93426 4.21001 7.12001L4.27 7.18001C4.50054 7.41569 4.65519 7.71502 4.714 8.03941C4.77282 8.36382 4.73311 8.69838 4.6 9C4.48572 9.31078 4.2806 9.57987 4.01131 9.77251C3.74201 9.96515 3.42099 10.0723 3.09 10.08H3.00001C2.46957 10.08 1.96086 10.2907 1.58579 10.6658C1.21072 11.0408 1 11.5496 1 12.08C1 12.6105 1.21072 13.1191 1.58579 13.4942C1.96086 13.8693 2.46957 14.08 3.00001 14.08H3.17C3.49179 14.0813 3.80617 14.1766 4.07446 14.3543C4.34275 14.5319 4.55323 14.7842 4.68 15.08C4.81311 15.3817 4.85282 15.7162 4.79401 16.0406C4.73519 16.365 4.58054 16.6643 4.34999 16.9L4.29 16.9601C4.10405 17.1458 3.95653 17.3664 3.85588 17.6092C3.75524 17.8519 3.70343 18.1122 3.70343 18.3751C3.70343 18.6378 3.75524 18.8981 3.85588 19.1409C3.95653 19.3836 4.10405 19.6043 4.29 19.7901C4.47575 19.976 4.69633 20.1235 4.93911 20.2242C5.18191 20.3248 5.44217 20.3767 5.705 20.3767C5.96783 20.3767 6.22808 20.3248 6.47088 20.2242C6.71368 20.1235 6.93425 19.976 7.12 19.7901L7.18001 19.73C7.41568 19.4995 7.71502 19.3449 8.03941 19.286C8.36381 19.2272 8.69838 19.2669 9 19.4C9.31077 19.5143 9.57986 19.7194 9.7725 19.9888C9.96514 20.258 10.0722 20.5791 10.0799 20.91V21.0001C10.0799 21.5304 10.2907 22.0392 10.6658 22.4143C11.0408 22.7894 11.5496 23 12.08 23C12.6105 23 13.1191 22.7894 13.4942 22.4143C13.8693 22.0392 14.08 21.5304 14.08 21.0001V20.83C14.0813 20.5082 14.1766 20.1938 14.3543 19.9255C14.5319 19.6573 14.7842 19.4467 15.08 19.32C15.3817 19.1869 15.7162 19.1471 16.0406 19.206C16.3649 19.2648 16.6643 19.4195 16.8999 19.65L16.96 19.7101C17.1458 19.896 17.3663 20.0435 17.6092 20.1441C17.8519 20.2448 18.1122 20.2966 18.375 20.2966C18.6378 20.2966 18.8981 20.2448 19.1409 20.1441C19.3836 20.0435 19.6043 19.896 19.7901 19.7101C19.976 19.5243 20.1235 19.3037 20.2241 19.0609C20.3248 18.8181 20.3766 18.5578 20.3766 18.295C20.3766 18.0321 20.3248 17.772 20.2241 17.5292C20.1235 17.2863 19.976 17.0658 19.7901 16.88L19.73 16.8201C19.4995 16.5844 19.3448 16.2851 19.286 15.9606ZM14.75 12C14.75 13.5188 13.5188 14.75 12 14.75C10.4812 14.75 9.25 13.5188 9.25 12C9.25 10.4812 10.4812 9.25 12 9.25C13.5188 9.25 14.75 10.4812 14.75 12Z",fill:"currentColor"}),N.exports.createElement("path",{d:"M19.4 15L19.8575 15.2018L19.8595 15.197L19.4 15ZM19.286 15.9606L19.778 15.8713L19.778 15.8713L19.286 15.9606ZM20.0055 14.2743L19.7295 13.8574L19.7293 13.8575L20.0055 14.2743ZM20.91 13.9999L20.91 13.4999L20.9079 13.5L20.91 13.9999ZM22.4142 13.4142L22.7678 13.7678L22.7678 13.7678L22.4142 13.4142ZM22.4142 10.5858L22.7678 10.2323L22.7678 10.2323L22.4142 10.5858ZM20.83 10.0001L20.8278 10.5001H20.83V10.0001ZM19.9255 9.72576L19.6493 10.1425L19.6494 10.1426L19.9255 9.72576ZM19.32 9H18.82V9.10264L18.8604 9.19698L19.32 9ZM19.32 8.92001H19.82V8.81459L19.7774 8.71815L19.32 8.92001ZM19.206 7.95942L19.6979 8.04867L19.6979 8.04867L19.206 7.95942ZM19.65 7.1L19.2967 6.74614L19.2924 6.75044L19.65 7.1ZM19.71 7.04001L20.0633 7.39384L20.0634 7.39371L19.71 7.04001ZM20.1441 6.39088L19.6822 6.19941L19.6822 6.19942L20.1441 6.39088ZM20.1441 4.85912L19.6822 5.05059L19.6822 5.05059L20.1441 4.85912ZM19.71 4.21001L19.3563 4.56338L19.3566 4.56372L19.71 4.21001ZM19.0609 3.77588L19.2524 3.31399L19.2524 3.31399L19.0609 3.77588ZM16.88 4.21001L17.2337 4.56344L17.2337 4.56338L16.88 4.21001ZM16.8201 4.27L17.1697 4.62745L17.1737 4.62343L16.8201 4.27ZM15.9605 4.714L15.8714 4.22202L15.8713 4.22203L15.9605 4.714ZM15 4.6L15.2018 4.14253L15.1969 4.14043L15 4.6ZM14.2743 3.99446L13.8574 4.27051L13.8575 4.27066L14.2743 3.99446ZM13.9999 3.09L13.4999 3.09L13.4999 3.09214L13.9999 3.09ZM13.4142 1.58579L13.0606 1.93936L13.0606 1.93936L13.4142 1.58579ZM10.5858 1.58579L10.9394 1.93936L10.9394 1.93936L10.5858 1.58579ZM10.0001 3.17L10.5001 3.17214V3.17H10.0001ZM9.72575 4.07446L10.1425 4.35066L10.1426 4.35051L9.72575 4.07446ZM9 4.68V5.18H9.10262L9.19695 5.13957L9 4.68ZM8.92 4.68V4.18H8.81457L8.71812 4.22257L8.92 4.68ZM7.95941 4.79401L7.8702 5.28599L7.87022 5.28599L7.95941 4.79401ZM7.1 4.35L6.74642 4.70358L6.75036 4.70743L7.1 4.35ZM7.04 4.29001L6.68625 4.64336L6.68645 4.64356L7.04 4.29001ZM6.39088 3.85588L6.58235 3.39399L6.58233 3.39398L6.39088 3.85588ZM4.85912 3.85588L4.66767 3.39398L4.66764 3.39399L4.85912 3.85588ZM4.21001 4.29001L4.56336 4.64376L4.56377 4.64335L4.21001 4.29001ZM3.77588 4.93912L3.314 4.74764L3.31399 4.74765L3.77588 4.93912ZM3.77588 6.47088L4.23776 6.27941L4.23776 6.27941L3.77588 6.47088ZM4.21001 7.12001L4.5636 6.76649L4.56336 6.76626L4.21001 7.12001ZM4.27 7.18001L4.62744 6.83035L4.62359 6.8265L4.27 7.18001ZM4.714 8.03941L4.22202 8.12861L4.22202 8.12862L4.714 8.03941ZM4.6 9L4.14256 8.79813L4.13618 8.8126L4.13072 8.82745L4.6 9ZM3.09 10.08L3.09 10.5801L3.10163 10.5798L3.09 10.08ZM1.58579 10.6658L1.93929 11.0195L1.93936 11.0194L1.58579 10.6658ZM1.58579 13.4942L1.93936 13.1407L1.93936 13.1407L1.58579 13.4942ZM3.17 14.08L3.17213 13.58H3.17V14.08ZM4.07446 14.3543L3.79841 14.7712L3.79841 14.7712L4.07446 14.3543ZM4.68 15.08L4.2204 15.277L4.22255 15.2819L4.68 15.08ZM4.79401 16.0406L5.28599 16.1298L5.28599 16.1298L4.79401 16.0406ZM4.34999 16.9L4.70385 17.2532L4.70742 17.2496L4.34999 16.9ZM4.29 16.9601L4.64337 17.3138L4.64384 17.3133L4.29 16.9601ZM3.85588 17.6092L4.31774 17.8007L4.31777 17.8006L3.85588 17.6092ZM3.85588 19.1409L3.39397 19.3324L3.39402 19.3325L3.85588 19.1409ZM4.29 19.7901L4.6437 19.4367L4.64337 19.4363L4.29 19.7901ZM4.93911 20.2242L4.74763 20.686L4.74764 20.6861L4.93911 20.2242ZM6.47088 20.2242L6.27941 19.7623L6.2794 19.7623L6.47088 20.2242ZM7.12 19.7901L7.4737 20.1435L7.4738 20.1434L7.12 19.7901ZM7.18001 19.73L6.83041 19.3725L6.82621 19.3767L7.18001 19.73ZM8.03941 19.286L7.95016 18.794L7.95016 18.794L8.03941 19.286ZM9 19.4L8.79814 19.8574L8.81261 19.8638L8.82746 19.8693L9 19.4ZM9.7725 19.9888L9.3658 20.2796L9.36587 20.2797L9.7725 19.9888ZM10.0799 20.91L10.5801 20.91L10.5798 20.8984L10.0799 20.91ZM10.6658 22.4143L11.0195 22.0608L11.0194 22.0607L10.6658 22.4143ZM13.4942 22.4143L13.8478 22.7678L13.8478 22.7678L13.4942 22.4143ZM14.08 20.83L13.58 20.8279V20.83H14.08ZM14.3543 19.9255L13.9374 19.6494L13.9374 19.6495L14.3543 19.9255ZM15.08 19.32L15.277 19.7796L15.2818 19.7774L15.08 19.32ZM16.0406 19.206L15.9513 19.6979L15.9513 19.6979L16.0406 19.206ZM16.8999 19.65L17.2535 19.2964L17.2495 19.2925L16.8999 19.65ZM16.96 19.7101L17.3137 19.3566L17.3136 19.3565L16.96 19.7101ZM17.6092 20.1441L17.8007 19.6823L17.8006 19.6822L17.6092 20.1441ZM19.1409 20.1441L19.3324 20.606L19.3325 20.606L19.1409 20.1441ZM19.7901 19.7101L19.4366 19.3564L19.4364 19.3566L19.7901 19.7101ZM20.2241 19.0609L20.686 19.2524L20.686 19.2524L20.2241 19.0609ZM20.2241 17.5292L20.686 17.3377L20.686 17.3377L20.2241 17.5292ZM19.7901 16.88L20.1435 16.5263L20.1432 16.5261L19.7901 16.88ZM19.73 16.8201L19.3725 17.1697L19.3768 17.174L19.73 16.8201ZM18.9425 14.7982C18.7691 15.1912 18.7173 15.6271 18.794 16.0498L19.778 15.8713C19.737 15.6453 19.7646 15.4122 19.8574 15.2018L18.9425 14.7982ZM19.7293 13.8575C19.3799 14.089 19.1056 14.4175 18.9404 14.803L19.8595 15.197C19.9479 14.9909 20.0946 14.8151 20.2817 14.691L19.7293 13.8575ZM20.9079 13.5C20.4888 13.5017 20.0791 13.6258 19.7295 13.8574L20.2816 14.6911C20.4685 14.5674 20.6877 14.5009 20.9121 14.4999L20.9079 13.5ZM21.0001 13.4999H20.91V14.4999H21.0001V13.4999ZM22.0607 13.0606C21.7794 13.342 21.3978 13.4999 21.0001 13.4999V14.4999C21.663 14.4999 22.2989 14.2366 22.7678 13.7678L22.0607 13.0606ZM22.5 12C22.5 12.3979 22.342 12.7793 22.0607 13.0606L22.7678 13.7678C23.2367 13.2989 23.5 12.6631 23.5 12H22.5ZM22.0607 10.9394C22.342 11.2207 22.5 11.6021 22.5 12H23.5C23.5 11.3369 23.2367 10.7011 22.7678 10.2323L22.0607 10.9394ZM21.0001 10.5001C21.3978 10.5001 21.7794 10.6581 22.0607 10.9394L22.7678 10.2323C22.2989 9.76338 21.663 9.50007 21.0001 9.50007V10.5001ZM20.83 10.5001H21.0001V9.50007H20.83V10.5001ZM19.6494 10.1426C19.9991 10.3742 20.4088 10.4983 20.8278 10.5001L20.8321 9.50007C20.6077 9.49912 20.3885 9.43264 20.2016 9.30888L19.6494 10.1426ZM18.8604 9.19698C19.0256 9.58246 19.2999 9.91099 19.6493 10.1425L20.2017 9.30898C20.0146 9.18495 19.8678 9.00913 19.7795 8.80303L18.8604 9.19698ZM18.82 8.92001V9H19.82V8.92001H18.82ZM18.714 7.87016C18.6373 8.29292 18.6891 8.72889 18.8625 9.12187L19.7774 8.71815C19.6846 8.50788 19.6569 8.27469 19.6979 8.04867L18.714 7.87016ZM19.2924 6.75044C18.9922 7.05749 18.7907 7.44748 18.714 7.87017L19.6979 8.04867C19.7389 7.82258 19.8468 7.61389 20.0075 7.44956L19.2924 6.75044ZM19.3568 6.68618L19.2967 6.74617L20.0032 7.45383L20.0633 7.39384L19.3568 6.68618ZM19.6822 6.19942C19.6068 6.3815 19.4961 6.54696 19.3566 6.68631L20.0634 7.39371C20.2958 7.16156 20.4802 6.88587 20.606 6.58235L19.6822 6.19942ZM19.7966 5.62501C19.7966 5.82209 19.7577 6.01728 19.6822 6.19941L20.606 6.58236C20.7318 6.2789 20.7966 5.95359 20.7966 5.62501H19.7966ZM19.6822 5.05059C19.7577 5.23272 19.7966 5.42792 19.7966 5.62501H20.7966C20.7966 5.29643 20.7318 4.97111 20.606 4.66765L19.6822 5.05059ZM19.3566 4.56372C19.4961 4.70305 19.6068 4.86851 19.6822 5.05059L20.606 4.66765C20.4802 4.36414 20.2958 4.08844 20.0634 3.8563L19.3566 4.56372ZM18.8694 4.23777C19.0515 4.31325 19.2169 4.42388 19.3563 4.56338L20.0638 3.85664C19.8316 3.62422 19.5559 3.43981 19.2524 3.31399L18.8694 4.23777ZM18.295 4.12343C18.4921 4.12343 18.6873 4.16228 18.8694 4.23777L19.2524 3.31399C18.9488 3.18818 18.6235 3.12343 18.295 3.12343V4.12343ZM17.7206 4.23777C17.9027 4.16227 18.0978 4.12343 18.295 4.12343V3.12343C17.9664 3.12343 17.6412 3.18818 17.3377 3.31399L17.7206 4.23777ZM17.2337 4.56338C17.3731 4.42388 17.5385 4.31325 17.7206 4.23777L17.3377 3.31399C17.0341 3.43981 16.7585 3.62422 16.5263 3.85664L17.2337 4.56338ZM17.1737 4.62343L17.2337 4.56344L16.5263 3.85658L16.4664 3.91657L17.1737 4.62343ZM16.0497 5.20599C16.4725 5.12936 16.8626 4.92785 17.1697 4.62742L16.4704 3.91258C16.3062 4.07324 16.0976 4.18102 15.8714 4.22202L16.0497 5.20599ZM14.7981 5.05745C15.1912 5.23087 15.6271 5.28263 16.0498 5.20598L15.8713 4.22203C15.6453 4.26302 15.4121 4.23536 15.2018 4.14255L14.7981 5.05745ZM13.8575 4.27066C14.089 4.6201 14.4176 4.89437 14.803 5.05957L15.1969 4.14043C14.9909 4.05211 14.8151 3.90541 14.691 3.71827L13.8575 4.27066ZM13.4999 3.09214C13.5017 3.51128 13.6258 3.92088 13.8574 4.27051L14.6911 3.71842C14.5674 3.53147 14.5009 3.31231 14.4999 3.08787L13.4999 3.09214ZM13.4999 3.00001V3.09H14.4999V3.00001H13.4999ZM13.0606 1.93936C13.3419 2.22063 13.4999 2.60214 13.4999 3.00001H14.4999C14.4999 2.337 14.2366 1.7011 13.7677 1.23223L13.0606 1.93936ZM12 1.5C12.3978 1.5 12.7793 1.65802 13.0606 1.93936L13.7677 1.23223C13.2989 0.763416 12.6631 0.5 12 0.5V1.5ZM10.9394 1.93936C11.2207 1.65802 11.6022 1.5 12 1.5V0.5C11.3369 0.5 10.7011 0.763416 10.2323 1.23223L10.9394 1.93936ZM10.5001 3.00001C10.5001 2.60214 10.6581 2.22063 10.9394 1.93936L10.2323 1.23223C9.76337 1.7011 9.50006 2.337 9.50006 3.00001H10.5001ZM10.5001 3.17V3.00001H9.50006V3.17H10.5001ZM10.1426 4.35051C10.3742 4.00088 10.4983 3.59128 10.5001 3.17214L9.50007 3.16786C9.49911 3.39231 9.43265 3.61146 9.30886 3.79842L10.1426 4.35051ZM9.19695 5.13957C9.58244 4.97437 9.91098 4.7001 10.1425 4.35066L9.30896 3.79827C9.18494 3.98541 9.00914 4.1321 8.80305 4.22042L9.19695 5.13957ZM8.92 5.18H9V4.18H8.92V5.18ZM7.87022 5.28599C8.29291 5.36262 8.72887 5.31088 9.12188 5.13743L8.71812 4.22257C8.5079 4.31534 8.2747 4.34302 8.0486 4.30203L7.87022 5.28599ZM6.75036 4.70743C7.05747 5.00783 7.44751 5.20934 7.8702 5.28599L8.04862 4.30204C7.82253 4.26104 7.6139 4.15325 7.44963 3.99257L6.75036 4.70743ZM6.68645 4.64356L6.74645 4.70356L7.45354 3.99644L7.39355 3.93645L6.68645 4.64356ZM6.19941 4.31776C6.3815 4.39325 6.54694 4.50389 6.68625 4.64336L7.39375 3.93665C7.16157 3.70421 6.88585 3.51981 6.58235 3.39399L6.19941 4.31776ZM5.625 4.20343C5.82212 4.20343 6.01731 4.24229 6.19943 4.31777L6.58233 3.39398C6.27885 3.2682 5.95355 3.20343 5.625 3.20343V4.20343ZM5.05057 4.31777C5.23268 4.24229 5.42789 4.20343 5.625 4.20343V3.20343C5.29646 3.20343 4.97115 3.26819 4.66767 3.39398L5.05057 4.31777ZM4.56377 4.64335C4.70306 4.50389 4.86849 4.39325 5.05059 4.31776L4.66764 3.39399C4.36414 3.51981 4.08842 3.70421 3.85624 3.93666L4.56377 4.64335ZM4.23776 5.1306C4.31325 4.94851 4.42389 4.78307 4.56336 4.64376L3.85665 3.93626C3.62421 4.16844 3.43981 4.44415 3.314 4.74764L4.23776 5.1306ZM4.12343 5.70501C4.12343 5.50787 4.16228 5.31267 4.23776 5.13059L3.31399 4.74765C3.18817 5.05116 3.12343 5.37648 3.12343 5.70501H4.12343ZM4.23776 6.27941C4.16228 6.09732 4.12343 5.90214 4.12343 5.70501H3.12343C3.12343 6.03354 3.18817 6.35885 3.31399 6.66235L4.23776 6.27941ZM4.56336 6.76626C4.42389 6.62694 4.31325 6.4615 4.23776 6.27941L3.31399 6.66235C3.43981 6.96586 3.62421 7.24157 3.85665 7.47376L4.56336 6.76626ZM4.62359 6.8265L4.5636 6.76649L3.85641 7.47352L3.9164 7.53352L4.62359 6.8265ZM5.20598 7.95022C5.12935 7.52752 4.92783 7.13747 4.62742 6.83037L3.91258 7.52965C4.07325 7.69391 4.18103 7.90253 4.22202 8.12861L5.20598 7.95022ZM5.05743 9.20188C5.23088 8.80887 5.28262 8.37292 5.20598 7.95021L4.22202 8.12862C4.26302 8.35472 4.23534 8.5879 4.14256 8.79813L5.05743 9.20188ZM4.30221 10.1792C4.65304 9.9282 4.92035 9.57757 5.06928 9.17256L4.13072 8.82745C4.05109 9.04399 3.90816 9.23154 3.7204 9.36584L4.30221 10.1792ZM3.10163 10.5798C3.53294 10.5698 3.95127 10.4302 4.30221 10.1792L3.7204 9.36584C3.53275 9.50008 3.30904 9.57473 3.07837 9.58009L3.10163 10.5798ZM3.00001 10.58H3.09V9.57996H3.00001V10.58ZM1.93936 11.0194C2.22069 10.738 2.60223 10.58 3.00001 10.58V9.57996C2.33691 9.57996 1.70104 9.84346 1.23223 10.3123L1.93936 11.0194ZM1.5 12.08C1.5 11.6821 1.65806 11.3006 1.93929 11.0195L1.23229 10.3122C0.763381 10.781 0.5 11.417 0.5 12.08H1.5ZM1.93936 13.1407C1.65802 12.8593 1.5 12.4779 1.5 12.08H0.5C0.5 12.7431 0.763415 13.3789 1.23223 13.8478L1.93936 13.1407ZM3.00001 13.58C2.60214 13.58 2.22063 13.422 1.93936 13.1407L1.23222 13.8478C1.7011 14.3167 2.337 14.58 3.00001 14.58V13.58ZM3.17 13.58H3.00001V14.58H3.17V13.58ZM4.35051 13.9374C4.00088 13.7059 3.59127 13.5818 3.17213 13.58L3.16786 14.58C3.3923 14.5809 3.61146 14.6474 3.79841 14.7712L4.35051 13.9374ZM5.13956 14.883C4.97441 14.4977 4.70016 14.1689 4.35051 13.9374L3.79841 14.7712C3.98534 14.895 4.13206 15.0708 4.22043 15.277L5.13956 14.883ZM5.28599 16.1298C5.36262 15.7071 5.31087 15.2712 5.13744 14.8782L4.22255 15.2819C4.31535 15.4922 4.34301 15.7253 4.30203 15.9514L5.28599 16.1298ZM4.70742 17.2496C5.00783 16.9425 5.20934 16.5525 5.28599 16.1298L4.30203 15.9514C4.26104 16.1774 4.15325 16.3861 3.99257 16.5503L4.70742 17.2496ZM4.64384 17.3133L4.70383 17.2532L3.99616 16.5467L3.93616 16.6068L4.64384 17.3133ZM4.31777 17.8006C4.39324 17.6186 4.50388 17.4531 4.64337 17.3138L3.93663 16.6063C3.70422 16.8385 3.51981 17.1142 3.39398 17.4177L4.31777 17.8006ZM4.20343 18.3751C4.20343 18.1778 4.2423 17.9826 4.31774 17.8007L3.39401 17.4177C3.26818 17.7211 3.20343 18.0465 3.20343 18.3751H4.20343ZM4.31778 18.9495C4.24228 18.7673 4.20343 18.5721 4.20343 18.3751H3.20343C3.20343 18.7035 3.26819 19.0289 3.39397 19.3324L4.31778 18.9495ZM4.64337 19.4363C4.50394 19.2971 4.39325 19.1315 4.31773 18.9494L3.39402 19.3325C3.5198 19.6358 3.70416 19.9116 3.93663 20.1438L4.64337 19.4363ZM5.13059 19.7623C4.94852 19.6868 4.78305 19.5761 4.6437 19.4367L3.93631 20.1435C4.16845 20.3758 4.44414 20.5602 4.74763 20.686L5.13059 19.7623ZM5.705 19.8767C5.50792 19.8767 5.31272 19.8378 5.13059 19.7623L4.74764 20.6861C5.0511 20.8118 5.37642 20.8767 5.705 20.8767V19.8767ZM6.2794 19.7623C6.09727 19.8378 5.90208 19.8767 5.705 19.8767V20.8767C6.03359 20.8767 6.35889 20.8118 6.66235 20.6861L6.2794 19.7623ZM6.7663 19.4367C6.62695 19.5761 6.46149 19.6868 6.27941 19.7623L6.66235 20.6861C6.96586 20.5602 7.24155 20.3758 7.4737 20.1435L6.7663 19.4367ZM6.82621 19.3767L6.7662 19.4368L7.4738 20.1434L7.53381 20.0833L6.82621 19.3767ZM7.95016 18.794C7.52747 18.8707 7.13748 19.0723 6.83044 19.3725L7.52957 20.0875C7.69388 19.9268 7.90256 19.819 8.12866 19.778L7.95016 18.794ZM9.20186 18.9425C8.80888 18.7691 8.37292 18.7173 7.95016 18.794L8.12866 19.778C8.3547 19.737 8.58788 19.7646 8.79814 19.8574L9.20186 18.9425ZM10.1792 19.6979C9.92824 19.347 9.57759 19.0796 9.17254 18.9307L8.82746 19.8693C9.04396 19.9489 9.23149 20.0918 9.3658 20.2796L10.1792 19.6979ZM10.5798 20.8984C10.5698 20.4671 10.4302 20.0487 10.1791 19.6978L9.36587 20.2797C9.50006 20.4673 9.57472 20.691 9.58008 20.9216L10.5798 20.8984ZM10.5799 21.0001V20.91H9.57995V21.0001H10.5799ZM11.0194 22.0607C10.738 21.7793 10.5799 21.3978 10.5799 21.0001H9.57995C9.57995 21.6631 9.84346 22.299 10.3123 22.7678L11.0194 22.0607ZM12.08 22.5C11.6821 22.5 11.3006 22.342 11.0195 22.0608L10.3122 22.7678C10.781 23.2367 11.417 23.5 12.08 23.5V22.5ZM13.1407 22.0607C12.8593 22.342 12.4779 22.5 12.08 22.5V23.5C12.7431 23.5 13.3789 23.2367 13.8478 22.7678L13.1407 22.0607ZM13.58 21.0001C13.58 21.3978 13.422 21.7794 13.1407 22.0607L13.8478 22.7678C14.3167 22.2989 14.58 21.663 14.58 21.0001H13.58ZM13.58 20.83V21.0001H14.58V20.83H13.58ZM13.9374 19.6495C13.7059 19.9991 13.5818 20.4088 13.58 20.8279L14.58 20.8321C14.5809 20.6077 14.6474 20.3885 14.7712 20.2016L13.9374 19.6495ZM14.883 18.8604C14.4977 19.0256 14.1689 19.2999 13.9374 19.6494L14.7712 20.2016C14.8949 20.0146 15.0708 19.8679 15.277 19.7795L14.883 18.8604ZM16.1298 18.714C15.7071 18.6373 15.2712 18.6891 14.8782 18.8625L15.2818 19.7774C15.4922 19.6846 15.7253 19.6569 15.9513 19.6979L16.1298 18.714ZM17.2495 19.2925C16.9425 18.9922 16.5525 18.7907 16.1298 18.714L15.9513 19.6979C16.1774 19.739 16.3861 19.8468 16.5504 20.0075L17.2495 19.2925ZM17.3136 19.3565L17.2535 19.2964L16.5464 20.0035L16.6065 20.0636L17.3136 19.3565ZM17.8006 19.6822C17.6185 19.6068 17.4531 19.4961 17.3137 19.3566L16.6064 20.0635C16.8385 20.2958 17.1142 20.4802 17.4177 20.606L17.8006 19.6822ZM18.375 19.7966C18.1779 19.7966 17.9827 19.7577 17.8007 19.6823L17.4176 20.606C17.7211 20.7318 18.0464 20.7966 18.375 20.7966V19.7966ZM18.9495 19.6822C18.7673 19.7578 18.5721 19.7966 18.375 19.7966V20.7966C18.7036 20.7966 19.0289 20.7318 19.3324 20.606L18.9495 19.6822ZM19.4364 19.3566C19.2971 19.4961 19.1315 19.6068 18.9494 19.6823L19.3325 20.606C19.6358 20.4802 19.9115 20.2958 20.1437 20.0635L19.4364 19.3566ZM19.7623 18.8695C19.6868 19.0515 19.5761 19.217 19.4366 19.3564L20.1435 20.0637C20.3758 19.8316 20.5602 19.5559 20.686 19.2524L19.7623 18.8695ZM19.8766 18.295C19.8766 18.492 19.8378 18.6873 19.7623 18.8695L20.686 19.2524C20.8118 18.9489 20.8766 18.6236 20.8766 18.295H19.8766ZM19.7623 17.7206C19.8378 17.9028 19.8766 18.0979 19.8766 18.295H20.8766C20.8766 17.9664 20.8118 17.6412 20.686 17.3377L19.7623 17.7206ZM19.4366 17.2337C19.5761 17.3731 19.6868 17.5385 19.7623 17.7206L20.686 17.3377C20.5602 17.0341 20.3758 16.7585 20.1435 16.5263L19.4366 17.2337ZM19.3768 17.174L19.4369 17.234L20.1432 16.5261L20.0831 16.4661L19.3768 17.174ZM18.794 16.0498C18.8707 16.4726 19.0722 16.8626 19.3725 17.1696L20.0875 16.4705C19.9268 16.3062 19.819 16.0975 19.778 15.8713L18.794 16.0498ZM12 15.25C13.795 15.25 15.25 13.795 15.25 12H14.25C14.25 13.2427 13.2427 14.25 12 14.25V15.25ZM8.75 12C8.75 13.795 10.205 15.25 12 15.25V14.25C10.7573 14.25 9.75 13.2427 9.75 12H8.75ZM12 8.75C10.205 8.75 8.75 10.205 8.75 12H9.75C9.75 10.7573 10.7573 9.75 12 9.75V8.75ZM15.25 12C15.25 10.205 13.795 8.75 12 8.75V9.75C13.2427 9.75 14.25 10.7573 14.25 12H15.25Z",fill:"currentColor"}))}var ko="_button_1t4fm_7",Ao="_dot_1t4fm_14",Do="_menu_1t4fm_26",To="_menuItem_1t4fm_31",Po="_highlight_1t4fm_41";class Vo extends x.Component{constructor(){super(...arguments),this.button=x.createRef(),this.state={isOpen:!1,isUpdateReadyDialogOpen:!1,isShortcutDialogOpen:!1},this.handleToggle=()=>{this.setState((e=>({isOpen:!e.isOpen})))},this.handleClose=()=>{this.setState({isOpen:!1})},this.handleSwitchTheme=()=>{Bt.apply("light"===Bt.theme?"dark":"light"),this.handleClose()},this.handleUpdateCheck=async()=>{this.handleClose(),await js.check()},this.handleUpdateDownload=async()=>{this.handleClose(),await js.download()},this.handleUpdateInstall=()=>{this.setState({isUpdateReadyDialogOpen:!0}),this.handleClose()},this.handleViewShortcuts=()=>{this.setState({isOpen:!1,isShortcutDialogOpen:!0})}}componentDidMount(){let e=v((()=>js.isUpdateReady)).observe((({newValue:t})=>{t&&(e(),this.setState({isUpdateReadyDialogOpen:!0}))}))}render(){const{isOpen:e,isUpdateReadyDialogOpen:t,isShortcutDialogOpen:s}=this.state;return x.createElement(x.Fragment,null,x.createElement(Qs,{innerRef:this.button,className:S(ko,{[Ao]:!js.isUpToDate||js.isUpdateReady}),ghost:!0,onClick:this.handleToggle,"data-testid":"settings-dialog"},x.createElement(Oo,null)),e&&x.createElement(Ai,{target:this.button,onClickOutside:this.handleClose},x.createElement("ul",{className:Do},x.createElement("li",{className:To,onClick:this.handleSwitchTheme},"light"===Bt.theme?"Dark theme":"Light theme"),js.hasCheckedForUpdates&&!js.isUpToDate&&!js.isDownloading&&!js.isUpdateReady&&x.createElement("li",{className:S(To,{[Po]:!0}),onClick:this.handleUpdateDownload},"Download v",js.latestVersion),js.isDownloading&&x.createElement("li",{className:S(To,{[Po]:!0}),onClick:this.handleUpdateDownload},"Downloading v",js.latestVersion),js.isUpdateReady&&x.createElement("li",{className:S(To,{[Po]:!0}),onClick:this.handleUpdateInstall},"Install v",js.latestVersion),V.updates&&x.createElement("li",{className:To,onClick:this.handleUpdateCheck},"Check for updates"),x.createElement("li",{className:To},x.createElement("a",{href:"https://github.com/prisma/studio/releases",target:"_blank",rel:"noreferrer noopener"},"View changelog")),x.createElement("li",{className:To,onClick:this.handleViewShortcuts,"data-testid":"view-shortcuts"},"View shortcuts"))),t&&x.createElement(Mo,{onClose:()=>this.setState({isUpdateReadyDialogOpen:!1})}),s&&x.createElement(No,{onClose:()=>this.setState({isShortcutDialogOpen:!1})}))}}var jo=_(Vo);var Fo={container:"_container_18ph6_1",title:"_title_18ph6_11",active:"_active_18ph6_14",preview:"_preview_18ph6_23",modelListTabButton:"_modelListTabButton_18ph6_27",closeButton:"_closeButton_18ph6_50",dirtyIndicator:"_dirtyIndicator_18ph6_59"};var Bo={container:"_container_os6jn_1",content:"_content_os6jn_10",description:"_description_os6jn_19"};class Ho extends x.Component{render(){const{onCancel:e,onDiscard:t,tabHasFilters:s,tabIsDirty:i}=this.props,a=(s&&i?"Unsaved changes on a filtered view":s&&"Filtered view")||i&&"Unsaved changes",n=(s&&i?"This tab has unsaved changes and filters applied. Do you still want to close it?":s&&"This tab has filters applied. Do you still want to close it?")||i&&"This tab contains unsaved changes. Do you want to discard them?",r=(s&&i?"Discard changes":s&&"Close filtered view")||i&&"Discard changes";return x.createElement(Xl,{dataTestId:"tab-discard-dialog",className:Bo.container,title:a,primaryButton:x.createElement(Qs,{"data-testid":"discard-btn",red:!0,onClick:t},r),secondaryButton:x.createElement(Qs,{"data-testid":"cancel-btn",ghost:!0,className:Bo.cancelBtn,onClick:e},"Cancel")},x.createElement("div",{className:Bo.content},x.createElement("p",{className:Bo.description},n)))}}var Zo=_(Ho);class qo extends x.PureComponent{constructor(e){super(e),this.container=x.createRef(),this.handleClick=()=>{Tt.switch({id:this.props.id})},this.handleDoubleClick=()=>{const e=Tt.get(this.props.id);e&&e.preview&&e.update({preview:!1})},this.handleClose=e=>{e&&e.stopPropagation(),e&&e.preventDefault();const t=Tt.get(this.props.id);t&&(t.isDirty||t.hasFilters?this.setState({isCloseDialogOpen:!0}):(Tt.remove(this.props.id),this.setState({isCloseDialogOpen:!1})))},this.handleDiscardChanges=()=>{Tt.remove(this.props.id),this.setState({isCloseDialogOpen:!1})},this.handleCancelClose=()=>{this.setState({isCloseDialogOpen:!1})},this.state={isCloseDialogOpen:!1},this.reaction=v((()=>Tt.activeTabId)).observe((({newValue:t})=>{var s;t===e.id&&(null==(s=this.container.current)||s.scrollIntoView())}))}componentWillUnmount(){this.reaction()}render(){const{isCloseDialogOpen:e}=this.state,{id:t}=this.props,s=Tt.get(t),i=Tt.activeTabId===t;if(!s||!s.session)return null;const a=null==s?void 0:s.isDirty,n=null==s?void 0:s.hasFilters;return x.createElement("div",{ref:this.container,"data-testid":"tab","data-test-active":i,"data-test-dirty":a||n,"data-test-preview":s.preview,className:S(Fo.container,{[Fo.active]:i,[Fo.preview]:s.preview}),onClick:this.handleClick,onDoubleClick:this.handleDoubleClick},s.session.isModelList&&x.createElement("div",{"data-testid":"new-tab-btn",className:Fo.modelListTabButton},x.createElement(ea,null)),s.session.isScript&&x.createElement(x.Fragment,null,x.createElement("div",{"data-testid":"title",className:Fo.title},s.title),x.createElement(ti,{"data-testid":"close-btn",className:S(Fo.closeButton,{[Fo.dirty]:a||n}),onClick:this.handleClose},a?x.createElement("div",{className:Fo.dirtyIndicator}):x.createElement(ea,null)),e&&x.createElement(Zo,{tabHasFilters:n,tabIsDirty:a,onCancel:this.handleCancelClose,onDiscard:this.handleDiscardChanges})),x.createElement(si,{keys:"mod+w",onMatch:()=>this.handleClose()}))}}var Uo=_(qo);var zo="_container_13n52_1";class $o extends x.Component{render(){return x.createElement("div",{"data-testid":"tab-bar",className:zo},Tt.openTabs.map((e=>x.createElement(Uo,{key:e.id,id:e.id}))))}}var Jo=_($o);var Wo="_container_1ud7d_1";var Ko=_((({className:e})=>x.createElement("div",{"data-testid":"header",className:S(Wo,e),style:{paddingLeft:32}},x.createElement(Jo,null),x.createElement(jo,null))));var Go={container:"_container_1xigg_1",content:"_content_1xigg_10",header:"_header_1xigg_20",title:"_title_1xigg_24",search:"_search_1xigg_30",section:"_section_1xigg_41",allModels:"_allModels_1xigg_45",sectionTitle:"_sectionTitle_1xigg_50",list:"_list_1xigg_55",model:"_model_1xigg_60",active:"_active_1xigg_66",spacer:"_spacer_1xigg_72",count:"_count_1xigg_75",empty:"_empty_1xigg_79"};class Qo extends x.PureComponent{constructor(){var e;super(...arguments),this.state={searchValue:"",highlightedSection:"recent",highlightedIdx:0},this.input=x.createRef(),this.recentModelRefs=((null==(e=Kt.activeProject)?void 0:e.recentModelIds)||[]).map((e=>x.createRef())),this.allModelRefs=Object.keys(as.values).map((e=>x.createRef())),this.filteredModelRefs=[],this.getRecentModels=()=>{var e;return(null==(e=Kt.activeProject)?void 0:e.recentModels)||[]},this.getAllModels=()=>c.exports.orderBy(Object.values(as.values),["name"]),this.getFilteredModels=e=>this.getAllModels().filter((t=>t.name.toLowerCase().includes(e.toLowerCase()))),this.scrollToHighlightedIdx=()=>{var e,t,s,i,a,n;const{highlightedSection:r,highlightedIdx:l}=this.state;"recent"===r?null==(t=null==(e=this.recentModelRefs[l])?void 0:e.current)||t.scrollIntoView({behavior:"smooth",block:"nearest"}):"all"===r?null==(i=null==(s=this.allModelRefs[l])?void 0:s.current)||i.scrollIntoView({behavior:"smooth",block:"nearest"}):"filtered"===r&&(null==(n=null==(a=this.filteredModelRefs[l])?void 0:a.current)||n.scrollIntoView({behavior:"smooth",block:"nearest"}))},this.handleSelectModel=e=>{var t;const s=as.get(e);if(!s)return;null==(t=Kt.activeProject)||t.addRecentModel(s.id);let i=Object.values(Tt.values).find((e=>{var t;return(null==(t=e.session)?void 0:t.isScript)&&e.session.script.model.id===s.id&&e.session.script.frozen}))||null;return i?(i.update({preview:!1}),void Tt.switch({id:i.id})):(i=Tt.previewTab,i?(i.load({modelId:s.id}),i.update({preview:!1}),void Tt.switch({id:i.id})):(i=Tt.add({modelId:s.id,preview:!1}),void Tt.switch({id:i.id})))},this.handleSelectHighlightedModel=()=>{var e,t,s;const{highlightedSection:i,highlightedIdx:a,searchValue:n}=this.state;"recent"===i?this.handleSelectModel(null==(e=this.getRecentModels()[a])?void 0:e.id):"all"===i?this.handleSelectModel(null==(t=this.getAllModels()[a])?void 0:t.id):"filtered"===i&&this.handleSelectModel(null==(s=this.getFilteredModels(n)[a])?void 0:s.id)},this.handleSearch=e=>{var t;this.filteredModelRefs=this.getFilteredModels(e).map((e=>x.createRef()));const s=(null==(t=Kt.activeProject)?void 0:t.recentModels)||[];""===e?this.setState({searchValue:e,highlightedSection:s.length>0?"recent":"all",highlightedIdx:0}):this.setState({searchValue:e,highlightedSection:"filtered",highlightedIdx:0})},this.handleHighlightPrev=()=>{this.setState((e=>{var t;let s=e.highlightedSection,i=e.highlightedIdx;if("recent"===e.highlightedSection)e.highlightedIdx-1<0||(i-=1);else if("all"===e.highlightedSection)if(e.highlightedIdx-1<0){const e=(null==(t=Kt.activeProject)?void 0:t.recentModels)||[];0===e.length||(s="recent",i=e.length-1)}else i-=1;else"filtered"===e.highlightedSection&&(e.highlightedIdx-1<0||(i-=1));return{highlightedSection:s,highlightedIdx:i}}),(()=>this.scrollToHighlightedIdx()))},this.handleHighlightNext=()=>{this.setState((e=>{var t;let s=e.highlightedSection,i=e.highlightedIdx;if("recent"===e.highlightedSection){const a=(null==(t=Kt.activeProject)?void 0:t.recentModels)||[];e.highlightedIdx+1>=a.length?(s="all",i=0):i+=1}else if("all"===e.highlightedSection){const t=Object.values(as.values);e.highlightedIdx+1>=t.length||(i+=1)}else if("filtered"===e.highlightedSection){const t=this.getFilteredModels(e.searchValue);e.highlightedIdx+1>=t.length||(i+=1)}return{highlightedSection:s,highlightedIdx:i}}),(()=>this.scrollToHighlightedIdx()))}}render(){var e;const{searchValue:t,highlightedSection:s,highlightedIdx:i}=this.state,a=(null==(e=Kt.activeProject)?void 0:e.recentModels)||[],n=this.getAllModels(),r=this.getFilteredModels(t);return x.createElement("div",{className:Go.container,"data-testid":"model-list-tab"},x.createElement("div",{className:Go.content},x.createElement("header",{className:Go.header},x.createElement("div",{className:Go.title},"Open a Model"),x.createElement(ai,{"data-testid":"model-list-search",innerRef:this.input,type:"text",className:Go.search,focusOnMount:!0,value:t,onChange:this.handleSearch,placeholder:"Search"})),a.length>0&&""===t&&x.createElement("section",{"data-testid":"recent-model-list",className:Go.section},x.createElement("div",{className:Go.sectionTitle},"Recently Opened"),x.createElement("ul",{className:Go.list},a.map(((e,t)=>x.createElement(Yo,{key:e.id,ref:this.recentModelRefs[t],name:e.name,count:e.count,isActive:"recent"===s&&i===t,onClick:()=>this.handleSelectModel(e.id)}))))),""===t&&x.createElement("section",{"data-testid":"model-list",className:S(Go.section,Go.allModels)},x.createElement("div",{className:Go.sectionTitle},"All Models"),x.createElement("ul",{className:Go.list},n.map(((e,t)=>x.createElement(Yo,{key:e.id,ref:this.allModelRefs[t],name:e.name,count:e.count,isActive:"all"===s&&i===t,onClick:()=>this.handleSelectModel(e.id)}))))),""!==t&&x.createElement("section",{"data-testid":"filtered-model-list",className:S(Go.section,Go.allModels)},x.createElement("div",{className:Go.sectionTitle},"Matches"),x.createElement("ul",{className:Go.list},r.map(((e,t)=>x.createElement(Yo,{key:e.id,ref:this.filteredModelRefs[t],name:e.name,count:e.count,isActive:"filtered"===s&&i===t,onClick:()=>{var t;this.handleSelectModel(e.id),null==(t=Tt.activeTab)||t.update({preview:!1})}})))),0===r.length&&x.createElement("div",{className:Go.empty},"No matches"))),x.createElement(si,{keys:["command+shift+[","command+option+right"],target:this.input,onMatch:()=>Tt.switch({direction:"right"})}),x.createElement(si,{keys:["command+shift+]","command+option+left"],target:this.input,onMatch:()=>Tt.switch({direction:"left"})}),x.createElement(si,{keys:"up",target:this.input,onMatch:()=>this.handleHighlightPrev()}),x.createElement(si,{keys:"down",target:this.input,onMatch:()=>this.handleHighlightNext()}),x.createElement(si,{keys:"enter",target:this.input,onMatch:()=>this.handleSelectHighlightedModel()}),x.createElement(si,{keys:"cmd+enter",target:this.input,onMatch:()=>this.handleSelectHighlightedModel()}))}}const Yo=N.exports.forwardRef((({name:e,count:t=0,isActive:s=!1,onClick:i},a)=>x.createElement("li",{ref:a,className:S(Go.model,{[Go.active]:s}),onClick:i},x.createElement("div",{"data-testid":"model-name",className:Go.name},e),x.createElement("div",{className:Go.spacer}),x.createElement("div",{"data-testid":"model-record-count",className:Go.count},null!=t?t:""))));var Xo=_(Qo);var ed="_container_dq66k_1",td="_header_dq66k_9",sd="_body_dq66k_14";class id extends x.PureComponent{constructor(){super(...arguments),this.state={error:!1}}componentDidCatch(e){us.update({type:"fatal",description:e.message,dump:e.stack||""}),ye.updateError({visible:!0}),F({path:"Studio.componentDidCatch",message:"Caught an error during rendering",nativeError:e})}static getDerivedStateFromError(e){return{error:!0,errorMessage:e.message}}render(){var e,t,s,i;const{error:a}=this.state;return a?x.createElement(ho,null):x.createElement("div",{className:ed},x.createElement(Ko,{className:td}),x.createElement("div",{className:sd},(null==(t=null==(e=Tt.activeTab)?void 0:e.session)?void 0:t.isScript)&&x.createElement(no,null),(null==(i=null==(s=Tt.activeTab)?void 0:s.session)?void 0:i.isModelList)&&x.createElement(Xo,null)),ye.error.visible&&"client"===us.type&&x.createElement(oo,null),ye.error.visible&&"fatal"===us.type&&x.createElement(ho,null),ye.error.visible&&"schemaDrift"===us.type&&x.createElement(uo,null))}}var ad=_(id);var nd="_container_1d62f_1",rd="_tab1_1d62f_11",ld="_tab2_1d62f_17",od="_tabEmptySpace_1d62f_22",dd="_filter1_1d62f_28",cd="_filter2_1d62f_29",hd="_filter3_1d62f_30",pd="_filter4_1d62f_31",ud="_separator_1d62f_55",md="_button1_1d62f_61",gd="_btnSpace_1d62f_69",vd="_table_1d62f_90";class fd extends x.PureComponent{render(){return x.createElement("div",{"data-testid":"skeleton",className:nd},x.createElement("div",{className:rd,style:{paddingLeft:32}}),x.createElement("div",{className:ld}),x.createElement("div",{className:od}),x.createElement("div",{className:dd}),x.createElement("div",{className:cd}),x.createElement("div",{className:hd}),x.createElement("div",{className:pd}),x.createElement("div",{className:ud}),x.createElement("div",{className:md}),x.createElement("div",{className:gd}," Getting things ready..."),x.createElement("div",{className:vd}))}}var yd=_(fd);var Id="_container_1w9ta_1";class Cd extends x.Component{constructor(){super(...arguments),this.state={isShortcutDialogOpen:!1}}async componentDidMount(){js.hasCheckedForUpdates||await js.check()}render(){const{isShortcutDialogOpen:e}=this.state;return x.createElement(x.Fragment,null,x.createElement("div",{className:S(Id,{"theme--light":"light"===Bt.theme,"theme--dark":"dark"===Bt.theme})},qs.ready&&Tt.activeTab?x.createElement(cl,{value:new ll({sessionId:Tt.activeTab.sessionId})},x.createElement(ad,null)):x.createElement(yd,null),x.createElement("div",{id:"modal-root"}),x.createElement("div",{id:"dropdown-root"}),x.createElement("div",{id:"dialog-root"})),x.createElement(si,{keys:"mod+/",onMatch:()=>this.setState((e=>({isShortcutDialogOpen:!e.isShortcutDialogOpen})))}),e&&x.createElement(No,{onClose:()=>this.setState({isShortcutDialogOpen:!1})}))}}var wd=_(Cd);window.databrowser=async function(e){await V.update(e),await qs.initTransport(),qs.init();const t=document.createElement("div");t.id="root",document.body.appendChild(t),R.render(x.createElement(wd),t)},window.splash=async function(e){await V.update(e),await qs.initTransport();const t=document.createElement("div");t.id="root",document.body.appendChild(t),R.render(x.createElement(Ii),t)};
