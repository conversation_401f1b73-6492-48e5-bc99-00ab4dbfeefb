const express = require('express');
const { body } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const geminiController = require('../controllers/geminiController');
const { validateRequest } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// 所有Gemini路由都需要认证
router.use(authenticate);

/**
 * @route   POST /api/v1/gemini/test-connection
 * @desc    测试Gemini API连接
 * @access  Private
 */
router.post('/test-connection', [
  body('apiKey')
    .optional()
    .notEmpty()
    .withMessage('API密钥不能为空'),
  validateRequest
], asyncHandler(geminiController.testConnection));

/**
 * @route   POST /api/v1/gemini/analyze-product
 * @desc    分析单个产品
 * @access  Private
 */
router.post('/analyze-product', [
  body('productData')
    .notEmpty()
    .withMessage('产品数据不能为空'),
  body('analysisType')
    .isIn(['basic', 'detailed', 'competitive'])
    .withMessage('分析类型不正确'),
  body('platform')
    .isIn(['taobao', 'tmall', 'jd', 'pdd'])
    .withMessage('平台类型不正确'),
  validateRequest
], asyncHandler(geminiController.analyzeProduct));

/**
 * @route   POST /api/v1/gemini/analyze-market
 * @desc    分析市场趋势
 * @access  Private
 */
router.post('/analyze-market', [
  body('category')
    .notEmpty()
    .withMessage('商品类别不能为空'),
  body('keywords')
    .isArray({ min: 1 })
    .withMessage('关键词列表不能为空'),
  body('timeRange')
    .isIn(['7d', '30d', '90d', '180d'])
    .withMessage('时间范围不正确'),
  validateRequest
], asyncHandler(geminiController.analyzeMarket));

/**
 * @route   POST /api/v1/gemini/compare-products
 * @desc    比较多个产品
 * @access  Private
 */
router.post('/compare-products', [
  body('products')
    .isArray({ min: 2, max: 5 })
    .withMessage('产品数量应在2-5个之间'),
  body('compareFields')
    .isArray({ min: 1 })
    .withMessage('比较字段不能为空'),
  validateRequest
], asyncHandler(geminiController.compareProducts));

/**
 * @route   POST /api/v1/gemini/predict-trend
 * @desc    预测价格趋势
 * @access  Private
 */
router.post('/predict-trend', [
  body('productId')
    .notEmpty()
    .withMessage('产品ID不能为空'),
  body('historicalData')
    .isArray({ min: 7 })
    .withMessage('历史数据至少需要7个数据点'),
  body('predictionDays')
    .isInt({ min: 1, max: 90 })
    .withMessage('预测天数应在1-90天之间'),
  validateRequest
], asyncHandler(geminiController.predictTrend));

/**
 * @route   POST /api/v1/gemini/generate-report
 * @desc    生成分析报告
 * @access  Private
 */
router.post('/generate-report', [
  body('analysisId')
    .notEmpty()
    .withMessage('分析ID不能为空'),
  body('reportType')
    .isIn(['summary', 'detailed', 'executive'])
    .withMessage('报告类型不正确'),
  body('includeCharts')
    .optional()
    .isBoolean()
    .withMessage('图表选项必须是布尔值'),
  validateRequest
], asyncHandler(geminiController.generateReport));

/**
 * @route   POST /api/v1/gemini/optimize-listing
 * @desc    优化商品标题和描述
 * @access  Private
 */
router.post('/optimize-listing', [
  body('title')
    .notEmpty()
    .withMessage('商品标题不能为空'),
  body('description')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('商品描述不能超过5000字'),
  body('category')
    .notEmpty()
    .withMessage('商品类别不能为空'),
  body('targetKeywords')
    .isArray({ min: 1 })
    .withMessage('目标关键词不能为空'),
  validateRequest
], asyncHandler(geminiController.optimizeListing));

/**
 * @route   POST /api/v1/gemini/analyze-competitor
 * @desc    分析竞争对手
 * @access  Private
 */
router.post('/analyze-competitor', [
  body('competitorUrl')
    .isURL()
    .withMessage('竞争对手URL格式不正确'),
  body('analysisDepth')
    .isIn(['basic', 'detailed'])
    .withMessage('分析深度不正确'),
  validateRequest
], asyncHandler(geminiController.analyzeCompetitor));

/**
 * @route   GET /api/v1/gemini/models
 * @desc    获取可用的Gemini模型列表
 * @access  Private
 */
router.get('/models', asyncHandler(geminiController.getAvailableModels));

/**
 * @route   GET /api/v1/gemini/usage-stats
 * @desc    获取API使用统计
 * @access  Private
 */
router.get('/usage-stats', asyncHandler(geminiController.getUsageStats));

module.exports = router;
