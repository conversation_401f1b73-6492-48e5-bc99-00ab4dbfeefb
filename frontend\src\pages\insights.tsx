import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';

const InsightsPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');

  if (isLoading) {
    return <LoadingScreen message="加载市场洞察..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <h1 style={{
              fontSize: '2rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              市场洞察
            </h1>
            <p style={{
              fontSize: '1rem',
              color: 'var(--color-gray-600)'
            }}>
              实时市场趋势分析和商业洞察
            </p>
          </div>
        </div>

        {/* 统计概览 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    15
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    市场洞察
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-success-500), var(--color-success-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    82%
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    准确率
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    8
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    机会识别
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 时间范围选择 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: 'var(--color-gray-900)',
                margin: 0
              }}>
                市场趋势分析
              </h2>
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                {['7d', '30d', '90d'].map((range) => (
                  <button
                    key={range}
                    className={`btn ${selectedTimeRange === range ? 'btn-primary' : 'btn-ghost'} btn-sm`}
                    onClick={() => setSelectedTimeRange(range)}
                  >
                    {range === '7d' ? '7天' : range === '30d' ? '30天' : '90天'}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="card">
          <div className="card-body">
            <div style={{
              textAlign: 'center',
              padding: '3rem',
              color: 'var(--color-gray-600)'
            }}>
              <div style={{
                width: '4rem',
                height: '4rem',
                background: 'var(--color-gray-200)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1.5rem'
              }}>
                <svg style={{ width: '2rem', height: '2rem', color: 'var(--color-gray-400)' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                marginBottom: '1rem',
                color: 'var(--color-gray-900)'
              }}>
                市场洞察分析
              </h3>
              <p style={{ marginBottom: '1.5rem' }}>
                基于AI算法的实时市场趋势分析，为您提供精准的商业洞察和决策建议。
              </p>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem',
                marginTop: '2rem'
              }}>
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-gray-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-gray-200)'
                }}>
                  <h4 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>趋势预测</h4>
                  <p style={{ fontSize: '0.875rem' }}>基于历史数据预测市场走势</p>
                </div>
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-gray-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-gray-200)'
                }}>
                  <h4 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>竞品分析</h4>
                  <p style={{ fontSize: '0.875rem' }}>实时监控竞争对手动态</p>
                </div>
                <div style={{
                  padding: '1rem',
                  background: 'var(--color-gray-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-gray-200)'
                }}>
                  <h4 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>机会识别</h4>
                  <p style={{ fontSize: '0.875rem' }}>发现潜在的市场机会</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightsPage;
