import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  LightBulbIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

const InsightsPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');

  if (isLoading) {
    return <LoadingScreen message="加载市场洞察..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 模拟市场洞察数据
  const insights = [
    {
      id: 1,
      type: 'opportunity',
      title: '高端手机市场增长机会',
      description: '根据最新数据分析，高端手机市场在Q1预计增长15%，建议加大iPhone 15 Pro系列的库存投入。',
      impact: 'high',
      category: '市场机会',
      timestamp: '2小时前',
      metrics: {
        potentialRevenue: '¥2,500,000',
        confidence: '85%'
      }
    },
    {
      id: 2,
      type: 'warning',
      title: '笔记本电脑价格波动预警',
      description: 'MacBook Pro系列价格出现异常波动，建议密切关注竞品动态，可能需要调整定价策略。',
      impact: 'medium',
      category: '价格预警',
      timestamp: '4小时前',
      metrics: {
        priceChange: '-3.2%',
        confidence: '78%'
      }
    },
    {
      id: 3,
      type: 'trend',
      title: '无线耳机需求上升趋势',
      description: 'AirPods系列产品搜索量增长25%，用户对降噪功能需求强烈，建议优化产品组合。',
      impact: 'high',
      category: '需求趋势',
      timestamp: '6小时前',
      metrics: {
        searchGrowth: '+25%',
        confidence: '92%'
      }
    },
    {
      id: 4,
      type: 'success',
      title: '小米产品销售表现优异',
      description: '小米14 Ultra在中高端市场表现超预期，销量比预测高出18%，建议继续保持库存充足。',
      impact: 'medium',
      category: '销售成果',
      timestamp: '8小时前',
      metrics: {
        salesGrowth: '+18%',
        confidence: '88%'
      }
    }
  ];

  const marketTrends = [
    {
      category: '手机',
      trend: 'up',
      change: '+12.5%',
      description: '高端机型需求增长'
    },
    {
      category: '笔记本',
      trend: 'down',
      change: '-3.2%',
      description: '价格竞争激烈'
    },
    {
      category: '耳机',
      trend: 'up',
      change: '+25.8%',
      description: '无线降噪需求旺盛'
    },
    {
      category: '平板',
      trend: 'up',
      change: '+8.1%',
      description: '办公需求推动增长'
    }
  ];

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity':
        return <LightBulbIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />;
      case 'warning':
        return <ExclamationTriangleIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />;
      case 'trend':
        return <TrendingUpIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />;
      case 'success':
        return <CheckCircleIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />;
      default:
        return <InformationCircleIcon style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'opportunity':
        return 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))';
      case 'warning':
        return 'linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600))';
      case 'trend':
        return 'linear-gradient(135deg, var(--color-success-500), var(--color-success-600))';
      case 'success':
        return 'linear-gradient(135deg, var(--color-success-500), var(--color-success-600))';
      default:
        return 'linear-gradient(135deg, var(--color-gray-500), var(--color-gray-600))';
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.5rem'
                }}>
                  市场洞察
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  实时市场趋势分析和商业洞察
                </p>
              </div>
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                {['7d', '30d', '90d'].map((range) => (
                  <button
                    key={range}
                    className={`btn ${selectedTimeRange === range ? 'btn-primary' : 'btn-ghost'} btn-sm`}
                    onClick={() => setSelectedTimeRange(range)}
                  >
                    {range === '7d' ? '7天' : range === '30d' ? '30天' : '90天'}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 市场趋势概览 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-header">
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: 'var(--color-gray-900)',
              margin: 0
            }}>
              市场趋势概览
            </h2>
          </div>
          <div className="card-body">
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1.5rem'
            }}>
              {marketTrends.map((trend, index) => (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '1rem',
                  background: 'var(--color-gray-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--color-gray-200)'
                }}>
                  <div>
                    <h3 style={{
                      fontSize: '1.125rem',
                      fontWeight: '600',
                      color: 'var(--color-gray-900)',
                      marginBottom: '0.25rem'
                    }}>
                      {trend.category}
                    </h3>
                    <p style={{
                      fontSize: '0.875rem',
                      color: 'var(--color-gray-600)'
                    }}>
                      {trend.description}
                    </p>
                  </div>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    {trend.trend === 'up' ? (
                      <TrendingUpIcon style={{ 
                        width: '1.25rem', 
                        height: '1.25rem', 
                        color: 'var(--color-success-500)' 
                      }} />
                    ) : (
                      <TrendingDownIcon style={{ 
                        width: '1.25rem', 
                        height: '1.25rem', 
                        color: 'var(--color-error-500)' 
                      }} />
                    )}
                    <span style={{
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: trend.trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)'
                    }}>
                      {trend.change}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 智能洞察 */}
        <div className="card">
          <div className="card-header">
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: 'var(--color-gray-900)',
              margin: 0
            }}>
              智能洞察
            </h2>
          </div>
          <div className="card-body">
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              {insights.map((insight) => (
                <div key={insight.id} className="card">
                  <div className="card-body">
                    <div style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: '1rem'
                    }}>
                      <div style={{
                        width: '3rem',
                        height: '3rem',
                        background: getInsightColor(insight.type),
                        borderRadius: 'var(--radius-lg)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        {getInsightIcon(insight.type)}
                      </div>
                      
                      <div style={{ flex: 1 }}>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'flex-start',
                          marginBottom: '0.5rem'
                        }}>
                          <div>
                            <h3 style={{
                              fontSize: '1.125rem',
                              fontWeight: '600',
                              color: 'var(--color-gray-900)',
                              marginBottom: '0.25rem'
                            }}>
                              {insight.title}
                            </h3>
                            <span className={`badge ${
                              insight.impact === 'high' ? 'badge-error' : 
                              insight.impact === 'medium' ? 'badge-warning' : 'badge-success'
                            }`}>
                              {insight.impact === 'high' ? '高影响' : 
                               insight.impact === 'medium' ? '中影响' : '低影响'}
                            </span>
                          </div>
                          <span style={{
                            fontSize: '0.875rem',
                            color: 'var(--color-gray-500)'
                          }}>
                            {insight.timestamp}
                          </span>
                        </div>
                        
                        <p style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '1rem',
                          lineHeight: '1.5'
                        }}>
                          {insight.description}
                        </p>
                        
                        <div style={{
                          display: 'flex',
                          gap: '2rem',
                          fontSize: '0.875rem'
                        }}>
                          {Object.entries(insight.metrics).map(([key, value]) => (
                            <div key={key}>
                              <span style={{ color: 'var(--color-gray-500)' }}>
                                {key === 'potentialRevenue' ? '潜在收益' :
                                 key === 'confidence' ? '置信度' :
                                 key === 'priceChange' ? '价格变化' :
                                 key === 'searchGrowth' ? '搜索增长' :
                                 key === 'salesGrowth' ? '销量增长' : key}:
                              </span>
                              <span style={{
                                marginLeft: '0.5rem',
                                fontWeight: '600',
                                color: 'var(--color-gray-900)'
                              }}>
                                {value}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightsPage;
