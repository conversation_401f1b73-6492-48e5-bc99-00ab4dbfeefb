#!/bin/bash

# SmartPick 部署脚本
# 用法: ./scripts/deploy.sh [environment]
# 环境: dev, staging, production

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-dev}
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|production)$ ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    log_info "用法: $0 [dev|staging|production]"
    exit 1
fi

log_info "开始部署到 $ENVIRONMENT 环境..."

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    ENV_FILE=".env"
    if [[ "$ENVIRONMENT" == "production" ]]; then
        ENV_FILE=".env.production"
    elif [[ "$ENVIRONMENT" == "staging" ]]; then
        ENV_FILE=".env.staging"
    fi
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "环境变量文件 $ENV_FILE 不存在"
        log_info "请复制 .env.example 并配置相应的环境变量"
        exit 1
    fi
    
    # 检查关键环境变量
    source "$ENV_FILE"
    
    REQUIRED_VARS=(
        "DB_PASSWORD"
        "JWT_SECRET"
        "ENCRYPTION_KEY"
    )
    
    for var in "${REQUIRED_VARS[@]}"; do
        if [[ -z "${!var}" ]]; then
            log_error "环境变量 $var 未设置"
            exit 1
        fi
    done
    
    log_success "环境变量检查完成"
}

# 备份数据库
backup_database() {
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "备份生产数据库..."
        
        BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        docker-compose exec -T postgres pg_dump -U smartpick_user smartpick > "$BACKUP_DIR/database.sql"
        
        if [[ $? -eq 0 ]]; then
            log_success "数据库备份完成: $BACKUP_DIR/database.sql"
        else
            log_error "数据库备份失败"
            exit 1
        fi
    fi
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml build --no-cache
    else
        docker-compose build --no-cache
    fi
    
    if [[ $? -eq 0 ]]; then
        log_success "镜像构建完成"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 停止旧服务
stop_services() {
    log_info "停止现有服务..."
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml down
    else
        docker-compose down
    fi
    
    log_success "服务停止完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        docker-compose up -d
    fi
    
    if [[ $? -eq 0 ]]; then
        log_success "服务启动完成"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待数据库启动
    sleep 10
    
    docker-compose exec backend npm run migrate
    
    if [[ $? -eq 0 ]]; then
        log_success "数据库迁移完成"
    else
        log_error "数据库迁移失败"
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查后端服务
    for i in {1..30}; do
        if curl -f http://localhost:8000/api/v1/health &> /dev/null; then
            log_success "后端服务健康检查通过"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            log_error "后端服务健康检查失败"
            exit 1
        fi
        
        sleep 2
    done
    
    # 检查前端服务
    for i in {1..30}; do
        if curl -f http://localhost:3000 &> /dev/null; then
            log_success "前端服务健康检查通过"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            log_error "前端服务健康检查失败"
            exit 1
        fi
        
        sleep 2
    done
}

# 清理旧镜像
cleanup() {
    log_info "清理旧镜像..."
    
    docker image prune -f
    docker volume prune -f
    
    log_success "清理完成"
}

# 发送通知
send_notification() {
    if [[ -n "$NOTIFICATION_WEBHOOK_URL" ]]; then
        log_info "发送部署通知..."
        
        curl -X POST "$NOTIFICATION_WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{
                \"text\": \"SmartPick 部署完成\",
                \"attachments\": [{
                    \"color\": \"good\",
                    \"fields\": [{
                        \"title\": \"环境\",
                        \"value\": \"$ENVIRONMENT\",
                        \"short\": true
                    }, {
                        \"title\": \"时间\",
                        \"value\": \"$(date)\",
                        \"short\": true
                    }]
                }]
            }" &> /dev/null
        
        log_success "通知发送完成"
    fi
}

# 主部署流程
main() {
    log_info "========================================="
    log_info "SmartPick 部署脚本"
    log_info "环境: $ENVIRONMENT"
    log_info "时间: $(date)"
    log_info "========================================="
    
    check_dependencies
    check_env_vars
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        backup_database
    fi
    
    build_images
    stop_services
    start_services
    run_migrations
    health_check
    cleanup
    send_notification
    
    log_success "========================================="
    log_success "部署完成!"
    log_success "前端地址: http://localhost:3000"
    log_success "后端地址: http://localhost:8000"
    log_success "========================================="
}

# 错误处理
trap 'log_error "部署过程中发生错误，正在回滚..."; docker-compose down; exit 1' ERR

# 执行主流程
main
