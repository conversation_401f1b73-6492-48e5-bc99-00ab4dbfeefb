const express = require('express');
const { body, param } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const userController = require('../controllers/userController');
const { validateRequest } = require('../middleware/validation');
const { authenticate, authorize } = require('../middleware/auth');
const upload = require('../middleware/upload');

const router = express.Router();

// 所有用户路由都需要认证
router.use(authenticate);

/**
 * @route   GET /api/v1/users/profile
 * @desc    获取用户资料
 * @access  Private
 */
router.get('/profile', asyncHandler(userController.getProfile));

/**
 * @route   PUT /api/v1/users/profile
 * @desc    更新用户资料
 * @access  Private
 */
router.put('/profile', [
  body('username')
    .optional()
    .isLength({ min: 2, max: 20 })
    .withMessage('用户名长度为2-20位')
    .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
    .withMessage('用户名只能包含字母、数字、下划线和中文'),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('bio')
    .optional()
    .isLength({ max: 200 })
    .withMessage('个人简介不能超过200字'),
  validateRequest
], asyncHandler(userController.updateProfile));

/**
 * @route   POST /api/v1/users/avatar
 * @desc    上传用户头像
 * @access  Private
 */
router.post('/avatar', 
  upload.single('avatar'),
  asyncHandler(userController.uploadAvatar)
);

/**
 * @route   PUT /api/v1/users/password
 * @desc    修改密码
 * @access  Private
 */
router.put('/password', [
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码不能为空'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('新密码长度至少6位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码必须包含大小写字母和数字'),
  validateRequest
], asyncHandler(userController.changePassword));

/**
 * @route   GET /api/v1/users/gemini-config
 * @desc    获取Gemini API配置
 * @access  Private
 */
router.get('/gemini-config', asyncHandler(userController.getGeminiConfig));

/**
 * @route   PUT /api/v1/users/gemini-config
 * @desc    更新Gemini API配置
 * @access  Private
 */
router.put('/gemini-config', [
  body('apiKey')
    .notEmpty()
    .withMessage('API密钥不能为空')
    .isLength({ min: 10 })
    .withMessage('API密钥格式不正确'),
  body('model')
    .optional()
    .isIn(['gemini-pro', 'gemini-pro-vision'])
    .withMessage('不支持的模型类型'),
  validateRequest
], asyncHandler(userController.updateGeminiConfig));

/**
 * @route   DELETE /api/v1/users/gemini-config
 * @desc    删除Gemini API配置
 * @access  Private
 */
router.delete('/gemini-config', asyncHandler(userController.deleteGeminiConfig));

/**
 * @route   GET /api/v1/users/analysis-history
 * @desc    获取分析历史
 * @access  Private
 */
router.get('/analysis-history', asyncHandler(userController.getAnalysisHistory));

/**
 * @route   GET /api/v1/users/statistics
 * @desc    获取用户统计信息
 * @access  Private
 */
router.get('/statistics', asyncHandler(userController.getUserStatistics));

/**
 * @route   DELETE /api/v1/users/account
 * @desc    删除用户账户
 * @access  Private
 */
router.delete('/account', [
  body('password')
    .notEmpty()
    .withMessage('密码不能为空'),
  validateRequest
], asyncHandler(userController.deleteAccount));

// 管理员路由
/**
 * @route   GET /api/v1/users
 * @desc    获取用户列表（管理员）
 * @access  Private/Admin
 */
router.get('/', authorize('admin'), asyncHandler(userController.getUsers));

/**
 * @route   GET /api/v1/users/:id
 * @desc    获取指定用户信息（管理员）
 * @access  Private/Admin
 */
router.get('/:id', [
  param('id').isUUID().withMessage('用户ID格式不正确'),
  validateRequest,
  authorize('admin')
], asyncHandler(userController.getUserById));

/**
 * @route   PUT /api/v1/users/:id/status
 * @desc    更新用户状态（管理员）
 * @access  Private/Admin
 */
router.put('/:id/status', [
  param('id').isUUID().withMessage('用户ID格式不正确'),
  body('status')
    .isIn(['active', 'inactive', 'banned'])
    .withMessage('用户状态值不正确'),
  validateRequest,
  authorize('admin')
], asyncHandler(userController.updateUserStatus));

module.exports = router;
