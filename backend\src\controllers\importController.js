const multer = require('multer');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { createError } = require('../middleware/errorHandler');
const { prisma } = require('../config/database');
const logger = require('../utils/logger');
const templateService = require('../services/templateService');
const importService = require('../services/importService');
// const Bull = require('bull');

// 创建导入任务队列 (暂时禁用)
// const importQueue = new Bull('import tasks', {
//   redis: {
//     host: process.env.REDIS_HOST || 'localhost',
//     port: process.env.REDIS_PORT || 6379,
//     password: process.env.REDIS_PASSWORD
//   }
// });

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/imports/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = uuidv4();
    const ext = path.extname(file.originalname);
    cb(null, `import_${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
      'application/vnd.ms-excel', // xls
      'text/csv' // csv
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件格式'), false);
    }
  }
});

// 处理导入任务 (暂时禁用)
// importQueue.process('process-import', async (job) => {
//   const { importTaskId, filePath, userId } = job.data;

//   try {
//     const result = await importService.processImport(importTaskId, filePath, userId);
//     return result;
//   } catch (error) {
//     logger.error('导入任务处理失败:', error);
//     throw error;
//   }
// });

const importController = {
  /**
   * 下载Excel模板
   */
  async downloadExcelTemplate(req, res) {
    try {
      const template = await templateService.generateExcelTemplate();
      
      res.setHeader('Content-Type', template.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(template.filename)}"`);
      res.send(template.buffer);
      
      logger.logUserActivity(req.user?.id, 'download_excel_template');
    } catch (error) {
      logger.error('下载Excel模板失败:', error);
      throw error;
    }
  },

  /**
   * 下载CSV模板
   */
  async downloadCSVTemplate(req, res) {
    try {
      const template = await templateService.generateCSVTemplate();
      
      res.setHeader('Content-Type', template.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(template.filename)}"`);
      res.send(template.content);
      
      logger.logUserActivity(req.user?.id, 'download_csv_template');
    } catch (error) {
      logger.error('下载CSV模板失败:', error);
      throw error;
    }
  },

  /**
   * 获取模板字段信息
   */
  async getTemplateFields(req, res) {
    try {
      const fields = templateService.getTemplateFields();
      
      res.json({
        success: true,
        message: '获取模板字段成功',
        data: fields
      });
    } catch (error) {
      logger.error('获取模板字段失败:', error);
      throw error;
    }
  },

  /**
   * 上传并导入数据文件
   */
  async uploadAndImport(req, res) {
    const uploadSingle = upload.single('file');
    
    uploadSingle(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              success: false,
              message: '文件大小超过限制（最大50MB）',
              code: 'FILE_TOO_LARGE'
            });
          }
        }
        return res.status(400).json({
          success: false,
          message: err.message,
          code: 'UPLOAD_ERROR'
        });
      }
      
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '请选择要上传的文件',
          code: 'NO_FILE'
        });
      }
      
      try {
        const userId = req.user.id;
        const file = req.file;
        
        // 验证文件
        const validationErrors = importService.validateFile(file);
        if (validationErrors.length > 0) {
          return res.status(400).json({
            success: false,
            message: validationErrors.join(', '),
            code: 'VALIDATION_ERROR'
          });
        }
        
        // 创建导入任务记录
        const importTask = await prisma.importTask.create({
          data: {
            userId: userId,
            title: `数据导入 - ${file.originalname}`,
            fileName: file.originalname,
            filePath: file.path,
            fileSize: file.size,
            fileType: path.extname(file.originalname).toLowerCase().slice(1).toUpperCase(),
            status: 'PENDING'
          }
        });
        const importTaskId = importTask.id;
        
        // 直接处理导入任务
        try {
          const result = await importService.processImport(importTaskId, file.path, userId);

          logger.logUserActivity(userId, 'upload_import_file', {
            importTaskId,
            fileName: file.originalname,
            fileSize: file.size,
            result
          });

          res.json({
            success: true,
            message: '文件上传并处理成功',
            data: {
              importTaskId,
              fileName: file.originalname,
              fileSize: file.size,
              status: 'completed',
              result
            }
          });
        } catch (importError) {
          logger.error('导入处理失败:', importError);
          res.json({
            success: true,
            message: '文件上传成功，但处理失败',
            data: {
              importTaskId,
              fileName: file.originalname,
              fileSize: file.size,
              status: 'failed',
              error: importError.message
            }
          });
        }
      } catch (error) {
        logger.error('文件上传处理失败:', error);
        throw error;
      }
    });
  },

  /**
   * 获取导入任务状态
   */
  async getImportTaskStatus(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const task = await importService.getImportTaskStatus(id, userId);
      
      res.json({
        success: true,
        message: '获取导入任务状态成功',
        data: task
      });
    } catch (error) {
      logger.error('获取导入任务状态失败:', error);
      throw error;
    }
  },

  /**
   * 获取导入任务列表
   */
  async getImportTasks(req, res) {
    try {
      const userId = req.user.id;
      const { 
        page = 1, 
        limit = 20, 
        status 
      } = req.query;
      
      const result = await importService.getUserImportTasks(userId, {
        page: parseInt(page),
        limit: parseInt(limit),
        status
      });
      
      res.json({
        success: true,
        message: '获取导入任务列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取导入任务列表失败:', error);
      throw error;
    }
  },

  /**
   * 删除导入任务
   */
  async deleteImportTask(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const task = await prisma.importTask.findFirst({
        where: {
          id,
          userId: userId
        }
      });

      if (!task) {
        throw createError.notFound('导入任务不存在', 'TASK_NOT_FOUND');
      }

      // 删除任务记录
      await prisma.importTask.delete({
        where: { id }
      });
      
      logger.logUserActivity(userId, 'delete_import_task', { importTaskId: id });
      
      res.json({
        success: true,
        message: '导入任务已删除'
      });
    } catch (error) {
      logger.error('删除导入任务失败:', error);
      throw error;
    }
  },

  /**
   * 重试失败的导入任务
   */
  async retryImportTask(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const task = await prisma.importTask.findFirst({
        where: {
          id,
          userId: userId
        }
      });

      if (!task) {
        throw createError.notFound('导入任务不存在', 'TASK_NOT_FOUND');
      }

      if (task.status !== 'FAILED') {
        throw createError.badRequest('只能重试失败的任务', 'TASK_NOT_RETRYABLE');
      }

      // 重置任务状态
      await prisma.importTask.update({
        where: { id },
        data: {
          status: 'PENDING',
          startedAt: null,
          completedAt: null,
          errorMessage: null,
          errorDetails: null,
          validationErrors: []
        }
      });
      
      // 直接重新处理导入任务
      try {
        const result = await importService.processImport(id, task.filePath, userId);

        logger.logUserActivity(userId, 'retry_import_task', { importTaskId: id, result });

        res.json({
          success: true,
          message: '导入任务重试成功',
          data: {
            importTaskId: id,
            status: 'completed',
            result
          }
        });
      } catch (retryError) {
        logger.error('重试导入任务失败:', retryError);
        res.json({
          success: false,
          message: '导入任务重试失败',
          data: {
            importTaskId: id,
            status: 'failed',
            error: retryError.message
          }
        });
      }
    } catch (error) {
      logger.error('重试导入任务失败:', error);
      throw error;
    }
  },

  /**
   * 获取导入统计信息
   */
  async getImportStatistics(req, res) {
    try {
      const userId = req.user.id;
      const { period = '30d' } = req.query;
      
      const periodDays = parseInt(period.replace('d', ''));
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - periodDays);
      
      const tasks = await prisma.importTask.findMany({
        where: {
          userId: userId,
          createdAt: {
            gte: startDate
          }
        }
      });

      const stats = {
        total_imports: tasks.length,
        completed_imports: tasks.filter(t => t.status === 'COMPLETED').length,
        failed_imports: tasks.filter(t => t.status === 'FAILED').length,
        total_imported_products: tasks.reduce((sum, t) => sum + (t.successRows || 0), 0),
        avg_duration: tasks.length > 0
          ? tasks.reduce((sum, t) => sum + (t.durationMs || 0), 0) / tasks.length
          : 0
      };

      const recentTasks = await prisma.importTask.findMany({
        where: { userId: userId },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          title: true,
          status: true,
          successRows: true,
          failedRows: true,
          createdAt: true
        }
      });
      
      const result = {
        summary: {
          totalImports: parseInt(stats.total_imports) || 0,
          completedImports: parseInt(stats.completed_imports) || 0,
          failedImports: parseInt(stats.failed_imports) || 0,
          totalImportedProducts: parseInt(stats.total_imported_products) || 0,
          averageDuration: Math.round(parseFloat(stats.avg_duration) || 0),
          successRate: stats.total_imports > 0 
            ? Math.round((stats.completed_imports / stats.total_imports) * 100) 
            : 0
        },
        recentTasks,
        period
      };
      
      res.json({
        success: true,
        message: '获取导入统计成功',
        data: result
      });
    } catch (error) {
      logger.error('获取导入统计失败:', error);
      throw error;
    }
  }
};

module.exports = importController;
