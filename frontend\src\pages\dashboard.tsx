import React from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import { LoadingScreen } from '@/components/LoadingScreen';
import Layout from '@/components/Layout';
import {
  ChartBarIcon,
  ShoppingBagIcon,
  CloudArrowUpIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

const DashboardPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();

  if (isLoading) {
    return <LoadingScreen message="加载仪表板..." />;
  }

  if (!isAuthenticated) {
    return null; // useRequireAuth会处理重定向
  }

  const stats = [
    {
      name: '总产品数',
      value: '12',
      icon: ShoppingBagIcon,
      change: '+4.75%',
      changeType: 'positive',
    },
    {
      name: '分析报告',
      value: '8',
      icon: ChartBarIcon,
      change: '+54.02%',
      changeType: 'positive',
    },
    {
      name: '导入任务',
      value: '3',
      icon: CloudArrowUpIcon,
      change: '-1.39%',
      changeType: 'negative',
    },
    {
      name: '活跃用户',
      value: '2',
      icon: UserGroupIcon,
      change: '+10.18%',
      changeType: 'positive',
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'import',
      title: '数据导入完成',
      description: '成功导入 15 个产品数据',
      time: '2 小时前',
      status: 'success',
    },
    {
      id: 2,
      type: 'analysis',
      title: 'AI分析完成',
      description: 'iPhone 15 Pro Max 市场分析报告已生成',
      time: '4 小时前',
      status: 'success',
    },
    {
      id: 3,
      type: 'product',
      title: '新产品添加',
      description: '小米14 Ultra 已添加到产品库',
      time: '1 天前',
      status: 'info',
    },
  ];

  return (
    <Layout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">仪表板</h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            欢迎回来！这里是您的数据概览。
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <div
              key={stat.name}
              className="relative bg-white dark:bg-gray-800 pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className="absolute bg-primary-500 rounded-md p-3">
                  <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
                <p className="ml-16 text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  {stat.name}
                </p>
              </dt>
              <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stat.value}
                </p>
                <p
                  className={`ml-2 flex items-baseline text-sm font-semibold ${
                    stat.changeType === 'positive'
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  }`}
                >
                  {stat.change}
                </p>
              </dd>
            </div>
          ))}
        </div>

        {/* 最近活动 */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              最近活动
            </h3>
            <div className="mt-6 flow-root">
              <ul className="-mb-8">
                {recentActivities.map((activity, activityIdx) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {activityIdx !== recentActivities.length - 1 ? (
                        <span
                          className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600"
                          aria-hidden="true"
                        />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span
                            className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800 ${
                              activity.status === 'success'
                                ? 'bg-green-500'
                                : activity.status === 'info'
                                ? 'bg-blue-500'
                                : 'bg-gray-500'
                            }`}
                          >
                            {activity.type === 'import' && (
                              <CloudArrowUpIcon className="h-5 w-5 text-white" />
                            )}
                            {activity.type === 'analysis' && (
                              <ChartBarIcon className="h-5 w-5 text-white" />
                            )}
                            {activity.type === 'product' && (
                              <ShoppingBagIcon className="h-5 w-5 text-white" />
                            )}
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-900 dark:text-white">
                              {activity.title}
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {activity.description}
                            </p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                            <time>{activity.time}</time>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              快速操作
            </h3>
            <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <button className="relative group bg-white dark:bg-gray-700 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-primary-500 dark:hover:border-primary-400">
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400 ring-4 ring-white dark:ring-gray-700">
                    <CloudArrowUpIcon className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    导入数据
                  </h3>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    上传Excel或CSV文件批量导入产品数据
                  </p>
                </div>
              </button>

              <button className="relative group bg-white dark:bg-gray-700 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-primary-500 dark:hover:border-primary-400">
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400 ring-4 ring-white dark:ring-gray-700">
                    <ChartBarIcon className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    AI分析
                  </h3>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    使用AI分析产品市场竞争力和选品建议
                  </p>
                </div>
              </button>

              <button className="relative group bg-white dark:bg-gray-700 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-primary-500 dark:hover:border-primary-400">
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400 ring-4 ring-white dark:ring-gray-700">
                    <ShoppingBagIcon className="h-6 w-6" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    产品管理
                  </h3>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    查看和管理您的产品库存和信息
                  </p>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default DashboardPage;
