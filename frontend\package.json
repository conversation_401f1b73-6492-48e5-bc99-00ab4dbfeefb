{"name": "smartpick-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true next build"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.2", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "zustand": "^4.4.7", "axios": "^1.6.2", "@tanstack/react-query": "^5.8.4", "recharts": "^2.8.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "framer-motion": "^10.16.5", "react-hot-toast": "^2.4.1", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "socket.io-client": "^4.7.4"}, "devDependencies": {"eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "@next/bundle-analyzer": "^14.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}