{"name": "smartpick-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true next build"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.8.4", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-table": "^7.7.18", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "chart.js": "^4.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "next": "^14.0.3", "postcss": "^8.4.31", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-table": "^7.8.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwindcss": "^3.3.6", "typescript": "^5.3.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}