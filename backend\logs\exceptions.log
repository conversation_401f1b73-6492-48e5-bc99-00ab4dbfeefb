{"date":"Mon Jul 28 2025 10:36:13 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7233.484},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18812,"external":2278282,"heapTotal":70725632,"heapUsed":44813496,"rss":108097536},"pid":19180,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 10:37:19 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7298.656},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18812,"external":2278282,"heapTotal":70987776,"heapUsed":44593816,"rss":111366144},"pid":20452,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 10:37:24 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7304.531},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18812,"external":2278282,"heapTotal":70987776,"heapUsed":44541352,"rss":108998656},"pid":11428,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 10:37:33 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7313.453},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18812,"external":2278282,"heapTotal":71249920,"heapUsed":44340456,"rss":108728320},"pid":15668,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 10:37:50 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7329.921},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18812,"external":2278282,"heapTotal":70987776,"heapUsed":44753024,"rss":108544000},"pid":7984,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 10:38:05 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7345.203},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18636,"external":2251951,"heapTotal":70987776,"heapUsed":44452528,"rss":107036672},"pid":18636,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 10:39:34 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7434.156},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18635,"external":2251990,"heapTotal":32190464,"heapUsed":16156832,"rss":58388480},"pid":19040,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 10:39:47 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7447.593},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18635,"external":2251990,"heapTotal":32452608,"heapUsed":17027592,"rss":58429440},"pid":16516,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 10:41:50 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":7570.062},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18635,"external":2251990,"heapTotal":32190464,"heapUsed":16584616,"rss":58294272},"pid":20592,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 11:10:11 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":9270.968},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\simple-server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18675,"external":2146690,"heapTotal":22491136,"heapUsed":16376488,"rss":65245184},"pid":20448,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 11:10:24 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":9284.531},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\simple-server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18635,"external":2146690,"heapTotal":31666176,"heapUsed":12651136,"rss":65789952},"pid":19260,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 11:12:24 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":9403.937},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\simple-server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18675,"external":2146690,"heapTotal":22491136,"heapUsed":16464136,"rss":64892928},"pid":16836,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 11:12:39 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":9419.437},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\simple-server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18675,"external":2146690,"heapTotal":22753280,"heapUsed":16401496,"rss":64888832},"pid":11880,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 11:14:02 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":9502.343},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\simple-server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18675,"external":2146690,"heapTotal":22491136,"heapUsed":16433752,"rss":65142784},"pid":22616,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 11:14:22 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":9522.468},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\simple-server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18675,"external":2146690,"heapTotal":22491136,"heapUsed":16455864,"rss":65228800},"pid":8996,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 11:31:58 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":10577.687},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\simple-server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18675,"external":2146690,"heapTotal":22491136,"heapUsed":16471408,"rss":56180736},"pid":7196,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
{"date":"Mon Jul 28 2025 11:32:46 GMT+0800 (中国标准时间)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\ai-product-select\\backend\\src\\routes\\auth.js","D:\\ai-product-select\\backend\\simple-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\nError: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":10626.515},"process":{"argv":["C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","D:\\ai-product-select\\backend\\simple-server.js"],"cwd":"D:\\ai-product-select\\backend","execPath":"C:\\Users\\<USER>\\scoop\\apps\\nodejs\\current\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":18635,"external":2146690,"heapTotal":31928320,"heapUsed":12650328,"rss":57143296},"pid":22140,"uid":null,"version":"v23.11.0"},"stack":"Error: Cannot find module '../controllers/authController'\nRequire stack:\n- D:\\ai-product-select\\backend\\src\\routes\\auth.js\n- D:\\ai-product-select\\backend\\simple-server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Function._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\ai-product-select\\backend\\src\\routes\\auth.js:4:24)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":24,"file":"D:\\ai-product-select\\backend\\src\\routes\\auth.js","function":null,"line":4,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
