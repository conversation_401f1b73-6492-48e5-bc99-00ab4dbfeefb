
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model Product
 * 
 */
export type Product = $Result.DefaultSelection<Prisma.$ProductPayload>
/**
 * Model AnalysisResult
 * 
 */
export type AnalysisResult = $Result.DefaultSelection<Prisma.$AnalysisResultPayload>
/**
 * Model ImportTask
 * 
 */
export type ImportTask = $Result.DefaultSelection<Prisma.$ImportTaskPayload>

/**
 * ##  Prisma Client ʲˢ
 * 
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   * 
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): void;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb, ExtArgs>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs>;

  /**
   * `prisma.product`: Exposes CRUD operations for the **Product** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Products
    * const products = await prisma.product.findMany()
    * ```
    */
  get product(): Prisma.ProductDelegate<ExtArgs>;

  /**
   * `prisma.analysisResult`: Exposes CRUD operations for the **AnalysisResult** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more AnalysisResults
    * const analysisResults = await prisma.analysisResult.findMany()
    * ```
    */
  get analysisResult(): Prisma.AnalysisResultDelegate<ExtArgs>;

  /**
   * `prisma.importTask`: Exposes CRUD operations for the **ImportTask** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ImportTasks
    * const importTasks = await prisma.importTask.findMany()
    * ```
    */
  get importTask(): Prisma.ImportTaskDelegate<ExtArgs>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError
  export import NotFoundError = runtime.NotFoundError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics 
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 5.22.0
   * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion 

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? K : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    Product: 'Product',
    AnalysisResult: 'AnalysisResult',
    ImportTask: 'ImportTask'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb extends $Utils.Fn<{extArgs: $Extensions.InternalArgs, clientOptions: PrismaClientOptions }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], this['params']['clientOptions']>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, ClientOptions = {}> = {
    meta: {
      modelProps: "user" | "product" | "analysisResult" | "importTask"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      Product: {
        payload: Prisma.$ProductPayload<ExtArgs>
        fields: Prisma.ProductFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ProductFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ProductFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          findFirst: {
            args: Prisma.ProductFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ProductFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          findMany: {
            args: Prisma.ProductFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>[]
          }
          create: {
            args: Prisma.ProductCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          createMany: {
            args: Prisma.ProductCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ProductCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>[]
          }
          delete: {
            args: Prisma.ProductDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          update: {
            args: Prisma.ProductUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          deleteMany: {
            args: Prisma.ProductDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ProductUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.ProductUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductPayload>
          }
          aggregate: {
            args: Prisma.ProductAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateProduct>
          }
          groupBy: {
            args: Prisma.ProductGroupByArgs<ExtArgs>
            result: $Utils.Optional<ProductGroupByOutputType>[]
          }
          count: {
            args: Prisma.ProductCountArgs<ExtArgs>
            result: $Utils.Optional<ProductCountAggregateOutputType> | number
          }
        }
      }
      AnalysisResult: {
        payload: Prisma.$AnalysisResultPayload<ExtArgs>
        fields: Prisma.AnalysisResultFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AnalysisResultFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AnalysisResultFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload>
          }
          findFirst: {
            args: Prisma.AnalysisResultFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AnalysisResultFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload>
          }
          findMany: {
            args: Prisma.AnalysisResultFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload>[]
          }
          create: {
            args: Prisma.AnalysisResultCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload>
          }
          createMany: {
            args: Prisma.AnalysisResultCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AnalysisResultCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload>[]
          }
          delete: {
            args: Prisma.AnalysisResultDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload>
          }
          update: {
            args: Prisma.AnalysisResultUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload>
          }
          deleteMany: {
            args: Prisma.AnalysisResultDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AnalysisResultUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.AnalysisResultUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AnalysisResultPayload>
          }
          aggregate: {
            args: Prisma.AnalysisResultAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAnalysisResult>
          }
          groupBy: {
            args: Prisma.AnalysisResultGroupByArgs<ExtArgs>
            result: $Utils.Optional<AnalysisResultGroupByOutputType>[]
          }
          count: {
            args: Prisma.AnalysisResultCountArgs<ExtArgs>
            result: $Utils.Optional<AnalysisResultCountAggregateOutputType> | number
          }
        }
      }
      ImportTask: {
        payload: Prisma.$ImportTaskPayload<ExtArgs>
        fields: Prisma.ImportTaskFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ImportTaskFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ImportTaskFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload>
          }
          findFirst: {
            args: Prisma.ImportTaskFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ImportTaskFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload>
          }
          findMany: {
            args: Prisma.ImportTaskFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload>[]
          }
          create: {
            args: Prisma.ImportTaskCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload>
          }
          createMany: {
            args: Prisma.ImportTaskCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ImportTaskCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload>[]
          }
          delete: {
            args: Prisma.ImportTaskDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload>
          }
          update: {
            args: Prisma.ImportTaskUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload>
          }
          deleteMany: {
            args: Prisma.ImportTaskDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ImportTaskUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.ImportTaskUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ImportTaskPayload>
          }
          aggregate: {
            args: Prisma.ImportTaskAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateImportTask>
          }
          groupBy: {
            args: Prisma.ImportTaskGroupByArgs<ExtArgs>
            result: $Utils.Optional<ImportTaskGroupByOutputType>[]
          }
          count: {
            args: Prisma.ImportTaskCountArgs<ExtArgs>
            result: $Utils.Optional<ImportTaskCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
  }


  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    products: number
    analysisResults: number
    importTasks: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    products?: boolean | UserCountOutputTypeCountProductsArgs
    analysisResults?: boolean | UserCountOutputTypeCountAnalysisResultsArgs
    importTasks?: boolean | UserCountOutputTypeCountImportTasksArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountProductsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ProductWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountAnalysisResultsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AnalysisResultWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountImportTasksArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ImportTaskWhereInput
  }


  /**
   * Count Type ProductCountOutputType
   */

  export type ProductCountOutputType = {
    analysisResults: number
  }

  export type ProductCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    analysisResults?: boolean | ProductCountOutputTypeCountAnalysisResultsArgs
  }

  // Custom InputTypes
  /**
   * ProductCountOutputType without action
   */
  export type ProductCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductCountOutputType
     */
    select?: ProductCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * ProductCountOutputType without action
   */
  export type ProductCountOutputTypeCountAnalysisResultsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AnalysisResultWhereInput
  }


  /**
   * Count Type ImportTaskCountOutputType
   */

  export type ImportTaskCountOutputType = {
    products: number
  }

  export type ImportTaskCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    products?: boolean | ImportTaskCountOutputTypeCountProductsArgs
  }

  // Custom InputTypes
  /**
   * ImportTaskCountOutputType without action
   */
  export type ImportTaskCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTaskCountOutputType
     */
    select?: ImportTaskCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * ImportTaskCountOutputType without action
   */
  export type ImportTaskCountOutputTypeCountProductsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ProductWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserAvgAggregateOutputType = {
    loginCount: number | null
  }

  export type UserSumAggregateOutputType = {
    loginCount: number | null
  }

  export type UserMinAggregateOutputType = {
    id: string | null
    email: string | null
    username: string | null
    password: string | null
    firstName: string | null
    lastName: string | null
    avatar: string | null
    phone: string | null
    role: string | null
    status: string | null
    emailVerified: boolean | null
    emailVerifiedAt: Date | null
    lastLoginAt: Date | null
    loginCount: number | null
    preferences: string | null
    settings: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: string | null
    email: string | null
    username: string | null
    password: string | null
    firstName: string | null
    lastName: string | null
    avatar: string | null
    phone: string | null
    role: string | null
    status: string | null
    emailVerified: boolean | null
    emailVerifiedAt: Date | null
    lastLoginAt: Date | null
    loginCount: number | null
    preferences: string | null
    settings: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    email: number
    username: number
    password: number
    firstName: number
    lastName: number
    avatar: number
    phone: number
    role: number
    status: number
    emailVerified: number
    emailVerifiedAt: number
    lastLoginAt: number
    loginCount: number
    preferences: number
    settings: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserAvgAggregateInputType = {
    loginCount?: true
  }

  export type UserSumAggregateInputType = {
    loginCount?: true
  }

  export type UserMinAggregateInputType = {
    id?: true
    email?: true
    username?: true
    password?: true
    firstName?: true
    lastName?: true
    avatar?: true
    phone?: true
    role?: true
    status?: true
    emailVerified?: true
    emailVerifiedAt?: true
    lastLoginAt?: true
    loginCount?: true
    preferences?: true
    settings?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    email?: true
    username?: true
    password?: true
    firstName?: true
    lastName?: true
    avatar?: true
    phone?: true
    role?: true
    status?: true
    emailVerified?: true
    emailVerifiedAt?: true
    lastLoginAt?: true
    loginCount?: true
    preferences?: true
    settings?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    email?: true
    username?: true
    password?: true
    firstName?: true
    lastName?: true
    avatar?: true
    phone?: true
    role?: true
    status?: true
    emailVerified?: true
    emailVerifiedAt?: true
    lastLoginAt?: true
    loginCount?: true
    preferences?: true
    settings?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: UserAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: UserSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _avg?: UserAvgAggregateInputType
    _sum?: UserSumAggregateInputType
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: string
    email: string
    username: string
    password: string
    firstName: string | null
    lastName: string | null
    avatar: string | null
    phone: string | null
    role: string
    status: string
    emailVerified: boolean
    emailVerifiedAt: Date | null
    lastLoginAt: Date | null
    loginCount: number
    preferences: string | null
    settings: string | null
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    username?: boolean
    password?: boolean
    firstName?: boolean
    lastName?: boolean
    avatar?: boolean
    phone?: boolean
    role?: boolean
    status?: boolean
    emailVerified?: boolean
    emailVerifiedAt?: boolean
    lastLoginAt?: boolean
    loginCount?: boolean
    preferences?: boolean
    settings?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    products?: boolean | User$productsArgs<ExtArgs>
    analysisResults?: boolean | User$analysisResultsArgs<ExtArgs>
    importTasks?: boolean | User$importTasksArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    username?: boolean
    password?: boolean
    firstName?: boolean
    lastName?: boolean
    avatar?: boolean
    phone?: boolean
    role?: boolean
    status?: boolean
    emailVerified?: boolean
    emailVerifiedAt?: boolean
    lastLoginAt?: boolean
    loginCount?: boolean
    preferences?: boolean
    settings?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    email?: boolean
    username?: boolean
    password?: boolean
    firstName?: boolean
    lastName?: boolean
    avatar?: boolean
    phone?: boolean
    role?: boolean
    status?: boolean
    emailVerified?: boolean
    emailVerifiedAt?: boolean
    lastLoginAt?: boolean
    loginCount?: boolean
    preferences?: boolean
    settings?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    products?: boolean | User$productsArgs<ExtArgs>
    analysisResults?: boolean | User$analysisResultsArgs<ExtArgs>
    importTasks?: boolean | User$importTasksArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      products: Prisma.$ProductPayload<ExtArgs>[]
      analysisResults: Prisma.$AnalysisResultPayload<ExtArgs>[]
      importTasks: Prisma.$ImportTaskPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string
      username: string
      password: string
      firstName: string | null
      lastName: string | null
      avatar: string | null
      phone: string | null
      role: string
      status: string
      emailVerified: boolean
      emailVerifiedAt: Date | null
      lastLoginAt: Date | null
      loginCount: number
      preferences: string | null
      settings: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    products<T extends User$productsArgs<ExtArgs> = {}>(args?: Subset<T, User$productsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findMany"> | Null>
    analysisResults<T extends User$analysisResultsArgs<ExtArgs> = {}>(args?: Subset<T, User$analysisResultsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "findMany"> | Null>
    importTasks<T extends User$importTasksArgs<ExtArgs> = {}>(args?: Subset<T, User$importTasksArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */ 
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly username: FieldRef<"User", 'String'>
    readonly password: FieldRef<"User", 'String'>
    readonly firstName: FieldRef<"User", 'String'>
    readonly lastName: FieldRef<"User", 'String'>
    readonly avatar: FieldRef<"User", 'String'>
    readonly phone: FieldRef<"User", 'String'>
    readonly role: FieldRef<"User", 'String'>
    readonly status: FieldRef<"User", 'String'>
    readonly emailVerified: FieldRef<"User", 'Boolean'>
    readonly emailVerifiedAt: FieldRef<"User", 'DateTime'>
    readonly lastLoginAt: FieldRef<"User", 'DateTime'>
    readonly loginCount: FieldRef<"User", 'Int'>
    readonly preferences: FieldRef<"User", 'String'>
    readonly settings: FieldRef<"User", 'String'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
  }

  /**
   * User.products
   */
  export type User$productsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    where?: ProductWhereInput
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    cursor?: ProductWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ProductScalarFieldEnum | ProductScalarFieldEnum[]
  }

  /**
   * User.analysisResults
   */
  export type User$analysisResultsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    where?: AnalysisResultWhereInput
    orderBy?: AnalysisResultOrderByWithRelationInput | AnalysisResultOrderByWithRelationInput[]
    cursor?: AnalysisResultWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AnalysisResultScalarFieldEnum | AnalysisResultScalarFieldEnum[]
  }

  /**
   * User.importTasks
   */
  export type User$importTasksArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    where?: ImportTaskWhereInput
    orderBy?: ImportTaskOrderByWithRelationInput | ImportTaskOrderByWithRelationInput[]
    cursor?: ImportTaskWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ImportTaskScalarFieldEnum | ImportTaskScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model Product
   */

  export type AggregateProduct = {
    _count: ProductCountAggregateOutputType | null
    _avg: ProductAvgAggregateOutputType | null
    _sum: ProductSumAggregateOutputType | null
    _min: ProductMinAggregateOutputType | null
    _max: ProductMaxAggregateOutputType | null
  }

  export type ProductAvgAggregateOutputType = {
    currentPrice: number | null
    originalPrice: number | null
    minPrice: number | null
    maxPrice: number | null
    salesCount: number | null
    rating: number | null
    reviewCount: number | null
    stockQuantity: number | null
    importCount: number | null
    analysisScore: number | null
  }

  export type ProductSumAggregateOutputType = {
    currentPrice: number | null
    originalPrice: number | null
    minPrice: number | null
    maxPrice: number | null
    salesCount: number | null
    rating: number | null
    reviewCount: number | null
    stockQuantity: number | null
    importCount: number | null
    analysisScore: number | null
  }

  export type ProductMinAggregateOutputType = {
    id: string | null
    userId: string | null
    title: string | null
    description: string | null
    url: string | null
    imageUrl: string | null
    brand: string | null
    model: string | null
    platform: string | null
    platformProductId: string | null
    shopName: string | null
    shopUrl: string | null
    importTaskId: string | null
    dataSource: string | null
    currentPrice: number | null
    originalPrice: number | null
    minPrice: number | null
    maxPrice: number | null
    currency: string | null
    salesCount: number | null
    rating: number | null
    reviewCount: number | null
    stockQuantity: number | null
    category: string | null
    subcategory: string | null
    tags: string | null
    attributes: string | null
    status: string | null
    isTracked: boolean | null
    isAnalyzed: boolean | null
    lastImportedAt: Date | null
    importCount: number | null
    importErrors: string | null
    analysisSummary: string | null
    analysisScore: number | null
    marketPosition: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ProductMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    title: string | null
    description: string | null
    url: string | null
    imageUrl: string | null
    brand: string | null
    model: string | null
    platform: string | null
    platformProductId: string | null
    shopName: string | null
    shopUrl: string | null
    importTaskId: string | null
    dataSource: string | null
    currentPrice: number | null
    originalPrice: number | null
    minPrice: number | null
    maxPrice: number | null
    currency: string | null
    salesCount: number | null
    rating: number | null
    reviewCount: number | null
    stockQuantity: number | null
    category: string | null
    subcategory: string | null
    tags: string | null
    attributes: string | null
    status: string | null
    isTracked: boolean | null
    isAnalyzed: boolean | null
    lastImportedAt: Date | null
    importCount: number | null
    importErrors: string | null
    analysisSummary: string | null
    analysisScore: number | null
    marketPosition: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ProductCountAggregateOutputType = {
    id: number
    userId: number
    title: number
    description: number
    url: number
    imageUrl: number
    brand: number
    model: number
    platform: number
    platformProductId: number
    shopName: number
    shopUrl: number
    importTaskId: number
    dataSource: number
    currentPrice: number
    originalPrice: number
    minPrice: number
    maxPrice: number
    currency: number
    salesCount: number
    rating: number
    reviewCount: number
    stockQuantity: number
    category: number
    subcategory: number
    tags: number
    attributes: number
    status: number
    isTracked: number
    isAnalyzed: number
    lastImportedAt: number
    importCount: number
    importErrors: number
    analysisSummary: number
    analysisScore: number
    marketPosition: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type ProductAvgAggregateInputType = {
    currentPrice?: true
    originalPrice?: true
    minPrice?: true
    maxPrice?: true
    salesCount?: true
    rating?: true
    reviewCount?: true
    stockQuantity?: true
    importCount?: true
    analysisScore?: true
  }

  export type ProductSumAggregateInputType = {
    currentPrice?: true
    originalPrice?: true
    minPrice?: true
    maxPrice?: true
    salesCount?: true
    rating?: true
    reviewCount?: true
    stockQuantity?: true
    importCount?: true
    analysisScore?: true
  }

  export type ProductMinAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    description?: true
    url?: true
    imageUrl?: true
    brand?: true
    model?: true
    platform?: true
    platformProductId?: true
    shopName?: true
    shopUrl?: true
    importTaskId?: true
    dataSource?: true
    currentPrice?: true
    originalPrice?: true
    minPrice?: true
    maxPrice?: true
    currency?: true
    salesCount?: true
    rating?: true
    reviewCount?: true
    stockQuantity?: true
    category?: true
    subcategory?: true
    tags?: true
    attributes?: true
    status?: true
    isTracked?: true
    isAnalyzed?: true
    lastImportedAt?: true
    importCount?: true
    importErrors?: true
    analysisSummary?: true
    analysisScore?: true
    marketPosition?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ProductMaxAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    description?: true
    url?: true
    imageUrl?: true
    brand?: true
    model?: true
    platform?: true
    platformProductId?: true
    shopName?: true
    shopUrl?: true
    importTaskId?: true
    dataSource?: true
    currentPrice?: true
    originalPrice?: true
    minPrice?: true
    maxPrice?: true
    currency?: true
    salesCount?: true
    rating?: true
    reviewCount?: true
    stockQuantity?: true
    category?: true
    subcategory?: true
    tags?: true
    attributes?: true
    status?: true
    isTracked?: true
    isAnalyzed?: true
    lastImportedAt?: true
    importCount?: true
    importErrors?: true
    analysisSummary?: true
    analysisScore?: true
    marketPosition?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ProductCountAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    description?: true
    url?: true
    imageUrl?: true
    brand?: true
    model?: true
    platform?: true
    platformProductId?: true
    shopName?: true
    shopUrl?: true
    importTaskId?: true
    dataSource?: true
    currentPrice?: true
    originalPrice?: true
    minPrice?: true
    maxPrice?: true
    currency?: true
    salesCount?: true
    rating?: true
    reviewCount?: true
    stockQuantity?: true
    category?: true
    subcategory?: true
    tags?: true
    attributes?: true
    status?: true
    isTracked?: true
    isAnalyzed?: true
    lastImportedAt?: true
    importCount?: true
    importErrors?: true
    analysisSummary?: true
    analysisScore?: true
    marketPosition?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type ProductAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Product to aggregate.
     */
    where?: ProductWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Products to fetch.
     */
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ProductWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Products from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Products.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Products
    **/
    _count?: true | ProductCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ProductAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ProductSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ProductMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ProductMaxAggregateInputType
  }

  export type GetProductAggregateType<T extends ProductAggregateArgs> = {
        [P in keyof T & keyof AggregateProduct]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateProduct[P]>
      : GetScalarType<T[P], AggregateProduct[P]>
  }




  export type ProductGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ProductWhereInput
    orderBy?: ProductOrderByWithAggregationInput | ProductOrderByWithAggregationInput[]
    by: ProductScalarFieldEnum[] | ProductScalarFieldEnum
    having?: ProductScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ProductCountAggregateInputType | true
    _avg?: ProductAvgAggregateInputType
    _sum?: ProductSumAggregateInputType
    _min?: ProductMinAggregateInputType
    _max?: ProductMaxAggregateInputType
  }

  export type ProductGroupByOutputType = {
    id: string
    userId: string
    title: string
    description: string | null
    url: string
    imageUrl: string | null
    brand: string | null
    model: string | null
    platform: string
    platformProductId: string | null
    shopName: string | null
    shopUrl: string | null
    importTaskId: string | null
    dataSource: string
    currentPrice: number | null
    originalPrice: number | null
    minPrice: number | null
    maxPrice: number | null
    currency: string
    salesCount: number | null
    rating: number | null
    reviewCount: number | null
    stockQuantity: number | null
    category: string | null
    subcategory: string | null
    tags: string | null
    attributes: string | null
    status: string
    isTracked: boolean
    isAnalyzed: boolean
    lastImportedAt: Date | null
    importCount: number
    importErrors: string | null
    analysisSummary: string | null
    analysisScore: number | null
    marketPosition: string | null
    createdAt: Date
    updatedAt: Date
    _count: ProductCountAggregateOutputType | null
    _avg: ProductAvgAggregateOutputType | null
    _sum: ProductSumAggregateOutputType | null
    _min: ProductMinAggregateOutputType | null
    _max: ProductMaxAggregateOutputType | null
  }

  type GetProductGroupByPayload<T extends ProductGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ProductGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ProductGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ProductGroupByOutputType[P]>
            : GetScalarType<T[P], ProductGroupByOutputType[P]>
        }
      >
    >


  export type ProductSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    description?: boolean
    url?: boolean
    imageUrl?: boolean
    brand?: boolean
    model?: boolean
    platform?: boolean
    platformProductId?: boolean
    shopName?: boolean
    shopUrl?: boolean
    importTaskId?: boolean
    dataSource?: boolean
    currentPrice?: boolean
    originalPrice?: boolean
    minPrice?: boolean
    maxPrice?: boolean
    currency?: boolean
    salesCount?: boolean
    rating?: boolean
    reviewCount?: boolean
    stockQuantity?: boolean
    category?: boolean
    subcategory?: boolean
    tags?: boolean
    attributes?: boolean
    status?: boolean
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: boolean
    importCount?: boolean
    importErrors?: boolean
    analysisSummary?: boolean
    analysisScore?: boolean
    marketPosition?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    importTask?: boolean | Product$importTaskArgs<ExtArgs>
    analysisResults?: boolean | Product$analysisResultsArgs<ExtArgs>
    _count?: boolean | ProductCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["product"]>

  export type ProductSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    description?: boolean
    url?: boolean
    imageUrl?: boolean
    brand?: boolean
    model?: boolean
    platform?: boolean
    platformProductId?: boolean
    shopName?: boolean
    shopUrl?: boolean
    importTaskId?: boolean
    dataSource?: boolean
    currentPrice?: boolean
    originalPrice?: boolean
    minPrice?: boolean
    maxPrice?: boolean
    currency?: boolean
    salesCount?: boolean
    rating?: boolean
    reviewCount?: boolean
    stockQuantity?: boolean
    category?: boolean
    subcategory?: boolean
    tags?: boolean
    attributes?: boolean
    status?: boolean
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: boolean
    importCount?: boolean
    importErrors?: boolean
    analysisSummary?: boolean
    analysisScore?: boolean
    marketPosition?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    importTask?: boolean | Product$importTaskArgs<ExtArgs>
  }, ExtArgs["result"]["product"]>

  export type ProductSelectScalar = {
    id?: boolean
    userId?: boolean
    title?: boolean
    description?: boolean
    url?: boolean
    imageUrl?: boolean
    brand?: boolean
    model?: boolean
    platform?: boolean
    platformProductId?: boolean
    shopName?: boolean
    shopUrl?: boolean
    importTaskId?: boolean
    dataSource?: boolean
    currentPrice?: boolean
    originalPrice?: boolean
    minPrice?: boolean
    maxPrice?: boolean
    currency?: boolean
    salesCount?: boolean
    rating?: boolean
    reviewCount?: boolean
    stockQuantity?: boolean
    category?: boolean
    subcategory?: boolean
    tags?: boolean
    attributes?: boolean
    status?: boolean
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: boolean
    importCount?: boolean
    importErrors?: boolean
    analysisSummary?: boolean
    analysisScore?: boolean
    marketPosition?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type ProductInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    importTask?: boolean | Product$importTaskArgs<ExtArgs>
    analysisResults?: boolean | Product$analysisResultsArgs<ExtArgs>
    _count?: boolean | ProductCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type ProductIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    importTask?: boolean | Product$importTaskArgs<ExtArgs>
  }

  export type $ProductPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Product"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      importTask: Prisma.$ImportTaskPayload<ExtArgs> | null
      analysisResults: Prisma.$AnalysisResultPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      title: string
      description: string | null
      url: string
      imageUrl: string | null
      brand: string | null
      model: string | null
      platform: string
      platformProductId: string | null
      shopName: string | null
      shopUrl: string | null
      importTaskId: string | null
      dataSource: string
      currentPrice: number | null
      originalPrice: number | null
      minPrice: number | null
      maxPrice: number | null
      currency: string
      salesCount: number | null
      rating: number | null
      reviewCount: number | null
      stockQuantity: number | null
      category: string | null
      subcategory: string | null
      tags: string | null
      attributes: string | null
      status: string
      isTracked: boolean
      isAnalyzed: boolean
      lastImportedAt: Date | null
      importCount: number
      importErrors: string | null
      analysisSummary: string | null
      analysisScore: number | null
      marketPosition: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["product"]>
    composites: {}
  }

  type ProductGetPayload<S extends boolean | null | undefined | ProductDefaultArgs> = $Result.GetResult<Prisma.$ProductPayload, S>

  type ProductCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<ProductFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: ProductCountAggregateInputType | true
    }

  export interface ProductDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Product'], meta: { name: 'Product' } }
    /**
     * Find zero or one Product that matches the filter.
     * @param {ProductFindUniqueArgs} args - Arguments to find a Product
     * @example
     * // Get one Product
     * const product = await prisma.product.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ProductFindUniqueArgs>(args: SelectSubset<T, ProductFindUniqueArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one Product that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {ProductFindUniqueOrThrowArgs} args - Arguments to find a Product
     * @example
     * // Get one Product
     * const product = await prisma.product.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ProductFindUniqueOrThrowArgs>(args: SelectSubset<T, ProductFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first Product that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductFindFirstArgs} args - Arguments to find a Product
     * @example
     * // Get one Product
     * const product = await prisma.product.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ProductFindFirstArgs>(args?: SelectSubset<T, ProductFindFirstArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first Product that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductFindFirstOrThrowArgs} args - Arguments to find a Product
     * @example
     * // Get one Product
     * const product = await prisma.product.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ProductFindFirstOrThrowArgs>(args?: SelectSubset<T, ProductFindFirstOrThrowArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Products that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Products
     * const products = await prisma.product.findMany()
     * 
     * // Get first 10 Products
     * const products = await prisma.product.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const productWithIdOnly = await prisma.product.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ProductFindManyArgs>(args?: SelectSubset<T, ProductFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a Product.
     * @param {ProductCreateArgs} args - Arguments to create a Product.
     * @example
     * // Create one Product
     * const Product = await prisma.product.create({
     *   data: {
     *     // ... data to create a Product
     *   }
     * })
     * 
     */
    create<T extends ProductCreateArgs>(args: SelectSubset<T, ProductCreateArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Products.
     * @param {ProductCreateManyArgs} args - Arguments to create many Products.
     * @example
     * // Create many Products
     * const product = await prisma.product.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ProductCreateManyArgs>(args?: SelectSubset<T, ProductCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Products and returns the data saved in the database.
     * @param {ProductCreateManyAndReturnArgs} args - Arguments to create many Products.
     * @example
     * // Create many Products
     * const product = await prisma.product.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Products and only return the `id`
     * const productWithIdOnly = await prisma.product.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ProductCreateManyAndReturnArgs>(args?: SelectSubset<T, ProductCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a Product.
     * @param {ProductDeleteArgs} args - Arguments to delete one Product.
     * @example
     * // Delete one Product
     * const Product = await prisma.product.delete({
     *   where: {
     *     // ... filter to delete one Product
     *   }
     * })
     * 
     */
    delete<T extends ProductDeleteArgs>(args: SelectSubset<T, ProductDeleteArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one Product.
     * @param {ProductUpdateArgs} args - Arguments to update one Product.
     * @example
     * // Update one Product
     * const product = await prisma.product.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ProductUpdateArgs>(args: SelectSubset<T, ProductUpdateArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Products.
     * @param {ProductDeleteManyArgs} args - Arguments to filter Products to delete.
     * @example
     * // Delete a few Products
     * const { count } = await prisma.product.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ProductDeleteManyArgs>(args?: SelectSubset<T, ProductDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Products.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Products
     * const product = await prisma.product.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ProductUpdateManyArgs>(args: SelectSubset<T, ProductUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Product.
     * @param {ProductUpsertArgs} args - Arguments to update or create a Product.
     * @example
     * // Update or create a Product
     * const product = await prisma.product.upsert({
     *   create: {
     *     // ... data to create a Product
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Product we want to update
     *   }
     * })
     */
    upsert<T extends ProductUpsertArgs>(args: SelectSubset<T, ProductUpsertArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Products.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductCountArgs} args - Arguments to filter Products to count.
     * @example
     * // Count the number of Products
     * const count = await prisma.product.count({
     *   where: {
     *     // ... the filter for the Products we want to count
     *   }
     * })
    **/
    count<T extends ProductCountArgs>(
      args?: Subset<T, ProductCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ProductCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Product.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ProductAggregateArgs>(args: Subset<T, ProductAggregateArgs>): Prisma.PrismaPromise<GetProductAggregateType<T>>

    /**
     * Group by Product.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ProductGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ProductGroupByArgs['orderBy'] }
        : { orderBy?: ProductGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ProductGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProductGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Product model
   */
  readonly fields: ProductFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Product.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ProductClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    importTask<T extends Product$importTaskArgs<ExtArgs> = {}>(args?: Subset<T, Product$importTaskArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "findUniqueOrThrow"> | null, null, ExtArgs>
    analysisResults<T extends Product$analysisResultsArgs<ExtArgs> = {}>(args?: Subset<T, Product$analysisResultsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Product model
   */ 
  interface ProductFieldRefs {
    readonly id: FieldRef<"Product", 'String'>
    readonly userId: FieldRef<"Product", 'String'>
    readonly title: FieldRef<"Product", 'String'>
    readonly description: FieldRef<"Product", 'String'>
    readonly url: FieldRef<"Product", 'String'>
    readonly imageUrl: FieldRef<"Product", 'String'>
    readonly brand: FieldRef<"Product", 'String'>
    readonly model: FieldRef<"Product", 'String'>
    readonly platform: FieldRef<"Product", 'String'>
    readonly platformProductId: FieldRef<"Product", 'String'>
    readonly shopName: FieldRef<"Product", 'String'>
    readonly shopUrl: FieldRef<"Product", 'String'>
    readonly importTaskId: FieldRef<"Product", 'String'>
    readonly dataSource: FieldRef<"Product", 'String'>
    readonly currentPrice: FieldRef<"Product", 'Float'>
    readonly originalPrice: FieldRef<"Product", 'Float'>
    readonly minPrice: FieldRef<"Product", 'Float'>
    readonly maxPrice: FieldRef<"Product", 'Float'>
    readonly currency: FieldRef<"Product", 'String'>
    readonly salesCount: FieldRef<"Product", 'Int'>
    readonly rating: FieldRef<"Product", 'Float'>
    readonly reviewCount: FieldRef<"Product", 'Int'>
    readonly stockQuantity: FieldRef<"Product", 'Int'>
    readonly category: FieldRef<"Product", 'String'>
    readonly subcategory: FieldRef<"Product", 'String'>
    readonly tags: FieldRef<"Product", 'String'>
    readonly attributes: FieldRef<"Product", 'String'>
    readonly status: FieldRef<"Product", 'String'>
    readonly isTracked: FieldRef<"Product", 'Boolean'>
    readonly isAnalyzed: FieldRef<"Product", 'Boolean'>
    readonly lastImportedAt: FieldRef<"Product", 'DateTime'>
    readonly importCount: FieldRef<"Product", 'Int'>
    readonly importErrors: FieldRef<"Product", 'String'>
    readonly analysisSummary: FieldRef<"Product", 'String'>
    readonly analysisScore: FieldRef<"Product", 'Float'>
    readonly marketPosition: FieldRef<"Product", 'String'>
    readonly createdAt: FieldRef<"Product", 'DateTime'>
    readonly updatedAt: FieldRef<"Product", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Product findUnique
   */
  export type ProductFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Product to fetch.
     */
    where: ProductWhereUniqueInput
  }

  /**
   * Product findUniqueOrThrow
   */
  export type ProductFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Product to fetch.
     */
    where: ProductWhereUniqueInput
  }

  /**
   * Product findFirst
   */
  export type ProductFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Product to fetch.
     */
    where?: ProductWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Products to fetch.
     */
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Products.
     */
    cursor?: ProductWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Products from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Products.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Products.
     */
    distinct?: ProductScalarFieldEnum | ProductScalarFieldEnum[]
  }

  /**
   * Product findFirstOrThrow
   */
  export type ProductFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Product to fetch.
     */
    where?: ProductWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Products to fetch.
     */
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Products.
     */
    cursor?: ProductWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Products from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Products.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Products.
     */
    distinct?: ProductScalarFieldEnum | ProductScalarFieldEnum[]
  }

  /**
   * Product findMany
   */
  export type ProductFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter, which Products to fetch.
     */
    where?: ProductWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Products to fetch.
     */
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Products.
     */
    cursor?: ProductWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Products from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Products.
     */
    skip?: number
    distinct?: ProductScalarFieldEnum | ProductScalarFieldEnum[]
  }

  /**
   * Product create
   */
  export type ProductCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * The data needed to create a Product.
     */
    data: XOR<ProductCreateInput, ProductUncheckedCreateInput>
  }

  /**
   * Product createMany
   */
  export type ProductCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Products.
     */
    data: ProductCreateManyInput | ProductCreateManyInput[]
  }

  /**
   * Product createManyAndReturn
   */
  export type ProductCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Products.
     */
    data: ProductCreateManyInput | ProductCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Product update
   */
  export type ProductUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * The data needed to update a Product.
     */
    data: XOR<ProductUpdateInput, ProductUncheckedUpdateInput>
    /**
     * Choose, which Product to update.
     */
    where: ProductWhereUniqueInput
  }

  /**
   * Product updateMany
   */
  export type ProductUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Products.
     */
    data: XOR<ProductUpdateManyMutationInput, ProductUncheckedUpdateManyInput>
    /**
     * Filter which Products to update
     */
    where?: ProductWhereInput
  }

  /**
   * Product upsert
   */
  export type ProductUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * The filter to search for the Product to update in case it exists.
     */
    where: ProductWhereUniqueInput
    /**
     * In case the Product found by the `where` argument doesn't exist, create a new Product with this data.
     */
    create: XOR<ProductCreateInput, ProductUncheckedCreateInput>
    /**
     * In case the Product was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ProductUpdateInput, ProductUncheckedUpdateInput>
  }

  /**
   * Product delete
   */
  export type ProductDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    /**
     * Filter which Product to delete.
     */
    where: ProductWhereUniqueInput
  }

  /**
   * Product deleteMany
   */
  export type ProductDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Products to delete
     */
    where?: ProductWhereInput
  }

  /**
   * Product.importTask
   */
  export type Product$importTaskArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    where?: ImportTaskWhereInput
  }

  /**
   * Product.analysisResults
   */
  export type Product$analysisResultsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    where?: AnalysisResultWhereInput
    orderBy?: AnalysisResultOrderByWithRelationInput | AnalysisResultOrderByWithRelationInput[]
    cursor?: AnalysisResultWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AnalysisResultScalarFieldEnum | AnalysisResultScalarFieldEnum[]
  }

  /**
   * Product without action
   */
  export type ProductDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
  }


  /**
   * Model AnalysisResult
   */

  export type AggregateAnalysisResult = {
    _count: AnalysisResultCountAggregateOutputType | null
    _avg: AnalysisResultAvgAggregateOutputType | null
    _sum: AnalysisResultSumAggregateOutputType | null
    _min: AnalysisResultMinAggregateOutputType | null
    _max: AnalysisResultMaxAggregateOutputType | null
  }

  export type AnalysisResultAvgAggregateOutputType = {
    confidenceScore: number | null
    durationMs: number | null
    retryCount: number | null
    viewCount: number | null
    shareCount: number | null
    exportCount: number | null
  }

  export type AnalysisResultSumAggregateOutputType = {
    confidenceScore: number | null
    durationMs: number | null
    retryCount: number | null
    viewCount: number | null
    shareCount: number | null
    exportCount: number | null
  }

  export type AnalysisResultMinAggregateOutputType = {
    id: string | null
    userId: string | null
    productId: string | null
    title: string | null
    description: string | null
    type: string | null
    status: string | null
    priority: string | null
    config: string | null
    inputData: string | null
    outputData: string | null
    summary: string | null
    insights: string | null
    recommendations: string | null
    chartsData: string | null
    confidenceScore: number | null
    startedAt: Date | null
    completedAt: Date | null
    durationMs: number | null
    errorMessage: string | null
    errorDetails: string | null
    retryCount: number | null
    viewCount: number | null
    shareCount: number | null
    exportCount: number | null
    lastViewedAt: Date | null
    shareToken: string | null
    shareType: string | null
    shareExpiresAt: Date | null
    tags: string | null
    category: string | null
    isFavorite: boolean | null
    isArchived: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AnalysisResultMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    productId: string | null
    title: string | null
    description: string | null
    type: string | null
    status: string | null
    priority: string | null
    config: string | null
    inputData: string | null
    outputData: string | null
    summary: string | null
    insights: string | null
    recommendations: string | null
    chartsData: string | null
    confidenceScore: number | null
    startedAt: Date | null
    completedAt: Date | null
    durationMs: number | null
    errorMessage: string | null
    errorDetails: string | null
    retryCount: number | null
    viewCount: number | null
    shareCount: number | null
    exportCount: number | null
    lastViewedAt: Date | null
    shareToken: string | null
    shareType: string | null
    shareExpiresAt: Date | null
    tags: string | null
    category: string | null
    isFavorite: boolean | null
    isArchived: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AnalysisResultCountAggregateOutputType = {
    id: number
    userId: number
    productId: number
    title: number
    description: number
    type: number
    status: number
    priority: number
    config: number
    inputData: number
    outputData: number
    summary: number
    insights: number
    recommendations: number
    chartsData: number
    confidenceScore: number
    startedAt: number
    completedAt: number
    durationMs: number
    errorMessage: number
    errorDetails: number
    retryCount: number
    viewCount: number
    shareCount: number
    exportCount: number
    lastViewedAt: number
    shareToken: number
    shareType: number
    shareExpiresAt: number
    tags: number
    category: number
    isFavorite: number
    isArchived: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type AnalysisResultAvgAggregateInputType = {
    confidenceScore?: true
    durationMs?: true
    retryCount?: true
    viewCount?: true
    shareCount?: true
    exportCount?: true
  }

  export type AnalysisResultSumAggregateInputType = {
    confidenceScore?: true
    durationMs?: true
    retryCount?: true
    viewCount?: true
    shareCount?: true
    exportCount?: true
  }

  export type AnalysisResultMinAggregateInputType = {
    id?: true
    userId?: true
    productId?: true
    title?: true
    description?: true
    type?: true
    status?: true
    priority?: true
    config?: true
    inputData?: true
    outputData?: true
    summary?: true
    insights?: true
    recommendations?: true
    chartsData?: true
    confidenceScore?: true
    startedAt?: true
    completedAt?: true
    durationMs?: true
    errorMessage?: true
    errorDetails?: true
    retryCount?: true
    viewCount?: true
    shareCount?: true
    exportCount?: true
    lastViewedAt?: true
    shareToken?: true
    shareType?: true
    shareExpiresAt?: true
    tags?: true
    category?: true
    isFavorite?: true
    isArchived?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AnalysisResultMaxAggregateInputType = {
    id?: true
    userId?: true
    productId?: true
    title?: true
    description?: true
    type?: true
    status?: true
    priority?: true
    config?: true
    inputData?: true
    outputData?: true
    summary?: true
    insights?: true
    recommendations?: true
    chartsData?: true
    confidenceScore?: true
    startedAt?: true
    completedAt?: true
    durationMs?: true
    errorMessage?: true
    errorDetails?: true
    retryCount?: true
    viewCount?: true
    shareCount?: true
    exportCount?: true
    lastViewedAt?: true
    shareToken?: true
    shareType?: true
    shareExpiresAt?: true
    tags?: true
    category?: true
    isFavorite?: true
    isArchived?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AnalysisResultCountAggregateInputType = {
    id?: true
    userId?: true
    productId?: true
    title?: true
    description?: true
    type?: true
    status?: true
    priority?: true
    config?: true
    inputData?: true
    outputData?: true
    summary?: true
    insights?: true
    recommendations?: true
    chartsData?: true
    confidenceScore?: true
    startedAt?: true
    completedAt?: true
    durationMs?: true
    errorMessage?: true
    errorDetails?: true
    retryCount?: true
    viewCount?: true
    shareCount?: true
    exportCount?: true
    lastViewedAt?: true
    shareToken?: true
    shareType?: true
    shareExpiresAt?: true
    tags?: true
    category?: true
    isFavorite?: true
    isArchived?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type AnalysisResultAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AnalysisResult to aggregate.
     */
    where?: AnalysisResultWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AnalysisResults to fetch.
     */
    orderBy?: AnalysisResultOrderByWithRelationInput | AnalysisResultOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AnalysisResultWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AnalysisResults from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AnalysisResults.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned AnalysisResults
    **/
    _count?: true | AnalysisResultCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: AnalysisResultAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: AnalysisResultSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AnalysisResultMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AnalysisResultMaxAggregateInputType
  }

  export type GetAnalysisResultAggregateType<T extends AnalysisResultAggregateArgs> = {
        [P in keyof T & keyof AggregateAnalysisResult]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAnalysisResult[P]>
      : GetScalarType<T[P], AggregateAnalysisResult[P]>
  }




  export type AnalysisResultGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AnalysisResultWhereInput
    orderBy?: AnalysisResultOrderByWithAggregationInput | AnalysisResultOrderByWithAggregationInput[]
    by: AnalysisResultScalarFieldEnum[] | AnalysisResultScalarFieldEnum
    having?: AnalysisResultScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AnalysisResultCountAggregateInputType | true
    _avg?: AnalysisResultAvgAggregateInputType
    _sum?: AnalysisResultSumAggregateInputType
    _min?: AnalysisResultMinAggregateInputType
    _max?: AnalysisResultMaxAggregateInputType
  }

  export type AnalysisResultGroupByOutputType = {
    id: string
    userId: string
    productId: string | null
    title: string
    description: string | null
    type: string
    status: string
    priority: string
    config: string | null
    inputData: string | null
    outputData: string | null
    summary: string | null
    insights: string | null
    recommendations: string | null
    chartsData: string | null
    confidenceScore: number | null
    startedAt: Date | null
    completedAt: Date | null
    durationMs: number | null
    errorMessage: string | null
    errorDetails: string | null
    retryCount: number
    viewCount: number
    shareCount: number
    exportCount: number
    lastViewedAt: Date | null
    shareToken: string | null
    shareType: string
    shareExpiresAt: Date | null
    tags: string | null
    category: string | null
    isFavorite: boolean
    isArchived: boolean
    createdAt: Date
    updatedAt: Date
    _count: AnalysisResultCountAggregateOutputType | null
    _avg: AnalysisResultAvgAggregateOutputType | null
    _sum: AnalysisResultSumAggregateOutputType | null
    _min: AnalysisResultMinAggregateOutputType | null
    _max: AnalysisResultMaxAggregateOutputType | null
  }

  type GetAnalysisResultGroupByPayload<T extends AnalysisResultGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AnalysisResultGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AnalysisResultGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AnalysisResultGroupByOutputType[P]>
            : GetScalarType<T[P], AnalysisResultGroupByOutputType[P]>
        }
      >
    >


  export type AnalysisResultSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    productId?: boolean
    title?: boolean
    description?: boolean
    type?: boolean
    status?: boolean
    priority?: boolean
    config?: boolean
    inputData?: boolean
    outputData?: boolean
    summary?: boolean
    insights?: boolean
    recommendations?: boolean
    chartsData?: boolean
    confidenceScore?: boolean
    startedAt?: boolean
    completedAt?: boolean
    durationMs?: boolean
    errorMessage?: boolean
    errorDetails?: boolean
    retryCount?: boolean
    viewCount?: boolean
    shareCount?: boolean
    exportCount?: boolean
    lastViewedAt?: boolean
    shareToken?: boolean
    shareType?: boolean
    shareExpiresAt?: boolean
    tags?: boolean
    category?: boolean
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    product?: boolean | AnalysisResult$productArgs<ExtArgs>
  }, ExtArgs["result"]["analysisResult"]>

  export type AnalysisResultSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    productId?: boolean
    title?: boolean
    description?: boolean
    type?: boolean
    status?: boolean
    priority?: boolean
    config?: boolean
    inputData?: boolean
    outputData?: boolean
    summary?: boolean
    insights?: boolean
    recommendations?: boolean
    chartsData?: boolean
    confidenceScore?: boolean
    startedAt?: boolean
    completedAt?: boolean
    durationMs?: boolean
    errorMessage?: boolean
    errorDetails?: boolean
    retryCount?: boolean
    viewCount?: boolean
    shareCount?: boolean
    exportCount?: boolean
    lastViewedAt?: boolean
    shareToken?: boolean
    shareType?: boolean
    shareExpiresAt?: boolean
    tags?: boolean
    category?: boolean
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    product?: boolean | AnalysisResult$productArgs<ExtArgs>
  }, ExtArgs["result"]["analysisResult"]>

  export type AnalysisResultSelectScalar = {
    id?: boolean
    userId?: boolean
    productId?: boolean
    title?: boolean
    description?: boolean
    type?: boolean
    status?: boolean
    priority?: boolean
    config?: boolean
    inputData?: boolean
    outputData?: boolean
    summary?: boolean
    insights?: boolean
    recommendations?: boolean
    chartsData?: boolean
    confidenceScore?: boolean
    startedAt?: boolean
    completedAt?: boolean
    durationMs?: boolean
    errorMessage?: boolean
    errorDetails?: boolean
    retryCount?: boolean
    viewCount?: boolean
    shareCount?: boolean
    exportCount?: boolean
    lastViewedAt?: boolean
    shareToken?: boolean
    shareType?: boolean
    shareExpiresAt?: boolean
    tags?: boolean
    category?: boolean
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type AnalysisResultInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    product?: boolean | AnalysisResult$productArgs<ExtArgs>
  }
  export type AnalysisResultIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    product?: boolean | AnalysisResult$productArgs<ExtArgs>
  }

  export type $AnalysisResultPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "AnalysisResult"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      product: Prisma.$ProductPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      productId: string | null
      title: string
      description: string | null
      type: string
      status: string
      priority: string
      config: string | null
      inputData: string | null
      outputData: string | null
      summary: string | null
      insights: string | null
      recommendations: string | null
      chartsData: string | null
      confidenceScore: number | null
      startedAt: Date | null
      completedAt: Date | null
      durationMs: number | null
      errorMessage: string | null
      errorDetails: string | null
      retryCount: number
      viewCount: number
      shareCount: number
      exportCount: number
      lastViewedAt: Date | null
      shareToken: string | null
      shareType: string
      shareExpiresAt: Date | null
      tags: string | null
      category: string | null
      isFavorite: boolean
      isArchived: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["analysisResult"]>
    composites: {}
  }

  type AnalysisResultGetPayload<S extends boolean | null | undefined | AnalysisResultDefaultArgs> = $Result.GetResult<Prisma.$AnalysisResultPayload, S>

  type AnalysisResultCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<AnalysisResultFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: AnalysisResultCountAggregateInputType | true
    }

  export interface AnalysisResultDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AnalysisResult'], meta: { name: 'AnalysisResult' } }
    /**
     * Find zero or one AnalysisResult that matches the filter.
     * @param {AnalysisResultFindUniqueArgs} args - Arguments to find a AnalysisResult
     * @example
     * // Get one AnalysisResult
     * const analysisResult = await prisma.analysisResult.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AnalysisResultFindUniqueArgs>(args: SelectSubset<T, AnalysisResultFindUniqueArgs<ExtArgs>>): Prisma__AnalysisResultClient<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one AnalysisResult that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {AnalysisResultFindUniqueOrThrowArgs} args - Arguments to find a AnalysisResult
     * @example
     * // Get one AnalysisResult
     * const analysisResult = await prisma.analysisResult.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AnalysisResultFindUniqueOrThrowArgs>(args: SelectSubset<T, AnalysisResultFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AnalysisResultClient<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first AnalysisResult that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AnalysisResultFindFirstArgs} args - Arguments to find a AnalysisResult
     * @example
     * // Get one AnalysisResult
     * const analysisResult = await prisma.analysisResult.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AnalysisResultFindFirstArgs>(args?: SelectSubset<T, AnalysisResultFindFirstArgs<ExtArgs>>): Prisma__AnalysisResultClient<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first AnalysisResult that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AnalysisResultFindFirstOrThrowArgs} args - Arguments to find a AnalysisResult
     * @example
     * // Get one AnalysisResult
     * const analysisResult = await prisma.analysisResult.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AnalysisResultFindFirstOrThrowArgs>(args?: SelectSubset<T, AnalysisResultFindFirstOrThrowArgs<ExtArgs>>): Prisma__AnalysisResultClient<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more AnalysisResults that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AnalysisResultFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all AnalysisResults
     * const analysisResults = await prisma.analysisResult.findMany()
     * 
     * // Get first 10 AnalysisResults
     * const analysisResults = await prisma.analysisResult.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const analysisResultWithIdOnly = await prisma.analysisResult.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AnalysisResultFindManyArgs>(args?: SelectSubset<T, AnalysisResultFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a AnalysisResult.
     * @param {AnalysisResultCreateArgs} args - Arguments to create a AnalysisResult.
     * @example
     * // Create one AnalysisResult
     * const AnalysisResult = await prisma.analysisResult.create({
     *   data: {
     *     // ... data to create a AnalysisResult
     *   }
     * })
     * 
     */
    create<T extends AnalysisResultCreateArgs>(args: SelectSubset<T, AnalysisResultCreateArgs<ExtArgs>>): Prisma__AnalysisResultClient<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many AnalysisResults.
     * @param {AnalysisResultCreateManyArgs} args - Arguments to create many AnalysisResults.
     * @example
     * // Create many AnalysisResults
     * const analysisResult = await prisma.analysisResult.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AnalysisResultCreateManyArgs>(args?: SelectSubset<T, AnalysisResultCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many AnalysisResults and returns the data saved in the database.
     * @param {AnalysisResultCreateManyAndReturnArgs} args - Arguments to create many AnalysisResults.
     * @example
     * // Create many AnalysisResults
     * const analysisResult = await prisma.analysisResult.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many AnalysisResults and only return the `id`
     * const analysisResultWithIdOnly = await prisma.analysisResult.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AnalysisResultCreateManyAndReturnArgs>(args?: SelectSubset<T, AnalysisResultCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a AnalysisResult.
     * @param {AnalysisResultDeleteArgs} args - Arguments to delete one AnalysisResult.
     * @example
     * // Delete one AnalysisResult
     * const AnalysisResult = await prisma.analysisResult.delete({
     *   where: {
     *     // ... filter to delete one AnalysisResult
     *   }
     * })
     * 
     */
    delete<T extends AnalysisResultDeleteArgs>(args: SelectSubset<T, AnalysisResultDeleteArgs<ExtArgs>>): Prisma__AnalysisResultClient<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one AnalysisResult.
     * @param {AnalysisResultUpdateArgs} args - Arguments to update one AnalysisResult.
     * @example
     * // Update one AnalysisResult
     * const analysisResult = await prisma.analysisResult.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AnalysisResultUpdateArgs>(args: SelectSubset<T, AnalysisResultUpdateArgs<ExtArgs>>): Prisma__AnalysisResultClient<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more AnalysisResults.
     * @param {AnalysisResultDeleteManyArgs} args - Arguments to filter AnalysisResults to delete.
     * @example
     * // Delete a few AnalysisResults
     * const { count } = await prisma.analysisResult.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AnalysisResultDeleteManyArgs>(args?: SelectSubset<T, AnalysisResultDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AnalysisResults.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AnalysisResultUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many AnalysisResults
     * const analysisResult = await prisma.analysisResult.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AnalysisResultUpdateManyArgs>(args: SelectSubset<T, AnalysisResultUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one AnalysisResult.
     * @param {AnalysisResultUpsertArgs} args - Arguments to update or create a AnalysisResult.
     * @example
     * // Update or create a AnalysisResult
     * const analysisResult = await prisma.analysisResult.upsert({
     *   create: {
     *     // ... data to create a AnalysisResult
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the AnalysisResult we want to update
     *   }
     * })
     */
    upsert<T extends AnalysisResultUpsertArgs>(args: SelectSubset<T, AnalysisResultUpsertArgs<ExtArgs>>): Prisma__AnalysisResultClient<$Result.GetResult<Prisma.$AnalysisResultPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of AnalysisResults.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AnalysisResultCountArgs} args - Arguments to filter AnalysisResults to count.
     * @example
     * // Count the number of AnalysisResults
     * const count = await prisma.analysisResult.count({
     *   where: {
     *     // ... the filter for the AnalysisResults we want to count
     *   }
     * })
    **/
    count<T extends AnalysisResultCountArgs>(
      args?: Subset<T, AnalysisResultCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AnalysisResultCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a AnalysisResult.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AnalysisResultAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AnalysisResultAggregateArgs>(args: Subset<T, AnalysisResultAggregateArgs>): Prisma.PrismaPromise<GetAnalysisResultAggregateType<T>>

    /**
     * Group by AnalysisResult.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AnalysisResultGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AnalysisResultGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AnalysisResultGroupByArgs['orderBy'] }
        : { orderBy?: AnalysisResultGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AnalysisResultGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAnalysisResultGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the AnalysisResult model
   */
  readonly fields: AnalysisResultFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for AnalysisResult.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AnalysisResultClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    product<T extends AnalysisResult$productArgs<ExtArgs> = {}>(args?: Subset<T, AnalysisResult$productArgs<ExtArgs>>): Prisma__ProductClient<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUniqueOrThrow"> | null, null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the AnalysisResult model
   */ 
  interface AnalysisResultFieldRefs {
    readonly id: FieldRef<"AnalysisResult", 'String'>
    readonly userId: FieldRef<"AnalysisResult", 'String'>
    readonly productId: FieldRef<"AnalysisResult", 'String'>
    readonly title: FieldRef<"AnalysisResult", 'String'>
    readonly description: FieldRef<"AnalysisResult", 'String'>
    readonly type: FieldRef<"AnalysisResult", 'String'>
    readonly status: FieldRef<"AnalysisResult", 'String'>
    readonly priority: FieldRef<"AnalysisResult", 'String'>
    readonly config: FieldRef<"AnalysisResult", 'String'>
    readonly inputData: FieldRef<"AnalysisResult", 'String'>
    readonly outputData: FieldRef<"AnalysisResult", 'String'>
    readonly summary: FieldRef<"AnalysisResult", 'String'>
    readonly insights: FieldRef<"AnalysisResult", 'String'>
    readonly recommendations: FieldRef<"AnalysisResult", 'String'>
    readonly chartsData: FieldRef<"AnalysisResult", 'String'>
    readonly confidenceScore: FieldRef<"AnalysisResult", 'Float'>
    readonly startedAt: FieldRef<"AnalysisResult", 'DateTime'>
    readonly completedAt: FieldRef<"AnalysisResult", 'DateTime'>
    readonly durationMs: FieldRef<"AnalysisResult", 'Int'>
    readonly errorMessage: FieldRef<"AnalysisResult", 'String'>
    readonly errorDetails: FieldRef<"AnalysisResult", 'String'>
    readonly retryCount: FieldRef<"AnalysisResult", 'Int'>
    readonly viewCount: FieldRef<"AnalysisResult", 'Int'>
    readonly shareCount: FieldRef<"AnalysisResult", 'Int'>
    readonly exportCount: FieldRef<"AnalysisResult", 'Int'>
    readonly lastViewedAt: FieldRef<"AnalysisResult", 'DateTime'>
    readonly shareToken: FieldRef<"AnalysisResult", 'String'>
    readonly shareType: FieldRef<"AnalysisResult", 'String'>
    readonly shareExpiresAt: FieldRef<"AnalysisResult", 'DateTime'>
    readonly tags: FieldRef<"AnalysisResult", 'String'>
    readonly category: FieldRef<"AnalysisResult", 'String'>
    readonly isFavorite: FieldRef<"AnalysisResult", 'Boolean'>
    readonly isArchived: FieldRef<"AnalysisResult", 'Boolean'>
    readonly createdAt: FieldRef<"AnalysisResult", 'DateTime'>
    readonly updatedAt: FieldRef<"AnalysisResult", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * AnalysisResult findUnique
   */
  export type AnalysisResultFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * Filter, which AnalysisResult to fetch.
     */
    where: AnalysisResultWhereUniqueInput
  }

  /**
   * AnalysisResult findUniqueOrThrow
   */
  export type AnalysisResultFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * Filter, which AnalysisResult to fetch.
     */
    where: AnalysisResultWhereUniqueInput
  }

  /**
   * AnalysisResult findFirst
   */
  export type AnalysisResultFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * Filter, which AnalysisResult to fetch.
     */
    where?: AnalysisResultWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AnalysisResults to fetch.
     */
    orderBy?: AnalysisResultOrderByWithRelationInput | AnalysisResultOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AnalysisResults.
     */
    cursor?: AnalysisResultWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AnalysisResults from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AnalysisResults.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AnalysisResults.
     */
    distinct?: AnalysisResultScalarFieldEnum | AnalysisResultScalarFieldEnum[]
  }

  /**
   * AnalysisResult findFirstOrThrow
   */
  export type AnalysisResultFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * Filter, which AnalysisResult to fetch.
     */
    where?: AnalysisResultWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AnalysisResults to fetch.
     */
    orderBy?: AnalysisResultOrderByWithRelationInput | AnalysisResultOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AnalysisResults.
     */
    cursor?: AnalysisResultWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AnalysisResults from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AnalysisResults.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AnalysisResults.
     */
    distinct?: AnalysisResultScalarFieldEnum | AnalysisResultScalarFieldEnum[]
  }

  /**
   * AnalysisResult findMany
   */
  export type AnalysisResultFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * Filter, which AnalysisResults to fetch.
     */
    where?: AnalysisResultWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AnalysisResults to fetch.
     */
    orderBy?: AnalysisResultOrderByWithRelationInput | AnalysisResultOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing AnalysisResults.
     */
    cursor?: AnalysisResultWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AnalysisResults from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AnalysisResults.
     */
    skip?: number
    distinct?: AnalysisResultScalarFieldEnum | AnalysisResultScalarFieldEnum[]
  }

  /**
   * AnalysisResult create
   */
  export type AnalysisResultCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * The data needed to create a AnalysisResult.
     */
    data: XOR<AnalysisResultCreateInput, AnalysisResultUncheckedCreateInput>
  }

  /**
   * AnalysisResult createMany
   */
  export type AnalysisResultCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many AnalysisResults.
     */
    data: AnalysisResultCreateManyInput | AnalysisResultCreateManyInput[]
  }

  /**
   * AnalysisResult createManyAndReturn
   */
  export type AnalysisResultCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many AnalysisResults.
     */
    data: AnalysisResultCreateManyInput | AnalysisResultCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * AnalysisResult update
   */
  export type AnalysisResultUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * The data needed to update a AnalysisResult.
     */
    data: XOR<AnalysisResultUpdateInput, AnalysisResultUncheckedUpdateInput>
    /**
     * Choose, which AnalysisResult to update.
     */
    where: AnalysisResultWhereUniqueInput
  }

  /**
   * AnalysisResult updateMany
   */
  export type AnalysisResultUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update AnalysisResults.
     */
    data: XOR<AnalysisResultUpdateManyMutationInput, AnalysisResultUncheckedUpdateManyInput>
    /**
     * Filter which AnalysisResults to update
     */
    where?: AnalysisResultWhereInput
  }

  /**
   * AnalysisResult upsert
   */
  export type AnalysisResultUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * The filter to search for the AnalysisResult to update in case it exists.
     */
    where: AnalysisResultWhereUniqueInput
    /**
     * In case the AnalysisResult found by the `where` argument doesn't exist, create a new AnalysisResult with this data.
     */
    create: XOR<AnalysisResultCreateInput, AnalysisResultUncheckedCreateInput>
    /**
     * In case the AnalysisResult was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AnalysisResultUpdateInput, AnalysisResultUncheckedUpdateInput>
  }

  /**
   * AnalysisResult delete
   */
  export type AnalysisResultDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
    /**
     * Filter which AnalysisResult to delete.
     */
    where: AnalysisResultWhereUniqueInput
  }

  /**
   * AnalysisResult deleteMany
   */
  export type AnalysisResultDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AnalysisResults to delete
     */
    where?: AnalysisResultWhereInput
  }

  /**
   * AnalysisResult.product
   */
  export type AnalysisResult$productArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    where?: ProductWhereInput
  }

  /**
   * AnalysisResult without action
   */
  export type AnalysisResultDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AnalysisResult
     */
    select?: AnalysisResultSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AnalysisResultInclude<ExtArgs> | null
  }


  /**
   * Model ImportTask
   */

  export type AggregateImportTask = {
    _count: ImportTaskCountAggregateOutputType | null
    _avg: ImportTaskAvgAggregateOutputType | null
    _sum: ImportTaskSumAggregateOutputType | null
    _min: ImportTaskMinAggregateOutputType | null
    _max: ImportTaskMaxAggregateOutputType | null
  }

  export type ImportTaskAvgAggregateOutputType = {
    fileSize: number | null
    durationMs: number | null
    totalRows: number | null
    processedRows: number | null
    successRows: number | null
    failedRows: number | null
  }

  export type ImportTaskSumAggregateOutputType = {
    fileSize: number | null
    durationMs: number | null
    totalRows: number | null
    processedRows: number | null
    successRows: number | null
    failedRows: number | null
  }

  export type ImportTaskMinAggregateOutputType = {
    id: string | null
    userId: string | null
    title: string | null
    description: string | null
    fileName: string | null
    filePath: string | null
    fileSize: number | null
    fileType: string | null
    status: string | null
    startedAt: Date | null
    completedAt: Date | null
    durationMs: number | null
    totalRows: number | null
    processedRows: number | null
    successRows: number | null
    failedRows: number | null
    errorMessage: string | null
    errorDetails: string | null
    validationErrors: string | null
    importSummary: string | null
    config: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ImportTaskMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    title: string | null
    description: string | null
    fileName: string | null
    filePath: string | null
    fileSize: number | null
    fileType: string | null
    status: string | null
    startedAt: Date | null
    completedAt: Date | null
    durationMs: number | null
    totalRows: number | null
    processedRows: number | null
    successRows: number | null
    failedRows: number | null
    errorMessage: string | null
    errorDetails: string | null
    validationErrors: string | null
    importSummary: string | null
    config: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ImportTaskCountAggregateOutputType = {
    id: number
    userId: number
    title: number
    description: number
    fileName: number
    filePath: number
    fileSize: number
    fileType: number
    status: number
    startedAt: number
    completedAt: number
    durationMs: number
    totalRows: number
    processedRows: number
    successRows: number
    failedRows: number
    errorMessage: number
    errorDetails: number
    validationErrors: number
    importSummary: number
    config: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type ImportTaskAvgAggregateInputType = {
    fileSize?: true
    durationMs?: true
    totalRows?: true
    processedRows?: true
    successRows?: true
    failedRows?: true
  }

  export type ImportTaskSumAggregateInputType = {
    fileSize?: true
    durationMs?: true
    totalRows?: true
    processedRows?: true
    successRows?: true
    failedRows?: true
  }

  export type ImportTaskMinAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    description?: true
    fileName?: true
    filePath?: true
    fileSize?: true
    fileType?: true
    status?: true
    startedAt?: true
    completedAt?: true
    durationMs?: true
    totalRows?: true
    processedRows?: true
    successRows?: true
    failedRows?: true
    errorMessage?: true
    errorDetails?: true
    validationErrors?: true
    importSummary?: true
    config?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ImportTaskMaxAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    description?: true
    fileName?: true
    filePath?: true
    fileSize?: true
    fileType?: true
    status?: true
    startedAt?: true
    completedAt?: true
    durationMs?: true
    totalRows?: true
    processedRows?: true
    successRows?: true
    failedRows?: true
    errorMessage?: true
    errorDetails?: true
    validationErrors?: true
    importSummary?: true
    config?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ImportTaskCountAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    description?: true
    fileName?: true
    filePath?: true
    fileSize?: true
    fileType?: true
    status?: true
    startedAt?: true
    completedAt?: true
    durationMs?: true
    totalRows?: true
    processedRows?: true
    successRows?: true
    failedRows?: true
    errorMessage?: true
    errorDetails?: true
    validationErrors?: true
    importSummary?: true
    config?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type ImportTaskAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ImportTask to aggregate.
     */
    where?: ImportTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ImportTasks to fetch.
     */
    orderBy?: ImportTaskOrderByWithRelationInput | ImportTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ImportTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ImportTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ImportTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ImportTasks
    **/
    _count?: true | ImportTaskCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ImportTaskAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ImportTaskSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ImportTaskMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ImportTaskMaxAggregateInputType
  }

  export type GetImportTaskAggregateType<T extends ImportTaskAggregateArgs> = {
        [P in keyof T & keyof AggregateImportTask]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateImportTask[P]>
      : GetScalarType<T[P], AggregateImportTask[P]>
  }




  export type ImportTaskGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ImportTaskWhereInput
    orderBy?: ImportTaskOrderByWithAggregationInput | ImportTaskOrderByWithAggregationInput[]
    by: ImportTaskScalarFieldEnum[] | ImportTaskScalarFieldEnum
    having?: ImportTaskScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ImportTaskCountAggregateInputType | true
    _avg?: ImportTaskAvgAggregateInputType
    _sum?: ImportTaskSumAggregateInputType
    _min?: ImportTaskMinAggregateInputType
    _max?: ImportTaskMaxAggregateInputType
  }

  export type ImportTaskGroupByOutputType = {
    id: string
    userId: string
    title: string
    description: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType: string
    status: string
    startedAt: Date | null
    completedAt: Date | null
    durationMs: number | null
    totalRows: number
    processedRows: number
    successRows: number
    failedRows: number
    errorMessage: string | null
    errorDetails: string | null
    validationErrors: string | null
    importSummary: string | null
    config: string | null
    createdAt: Date
    updatedAt: Date
    _count: ImportTaskCountAggregateOutputType | null
    _avg: ImportTaskAvgAggregateOutputType | null
    _sum: ImportTaskSumAggregateOutputType | null
    _min: ImportTaskMinAggregateOutputType | null
    _max: ImportTaskMaxAggregateOutputType | null
  }

  type GetImportTaskGroupByPayload<T extends ImportTaskGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ImportTaskGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ImportTaskGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ImportTaskGroupByOutputType[P]>
            : GetScalarType<T[P], ImportTaskGroupByOutputType[P]>
        }
      >
    >


  export type ImportTaskSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    description?: boolean
    fileName?: boolean
    filePath?: boolean
    fileSize?: boolean
    fileType?: boolean
    status?: boolean
    startedAt?: boolean
    completedAt?: boolean
    durationMs?: boolean
    totalRows?: boolean
    processedRows?: boolean
    successRows?: boolean
    failedRows?: boolean
    errorMessage?: boolean
    errorDetails?: boolean
    validationErrors?: boolean
    importSummary?: boolean
    config?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    products?: boolean | ImportTask$productsArgs<ExtArgs>
    _count?: boolean | ImportTaskCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["importTask"]>

  export type ImportTaskSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    description?: boolean
    fileName?: boolean
    filePath?: boolean
    fileSize?: boolean
    fileType?: boolean
    status?: boolean
    startedAt?: boolean
    completedAt?: boolean
    durationMs?: boolean
    totalRows?: boolean
    processedRows?: boolean
    successRows?: boolean
    failedRows?: boolean
    errorMessage?: boolean
    errorDetails?: boolean
    validationErrors?: boolean
    importSummary?: boolean
    config?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["importTask"]>

  export type ImportTaskSelectScalar = {
    id?: boolean
    userId?: boolean
    title?: boolean
    description?: boolean
    fileName?: boolean
    filePath?: boolean
    fileSize?: boolean
    fileType?: boolean
    status?: boolean
    startedAt?: boolean
    completedAt?: boolean
    durationMs?: boolean
    totalRows?: boolean
    processedRows?: boolean
    successRows?: boolean
    failedRows?: boolean
    errorMessage?: boolean
    errorDetails?: boolean
    validationErrors?: boolean
    importSummary?: boolean
    config?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type ImportTaskInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    products?: boolean | ImportTask$productsArgs<ExtArgs>
    _count?: boolean | ImportTaskCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type ImportTaskIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $ImportTaskPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ImportTask"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      products: Prisma.$ProductPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      title: string
      description: string | null
      fileName: string
      filePath: string
      fileSize: number
      fileType: string
      status: string
      startedAt: Date | null
      completedAt: Date | null
      durationMs: number | null
      totalRows: number
      processedRows: number
      successRows: number
      failedRows: number
      errorMessage: string | null
      errorDetails: string | null
      validationErrors: string | null
      importSummary: string | null
      config: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["importTask"]>
    composites: {}
  }

  type ImportTaskGetPayload<S extends boolean | null | undefined | ImportTaskDefaultArgs> = $Result.GetResult<Prisma.$ImportTaskPayload, S>

  type ImportTaskCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<ImportTaskFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: ImportTaskCountAggregateInputType | true
    }

  export interface ImportTaskDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ImportTask'], meta: { name: 'ImportTask' } }
    /**
     * Find zero or one ImportTask that matches the filter.
     * @param {ImportTaskFindUniqueArgs} args - Arguments to find a ImportTask
     * @example
     * // Get one ImportTask
     * const importTask = await prisma.importTask.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ImportTaskFindUniqueArgs>(args: SelectSubset<T, ImportTaskFindUniqueArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one ImportTask that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {ImportTaskFindUniqueOrThrowArgs} args - Arguments to find a ImportTask
     * @example
     * // Get one ImportTask
     * const importTask = await prisma.importTask.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ImportTaskFindUniqueOrThrowArgs>(args: SelectSubset<T, ImportTaskFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first ImportTask that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ImportTaskFindFirstArgs} args - Arguments to find a ImportTask
     * @example
     * // Get one ImportTask
     * const importTask = await prisma.importTask.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ImportTaskFindFirstArgs>(args?: SelectSubset<T, ImportTaskFindFirstArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first ImportTask that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ImportTaskFindFirstOrThrowArgs} args - Arguments to find a ImportTask
     * @example
     * // Get one ImportTask
     * const importTask = await prisma.importTask.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ImportTaskFindFirstOrThrowArgs>(args?: SelectSubset<T, ImportTaskFindFirstOrThrowArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more ImportTasks that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ImportTaskFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ImportTasks
     * const importTasks = await prisma.importTask.findMany()
     * 
     * // Get first 10 ImportTasks
     * const importTasks = await prisma.importTask.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const importTaskWithIdOnly = await prisma.importTask.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ImportTaskFindManyArgs>(args?: SelectSubset<T, ImportTaskFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a ImportTask.
     * @param {ImportTaskCreateArgs} args - Arguments to create a ImportTask.
     * @example
     * // Create one ImportTask
     * const ImportTask = await prisma.importTask.create({
     *   data: {
     *     // ... data to create a ImportTask
     *   }
     * })
     * 
     */
    create<T extends ImportTaskCreateArgs>(args: SelectSubset<T, ImportTaskCreateArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many ImportTasks.
     * @param {ImportTaskCreateManyArgs} args - Arguments to create many ImportTasks.
     * @example
     * // Create many ImportTasks
     * const importTask = await prisma.importTask.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ImportTaskCreateManyArgs>(args?: SelectSubset<T, ImportTaskCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many ImportTasks and returns the data saved in the database.
     * @param {ImportTaskCreateManyAndReturnArgs} args - Arguments to create many ImportTasks.
     * @example
     * // Create many ImportTasks
     * const importTask = await prisma.importTask.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many ImportTasks and only return the `id`
     * const importTaskWithIdOnly = await prisma.importTask.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ImportTaskCreateManyAndReturnArgs>(args?: SelectSubset<T, ImportTaskCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a ImportTask.
     * @param {ImportTaskDeleteArgs} args - Arguments to delete one ImportTask.
     * @example
     * // Delete one ImportTask
     * const ImportTask = await prisma.importTask.delete({
     *   where: {
     *     // ... filter to delete one ImportTask
     *   }
     * })
     * 
     */
    delete<T extends ImportTaskDeleteArgs>(args: SelectSubset<T, ImportTaskDeleteArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one ImportTask.
     * @param {ImportTaskUpdateArgs} args - Arguments to update one ImportTask.
     * @example
     * // Update one ImportTask
     * const importTask = await prisma.importTask.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ImportTaskUpdateArgs>(args: SelectSubset<T, ImportTaskUpdateArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more ImportTasks.
     * @param {ImportTaskDeleteManyArgs} args - Arguments to filter ImportTasks to delete.
     * @example
     * // Delete a few ImportTasks
     * const { count } = await prisma.importTask.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ImportTaskDeleteManyArgs>(args?: SelectSubset<T, ImportTaskDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ImportTasks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ImportTaskUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ImportTasks
     * const importTask = await prisma.importTask.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ImportTaskUpdateManyArgs>(args: SelectSubset<T, ImportTaskUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one ImportTask.
     * @param {ImportTaskUpsertArgs} args - Arguments to update or create a ImportTask.
     * @example
     * // Update or create a ImportTask
     * const importTask = await prisma.importTask.upsert({
     *   create: {
     *     // ... data to create a ImportTask
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ImportTask we want to update
     *   }
     * })
     */
    upsert<T extends ImportTaskUpsertArgs>(args: SelectSubset<T, ImportTaskUpsertArgs<ExtArgs>>): Prisma__ImportTaskClient<$Result.GetResult<Prisma.$ImportTaskPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of ImportTasks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ImportTaskCountArgs} args - Arguments to filter ImportTasks to count.
     * @example
     * // Count the number of ImportTasks
     * const count = await prisma.importTask.count({
     *   where: {
     *     // ... the filter for the ImportTasks we want to count
     *   }
     * })
    **/
    count<T extends ImportTaskCountArgs>(
      args?: Subset<T, ImportTaskCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ImportTaskCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ImportTask.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ImportTaskAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ImportTaskAggregateArgs>(args: Subset<T, ImportTaskAggregateArgs>): Prisma.PrismaPromise<GetImportTaskAggregateType<T>>

    /**
     * Group by ImportTask.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ImportTaskGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ImportTaskGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ImportTaskGroupByArgs['orderBy'] }
        : { orderBy?: ImportTaskGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ImportTaskGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetImportTaskGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ImportTask model
   */
  readonly fields: ImportTaskFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ImportTask.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ImportTaskClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    products<T extends ImportTask$productsArgs<ExtArgs> = {}>(args?: Subset<T, ImportTask$productsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ImportTask model
   */ 
  interface ImportTaskFieldRefs {
    readonly id: FieldRef<"ImportTask", 'String'>
    readonly userId: FieldRef<"ImportTask", 'String'>
    readonly title: FieldRef<"ImportTask", 'String'>
    readonly description: FieldRef<"ImportTask", 'String'>
    readonly fileName: FieldRef<"ImportTask", 'String'>
    readonly filePath: FieldRef<"ImportTask", 'String'>
    readonly fileSize: FieldRef<"ImportTask", 'Int'>
    readonly fileType: FieldRef<"ImportTask", 'String'>
    readonly status: FieldRef<"ImportTask", 'String'>
    readonly startedAt: FieldRef<"ImportTask", 'DateTime'>
    readonly completedAt: FieldRef<"ImportTask", 'DateTime'>
    readonly durationMs: FieldRef<"ImportTask", 'Int'>
    readonly totalRows: FieldRef<"ImportTask", 'Int'>
    readonly processedRows: FieldRef<"ImportTask", 'Int'>
    readonly successRows: FieldRef<"ImportTask", 'Int'>
    readonly failedRows: FieldRef<"ImportTask", 'Int'>
    readonly errorMessage: FieldRef<"ImportTask", 'String'>
    readonly errorDetails: FieldRef<"ImportTask", 'String'>
    readonly validationErrors: FieldRef<"ImportTask", 'String'>
    readonly importSummary: FieldRef<"ImportTask", 'String'>
    readonly config: FieldRef<"ImportTask", 'String'>
    readonly createdAt: FieldRef<"ImportTask", 'DateTime'>
    readonly updatedAt: FieldRef<"ImportTask", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * ImportTask findUnique
   */
  export type ImportTaskFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * Filter, which ImportTask to fetch.
     */
    where: ImportTaskWhereUniqueInput
  }

  /**
   * ImportTask findUniqueOrThrow
   */
  export type ImportTaskFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * Filter, which ImportTask to fetch.
     */
    where: ImportTaskWhereUniqueInput
  }

  /**
   * ImportTask findFirst
   */
  export type ImportTaskFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * Filter, which ImportTask to fetch.
     */
    where?: ImportTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ImportTasks to fetch.
     */
    orderBy?: ImportTaskOrderByWithRelationInput | ImportTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ImportTasks.
     */
    cursor?: ImportTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ImportTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ImportTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ImportTasks.
     */
    distinct?: ImportTaskScalarFieldEnum | ImportTaskScalarFieldEnum[]
  }

  /**
   * ImportTask findFirstOrThrow
   */
  export type ImportTaskFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * Filter, which ImportTask to fetch.
     */
    where?: ImportTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ImportTasks to fetch.
     */
    orderBy?: ImportTaskOrderByWithRelationInput | ImportTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ImportTasks.
     */
    cursor?: ImportTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ImportTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ImportTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ImportTasks.
     */
    distinct?: ImportTaskScalarFieldEnum | ImportTaskScalarFieldEnum[]
  }

  /**
   * ImportTask findMany
   */
  export type ImportTaskFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * Filter, which ImportTasks to fetch.
     */
    where?: ImportTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ImportTasks to fetch.
     */
    orderBy?: ImportTaskOrderByWithRelationInput | ImportTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ImportTasks.
     */
    cursor?: ImportTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ImportTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ImportTasks.
     */
    skip?: number
    distinct?: ImportTaskScalarFieldEnum | ImportTaskScalarFieldEnum[]
  }

  /**
   * ImportTask create
   */
  export type ImportTaskCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * The data needed to create a ImportTask.
     */
    data: XOR<ImportTaskCreateInput, ImportTaskUncheckedCreateInput>
  }

  /**
   * ImportTask createMany
   */
  export type ImportTaskCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ImportTasks.
     */
    data: ImportTaskCreateManyInput | ImportTaskCreateManyInput[]
  }

  /**
   * ImportTask createManyAndReturn
   */
  export type ImportTaskCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many ImportTasks.
     */
    data: ImportTaskCreateManyInput | ImportTaskCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * ImportTask update
   */
  export type ImportTaskUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * The data needed to update a ImportTask.
     */
    data: XOR<ImportTaskUpdateInput, ImportTaskUncheckedUpdateInput>
    /**
     * Choose, which ImportTask to update.
     */
    where: ImportTaskWhereUniqueInput
  }

  /**
   * ImportTask updateMany
   */
  export type ImportTaskUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ImportTasks.
     */
    data: XOR<ImportTaskUpdateManyMutationInput, ImportTaskUncheckedUpdateManyInput>
    /**
     * Filter which ImportTasks to update
     */
    where?: ImportTaskWhereInput
  }

  /**
   * ImportTask upsert
   */
  export type ImportTaskUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * The filter to search for the ImportTask to update in case it exists.
     */
    where: ImportTaskWhereUniqueInput
    /**
     * In case the ImportTask found by the `where` argument doesn't exist, create a new ImportTask with this data.
     */
    create: XOR<ImportTaskCreateInput, ImportTaskUncheckedCreateInput>
    /**
     * In case the ImportTask was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ImportTaskUpdateInput, ImportTaskUncheckedUpdateInput>
  }

  /**
   * ImportTask delete
   */
  export type ImportTaskDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
    /**
     * Filter which ImportTask to delete.
     */
    where: ImportTaskWhereUniqueInput
  }

  /**
   * ImportTask deleteMany
   */
  export type ImportTaskDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ImportTasks to delete
     */
    where?: ImportTaskWhereInput
  }

  /**
   * ImportTask.products
   */
  export type ImportTask$productsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Product
     */
    select?: ProductSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductInclude<ExtArgs> | null
    where?: ProductWhereInput
    orderBy?: ProductOrderByWithRelationInput | ProductOrderByWithRelationInput[]
    cursor?: ProductWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ProductScalarFieldEnum | ProductScalarFieldEnum[]
  }

  /**
   * ImportTask without action
   */
  export type ImportTaskDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ImportTask
     */
    select?: ImportTaskSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ImportTaskInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    email: 'email',
    username: 'username',
    password: 'password',
    firstName: 'firstName',
    lastName: 'lastName',
    avatar: 'avatar',
    phone: 'phone',
    role: 'role',
    status: 'status',
    emailVerified: 'emailVerified',
    emailVerifiedAt: 'emailVerifiedAt',
    lastLoginAt: 'lastLoginAt',
    loginCount: 'loginCount',
    preferences: 'preferences',
    settings: 'settings',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const ProductScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    title: 'title',
    description: 'description',
    url: 'url',
    imageUrl: 'imageUrl',
    brand: 'brand',
    model: 'model',
    platform: 'platform',
    platformProductId: 'platformProductId',
    shopName: 'shopName',
    shopUrl: 'shopUrl',
    importTaskId: 'importTaskId',
    dataSource: 'dataSource',
    currentPrice: 'currentPrice',
    originalPrice: 'originalPrice',
    minPrice: 'minPrice',
    maxPrice: 'maxPrice',
    currency: 'currency',
    salesCount: 'salesCount',
    rating: 'rating',
    reviewCount: 'reviewCount',
    stockQuantity: 'stockQuantity',
    category: 'category',
    subcategory: 'subcategory',
    tags: 'tags',
    attributes: 'attributes',
    status: 'status',
    isTracked: 'isTracked',
    isAnalyzed: 'isAnalyzed',
    lastImportedAt: 'lastImportedAt',
    importCount: 'importCount',
    importErrors: 'importErrors',
    analysisSummary: 'analysisSummary',
    analysisScore: 'analysisScore',
    marketPosition: 'marketPosition',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type ProductScalarFieldEnum = (typeof ProductScalarFieldEnum)[keyof typeof ProductScalarFieldEnum]


  export const AnalysisResultScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    productId: 'productId',
    title: 'title',
    description: 'description',
    type: 'type',
    status: 'status',
    priority: 'priority',
    config: 'config',
    inputData: 'inputData',
    outputData: 'outputData',
    summary: 'summary',
    insights: 'insights',
    recommendations: 'recommendations',
    chartsData: 'chartsData',
    confidenceScore: 'confidenceScore',
    startedAt: 'startedAt',
    completedAt: 'completedAt',
    durationMs: 'durationMs',
    errorMessage: 'errorMessage',
    errorDetails: 'errorDetails',
    retryCount: 'retryCount',
    viewCount: 'viewCount',
    shareCount: 'shareCount',
    exportCount: 'exportCount',
    lastViewedAt: 'lastViewedAt',
    shareToken: 'shareToken',
    shareType: 'shareType',
    shareExpiresAt: 'shareExpiresAt',
    tags: 'tags',
    category: 'category',
    isFavorite: 'isFavorite',
    isArchived: 'isArchived',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type AnalysisResultScalarFieldEnum = (typeof AnalysisResultScalarFieldEnum)[keyof typeof AnalysisResultScalarFieldEnum]


  export const ImportTaskScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    title: 'title',
    description: 'description',
    fileName: 'fileName',
    filePath: 'filePath',
    fileSize: 'fileSize',
    fileType: 'fileType',
    status: 'status',
    startedAt: 'startedAt',
    completedAt: 'completedAt',
    durationMs: 'durationMs',
    totalRows: 'totalRows',
    processedRows: 'processedRows',
    successRows: 'successRows',
    failedRows: 'failedRows',
    errorMessage: 'errorMessage',
    errorDetails: 'errorDetails',
    validationErrors: 'validationErrors',
    importSummary: 'importSummary',
    config: 'config',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type ImportTaskScalarFieldEnum = (typeof ImportTaskScalarFieldEnum)[keyof typeof ImportTaskScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references 
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: StringFilter<"User"> | string
    email?: StringFilter<"User"> | string
    username?: StringFilter<"User"> | string
    password?: StringFilter<"User"> | string
    firstName?: StringNullableFilter<"User"> | string | null
    lastName?: StringNullableFilter<"User"> | string | null
    avatar?: StringNullableFilter<"User"> | string | null
    phone?: StringNullableFilter<"User"> | string | null
    role?: StringFilter<"User"> | string
    status?: StringFilter<"User"> | string
    emailVerified?: BoolFilter<"User"> | boolean
    emailVerifiedAt?: DateTimeNullableFilter<"User"> | Date | string | null
    lastLoginAt?: DateTimeNullableFilter<"User"> | Date | string | null
    loginCount?: IntFilter<"User"> | number
    preferences?: StringNullableFilter<"User"> | string | null
    settings?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    products?: ProductListRelationFilter
    analysisResults?: AnalysisResultListRelationFilter
    importTasks?: ImportTaskListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    firstName?: SortOrderInput | SortOrder
    lastName?: SortOrderInput | SortOrder
    avatar?: SortOrderInput | SortOrder
    phone?: SortOrderInput | SortOrder
    role?: SortOrder
    status?: SortOrder
    emailVerified?: SortOrder
    emailVerifiedAt?: SortOrderInput | SortOrder
    lastLoginAt?: SortOrderInput | SortOrder
    loginCount?: SortOrder
    preferences?: SortOrderInput | SortOrder
    settings?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    products?: ProductOrderByRelationAggregateInput
    analysisResults?: AnalysisResultOrderByRelationAggregateInput
    importTasks?: ImportTaskOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    username?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    password?: StringFilter<"User"> | string
    firstName?: StringNullableFilter<"User"> | string | null
    lastName?: StringNullableFilter<"User"> | string | null
    avatar?: StringNullableFilter<"User"> | string | null
    phone?: StringNullableFilter<"User"> | string | null
    role?: StringFilter<"User"> | string
    status?: StringFilter<"User"> | string
    emailVerified?: BoolFilter<"User"> | boolean
    emailVerifiedAt?: DateTimeNullableFilter<"User"> | Date | string | null
    lastLoginAt?: DateTimeNullableFilter<"User"> | Date | string | null
    loginCount?: IntFilter<"User"> | number
    preferences?: StringNullableFilter<"User"> | string | null
    settings?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    products?: ProductListRelationFilter
    analysisResults?: AnalysisResultListRelationFilter
    importTasks?: ImportTaskListRelationFilter
  }, "id" | "email" | "username">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    firstName?: SortOrderInput | SortOrder
    lastName?: SortOrderInput | SortOrder
    avatar?: SortOrderInput | SortOrder
    phone?: SortOrderInput | SortOrder
    role?: SortOrder
    status?: SortOrder
    emailVerified?: SortOrder
    emailVerifiedAt?: SortOrderInput | SortOrder
    lastLoginAt?: SortOrderInput | SortOrder
    loginCount?: SortOrder
    preferences?: SortOrderInput | SortOrder
    settings?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _avg?: UserAvgOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
    _sum?: UserSumOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"User"> | string
    email?: StringWithAggregatesFilter<"User"> | string
    username?: StringWithAggregatesFilter<"User"> | string
    password?: StringWithAggregatesFilter<"User"> | string
    firstName?: StringNullableWithAggregatesFilter<"User"> | string | null
    lastName?: StringNullableWithAggregatesFilter<"User"> | string | null
    avatar?: StringNullableWithAggregatesFilter<"User"> | string | null
    phone?: StringNullableWithAggregatesFilter<"User"> | string | null
    role?: StringWithAggregatesFilter<"User"> | string
    status?: StringWithAggregatesFilter<"User"> | string
    emailVerified?: BoolWithAggregatesFilter<"User"> | boolean
    emailVerifiedAt?: DateTimeNullableWithAggregatesFilter<"User"> | Date | string | null
    lastLoginAt?: DateTimeNullableWithAggregatesFilter<"User"> | Date | string | null
    loginCount?: IntWithAggregatesFilter<"User"> | number
    preferences?: StringNullableWithAggregatesFilter<"User"> | string | null
    settings?: StringNullableWithAggregatesFilter<"User"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type ProductWhereInput = {
    AND?: ProductWhereInput | ProductWhereInput[]
    OR?: ProductWhereInput[]
    NOT?: ProductWhereInput | ProductWhereInput[]
    id?: StringFilter<"Product"> | string
    userId?: StringFilter<"Product"> | string
    title?: StringFilter<"Product"> | string
    description?: StringNullableFilter<"Product"> | string | null
    url?: StringFilter<"Product"> | string
    imageUrl?: StringNullableFilter<"Product"> | string | null
    brand?: StringNullableFilter<"Product"> | string | null
    model?: StringNullableFilter<"Product"> | string | null
    platform?: StringFilter<"Product"> | string
    platformProductId?: StringNullableFilter<"Product"> | string | null
    shopName?: StringNullableFilter<"Product"> | string | null
    shopUrl?: StringNullableFilter<"Product"> | string | null
    importTaskId?: StringNullableFilter<"Product"> | string | null
    dataSource?: StringFilter<"Product"> | string
    currentPrice?: FloatNullableFilter<"Product"> | number | null
    originalPrice?: FloatNullableFilter<"Product"> | number | null
    minPrice?: FloatNullableFilter<"Product"> | number | null
    maxPrice?: FloatNullableFilter<"Product"> | number | null
    currency?: StringFilter<"Product"> | string
    salesCount?: IntNullableFilter<"Product"> | number | null
    rating?: FloatNullableFilter<"Product"> | number | null
    reviewCount?: IntNullableFilter<"Product"> | number | null
    stockQuantity?: IntNullableFilter<"Product"> | number | null
    category?: StringNullableFilter<"Product"> | string | null
    subcategory?: StringNullableFilter<"Product"> | string | null
    tags?: StringNullableFilter<"Product"> | string | null
    attributes?: StringNullableFilter<"Product"> | string | null
    status?: StringFilter<"Product"> | string
    isTracked?: BoolFilter<"Product"> | boolean
    isAnalyzed?: BoolFilter<"Product"> | boolean
    lastImportedAt?: DateTimeNullableFilter<"Product"> | Date | string | null
    importCount?: IntFilter<"Product"> | number
    importErrors?: StringNullableFilter<"Product"> | string | null
    analysisSummary?: StringNullableFilter<"Product"> | string | null
    analysisScore?: FloatNullableFilter<"Product"> | number | null
    marketPosition?: StringNullableFilter<"Product"> | string | null
    createdAt?: DateTimeFilter<"Product"> | Date | string
    updatedAt?: DateTimeFilter<"Product"> | Date | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    importTask?: XOR<ImportTaskNullableRelationFilter, ImportTaskWhereInput> | null
    analysisResults?: AnalysisResultListRelationFilter
  }

  export type ProductOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrderInput | SortOrder
    url?: SortOrder
    imageUrl?: SortOrderInput | SortOrder
    brand?: SortOrderInput | SortOrder
    model?: SortOrderInput | SortOrder
    platform?: SortOrder
    platformProductId?: SortOrderInput | SortOrder
    shopName?: SortOrderInput | SortOrder
    shopUrl?: SortOrderInput | SortOrder
    importTaskId?: SortOrderInput | SortOrder
    dataSource?: SortOrder
    currentPrice?: SortOrderInput | SortOrder
    originalPrice?: SortOrderInput | SortOrder
    minPrice?: SortOrderInput | SortOrder
    maxPrice?: SortOrderInput | SortOrder
    currency?: SortOrder
    salesCount?: SortOrderInput | SortOrder
    rating?: SortOrderInput | SortOrder
    reviewCount?: SortOrderInput | SortOrder
    stockQuantity?: SortOrderInput | SortOrder
    category?: SortOrderInput | SortOrder
    subcategory?: SortOrderInput | SortOrder
    tags?: SortOrderInput | SortOrder
    attributes?: SortOrderInput | SortOrder
    status?: SortOrder
    isTracked?: SortOrder
    isAnalyzed?: SortOrder
    lastImportedAt?: SortOrderInput | SortOrder
    importCount?: SortOrder
    importErrors?: SortOrderInput | SortOrder
    analysisSummary?: SortOrderInput | SortOrder
    analysisScore?: SortOrderInput | SortOrder
    marketPosition?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: UserOrderByWithRelationInput
    importTask?: ImportTaskOrderByWithRelationInput
    analysisResults?: AnalysisResultOrderByRelationAggregateInput
  }

  export type ProductWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: ProductWhereInput | ProductWhereInput[]
    OR?: ProductWhereInput[]
    NOT?: ProductWhereInput | ProductWhereInput[]
    userId?: StringFilter<"Product"> | string
    title?: StringFilter<"Product"> | string
    description?: StringNullableFilter<"Product"> | string | null
    url?: StringFilter<"Product"> | string
    imageUrl?: StringNullableFilter<"Product"> | string | null
    brand?: StringNullableFilter<"Product"> | string | null
    model?: StringNullableFilter<"Product"> | string | null
    platform?: StringFilter<"Product"> | string
    platformProductId?: StringNullableFilter<"Product"> | string | null
    shopName?: StringNullableFilter<"Product"> | string | null
    shopUrl?: StringNullableFilter<"Product"> | string | null
    importTaskId?: StringNullableFilter<"Product"> | string | null
    dataSource?: StringFilter<"Product"> | string
    currentPrice?: FloatNullableFilter<"Product"> | number | null
    originalPrice?: FloatNullableFilter<"Product"> | number | null
    minPrice?: FloatNullableFilter<"Product"> | number | null
    maxPrice?: FloatNullableFilter<"Product"> | number | null
    currency?: StringFilter<"Product"> | string
    salesCount?: IntNullableFilter<"Product"> | number | null
    rating?: FloatNullableFilter<"Product"> | number | null
    reviewCount?: IntNullableFilter<"Product"> | number | null
    stockQuantity?: IntNullableFilter<"Product"> | number | null
    category?: StringNullableFilter<"Product"> | string | null
    subcategory?: StringNullableFilter<"Product"> | string | null
    tags?: StringNullableFilter<"Product"> | string | null
    attributes?: StringNullableFilter<"Product"> | string | null
    status?: StringFilter<"Product"> | string
    isTracked?: BoolFilter<"Product"> | boolean
    isAnalyzed?: BoolFilter<"Product"> | boolean
    lastImportedAt?: DateTimeNullableFilter<"Product"> | Date | string | null
    importCount?: IntFilter<"Product"> | number
    importErrors?: StringNullableFilter<"Product"> | string | null
    analysisSummary?: StringNullableFilter<"Product"> | string | null
    analysisScore?: FloatNullableFilter<"Product"> | number | null
    marketPosition?: StringNullableFilter<"Product"> | string | null
    createdAt?: DateTimeFilter<"Product"> | Date | string
    updatedAt?: DateTimeFilter<"Product"> | Date | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    importTask?: XOR<ImportTaskNullableRelationFilter, ImportTaskWhereInput> | null
    analysisResults?: AnalysisResultListRelationFilter
  }, "id">

  export type ProductOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrderInput | SortOrder
    url?: SortOrder
    imageUrl?: SortOrderInput | SortOrder
    brand?: SortOrderInput | SortOrder
    model?: SortOrderInput | SortOrder
    platform?: SortOrder
    platformProductId?: SortOrderInput | SortOrder
    shopName?: SortOrderInput | SortOrder
    shopUrl?: SortOrderInput | SortOrder
    importTaskId?: SortOrderInput | SortOrder
    dataSource?: SortOrder
    currentPrice?: SortOrderInput | SortOrder
    originalPrice?: SortOrderInput | SortOrder
    minPrice?: SortOrderInput | SortOrder
    maxPrice?: SortOrderInput | SortOrder
    currency?: SortOrder
    salesCount?: SortOrderInput | SortOrder
    rating?: SortOrderInput | SortOrder
    reviewCount?: SortOrderInput | SortOrder
    stockQuantity?: SortOrderInput | SortOrder
    category?: SortOrderInput | SortOrder
    subcategory?: SortOrderInput | SortOrder
    tags?: SortOrderInput | SortOrder
    attributes?: SortOrderInput | SortOrder
    status?: SortOrder
    isTracked?: SortOrder
    isAnalyzed?: SortOrder
    lastImportedAt?: SortOrderInput | SortOrder
    importCount?: SortOrder
    importErrors?: SortOrderInput | SortOrder
    analysisSummary?: SortOrderInput | SortOrder
    analysisScore?: SortOrderInput | SortOrder
    marketPosition?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: ProductCountOrderByAggregateInput
    _avg?: ProductAvgOrderByAggregateInput
    _max?: ProductMaxOrderByAggregateInput
    _min?: ProductMinOrderByAggregateInput
    _sum?: ProductSumOrderByAggregateInput
  }

  export type ProductScalarWhereWithAggregatesInput = {
    AND?: ProductScalarWhereWithAggregatesInput | ProductScalarWhereWithAggregatesInput[]
    OR?: ProductScalarWhereWithAggregatesInput[]
    NOT?: ProductScalarWhereWithAggregatesInput | ProductScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Product"> | string
    userId?: StringWithAggregatesFilter<"Product"> | string
    title?: StringWithAggregatesFilter<"Product"> | string
    description?: StringNullableWithAggregatesFilter<"Product"> | string | null
    url?: StringWithAggregatesFilter<"Product"> | string
    imageUrl?: StringNullableWithAggregatesFilter<"Product"> | string | null
    brand?: StringNullableWithAggregatesFilter<"Product"> | string | null
    model?: StringNullableWithAggregatesFilter<"Product"> | string | null
    platform?: StringWithAggregatesFilter<"Product"> | string
    platformProductId?: StringNullableWithAggregatesFilter<"Product"> | string | null
    shopName?: StringNullableWithAggregatesFilter<"Product"> | string | null
    shopUrl?: StringNullableWithAggregatesFilter<"Product"> | string | null
    importTaskId?: StringNullableWithAggregatesFilter<"Product"> | string | null
    dataSource?: StringWithAggregatesFilter<"Product"> | string
    currentPrice?: FloatNullableWithAggregatesFilter<"Product"> | number | null
    originalPrice?: FloatNullableWithAggregatesFilter<"Product"> | number | null
    minPrice?: FloatNullableWithAggregatesFilter<"Product"> | number | null
    maxPrice?: FloatNullableWithAggregatesFilter<"Product"> | number | null
    currency?: StringWithAggregatesFilter<"Product"> | string
    salesCount?: IntNullableWithAggregatesFilter<"Product"> | number | null
    rating?: FloatNullableWithAggregatesFilter<"Product"> | number | null
    reviewCount?: IntNullableWithAggregatesFilter<"Product"> | number | null
    stockQuantity?: IntNullableWithAggregatesFilter<"Product"> | number | null
    category?: StringNullableWithAggregatesFilter<"Product"> | string | null
    subcategory?: StringNullableWithAggregatesFilter<"Product"> | string | null
    tags?: StringNullableWithAggregatesFilter<"Product"> | string | null
    attributes?: StringNullableWithAggregatesFilter<"Product"> | string | null
    status?: StringWithAggregatesFilter<"Product"> | string
    isTracked?: BoolWithAggregatesFilter<"Product"> | boolean
    isAnalyzed?: BoolWithAggregatesFilter<"Product"> | boolean
    lastImportedAt?: DateTimeNullableWithAggregatesFilter<"Product"> | Date | string | null
    importCount?: IntWithAggregatesFilter<"Product"> | number
    importErrors?: StringNullableWithAggregatesFilter<"Product"> | string | null
    analysisSummary?: StringNullableWithAggregatesFilter<"Product"> | string | null
    analysisScore?: FloatNullableWithAggregatesFilter<"Product"> | number | null
    marketPosition?: StringNullableWithAggregatesFilter<"Product"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"Product"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Product"> | Date | string
  }

  export type AnalysisResultWhereInput = {
    AND?: AnalysisResultWhereInput | AnalysisResultWhereInput[]
    OR?: AnalysisResultWhereInput[]
    NOT?: AnalysisResultWhereInput | AnalysisResultWhereInput[]
    id?: StringFilter<"AnalysisResult"> | string
    userId?: StringFilter<"AnalysisResult"> | string
    productId?: StringNullableFilter<"AnalysisResult"> | string | null
    title?: StringFilter<"AnalysisResult"> | string
    description?: StringNullableFilter<"AnalysisResult"> | string | null
    type?: StringFilter<"AnalysisResult"> | string
    status?: StringFilter<"AnalysisResult"> | string
    priority?: StringFilter<"AnalysisResult"> | string
    config?: StringNullableFilter<"AnalysisResult"> | string | null
    inputData?: StringNullableFilter<"AnalysisResult"> | string | null
    outputData?: StringNullableFilter<"AnalysisResult"> | string | null
    summary?: StringNullableFilter<"AnalysisResult"> | string | null
    insights?: StringNullableFilter<"AnalysisResult"> | string | null
    recommendations?: StringNullableFilter<"AnalysisResult"> | string | null
    chartsData?: StringNullableFilter<"AnalysisResult"> | string | null
    confidenceScore?: FloatNullableFilter<"AnalysisResult"> | number | null
    startedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    durationMs?: IntNullableFilter<"AnalysisResult"> | number | null
    errorMessage?: StringNullableFilter<"AnalysisResult"> | string | null
    errorDetails?: StringNullableFilter<"AnalysisResult"> | string | null
    retryCount?: IntFilter<"AnalysisResult"> | number
    viewCount?: IntFilter<"AnalysisResult"> | number
    shareCount?: IntFilter<"AnalysisResult"> | number
    exportCount?: IntFilter<"AnalysisResult"> | number
    lastViewedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    shareToken?: StringNullableFilter<"AnalysisResult"> | string | null
    shareType?: StringFilter<"AnalysisResult"> | string
    shareExpiresAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    tags?: StringNullableFilter<"AnalysisResult"> | string | null
    category?: StringNullableFilter<"AnalysisResult"> | string | null
    isFavorite?: BoolFilter<"AnalysisResult"> | boolean
    isArchived?: BoolFilter<"AnalysisResult"> | boolean
    createdAt?: DateTimeFilter<"AnalysisResult"> | Date | string
    updatedAt?: DateTimeFilter<"AnalysisResult"> | Date | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    product?: XOR<ProductNullableRelationFilter, ProductWhereInput> | null
  }

  export type AnalysisResultOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    productId?: SortOrderInput | SortOrder
    title?: SortOrder
    description?: SortOrderInput | SortOrder
    type?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    config?: SortOrderInput | SortOrder
    inputData?: SortOrderInput | SortOrder
    outputData?: SortOrderInput | SortOrder
    summary?: SortOrderInput | SortOrder
    insights?: SortOrderInput | SortOrder
    recommendations?: SortOrderInput | SortOrder
    chartsData?: SortOrderInput | SortOrder
    confidenceScore?: SortOrderInput | SortOrder
    startedAt?: SortOrderInput | SortOrder
    completedAt?: SortOrderInput | SortOrder
    durationMs?: SortOrderInput | SortOrder
    errorMessage?: SortOrderInput | SortOrder
    errorDetails?: SortOrderInput | SortOrder
    retryCount?: SortOrder
    viewCount?: SortOrder
    shareCount?: SortOrder
    exportCount?: SortOrder
    lastViewedAt?: SortOrderInput | SortOrder
    shareToken?: SortOrderInput | SortOrder
    shareType?: SortOrder
    shareExpiresAt?: SortOrderInput | SortOrder
    tags?: SortOrderInput | SortOrder
    category?: SortOrderInput | SortOrder
    isFavorite?: SortOrder
    isArchived?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: UserOrderByWithRelationInput
    product?: ProductOrderByWithRelationInput
  }

  export type AnalysisResultWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: AnalysisResultWhereInput | AnalysisResultWhereInput[]
    OR?: AnalysisResultWhereInput[]
    NOT?: AnalysisResultWhereInput | AnalysisResultWhereInput[]
    userId?: StringFilter<"AnalysisResult"> | string
    productId?: StringNullableFilter<"AnalysisResult"> | string | null
    title?: StringFilter<"AnalysisResult"> | string
    description?: StringNullableFilter<"AnalysisResult"> | string | null
    type?: StringFilter<"AnalysisResult"> | string
    status?: StringFilter<"AnalysisResult"> | string
    priority?: StringFilter<"AnalysisResult"> | string
    config?: StringNullableFilter<"AnalysisResult"> | string | null
    inputData?: StringNullableFilter<"AnalysisResult"> | string | null
    outputData?: StringNullableFilter<"AnalysisResult"> | string | null
    summary?: StringNullableFilter<"AnalysisResult"> | string | null
    insights?: StringNullableFilter<"AnalysisResult"> | string | null
    recommendations?: StringNullableFilter<"AnalysisResult"> | string | null
    chartsData?: StringNullableFilter<"AnalysisResult"> | string | null
    confidenceScore?: FloatNullableFilter<"AnalysisResult"> | number | null
    startedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    durationMs?: IntNullableFilter<"AnalysisResult"> | number | null
    errorMessage?: StringNullableFilter<"AnalysisResult"> | string | null
    errorDetails?: StringNullableFilter<"AnalysisResult"> | string | null
    retryCount?: IntFilter<"AnalysisResult"> | number
    viewCount?: IntFilter<"AnalysisResult"> | number
    shareCount?: IntFilter<"AnalysisResult"> | number
    exportCount?: IntFilter<"AnalysisResult"> | number
    lastViewedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    shareToken?: StringNullableFilter<"AnalysisResult"> | string | null
    shareType?: StringFilter<"AnalysisResult"> | string
    shareExpiresAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    tags?: StringNullableFilter<"AnalysisResult"> | string | null
    category?: StringNullableFilter<"AnalysisResult"> | string | null
    isFavorite?: BoolFilter<"AnalysisResult"> | boolean
    isArchived?: BoolFilter<"AnalysisResult"> | boolean
    createdAt?: DateTimeFilter<"AnalysisResult"> | Date | string
    updatedAt?: DateTimeFilter<"AnalysisResult"> | Date | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    product?: XOR<ProductNullableRelationFilter, ProductWhereInput> | null
  }, "id">

  export type AnalysisResultOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    productId?: SortOrderInput | SortOrder
    title?: SortOrder
    description?: SortOrderInput | SortOrder
    type?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    config?: SortOrderInput | SortOrder
    inputData?: SortOrderInput | SortOrder
    outputData?: SortOrderInput | SortOrder
    summary?: SortOrderInput | SortOrder
    insights?: SortOrderInput | SortOrder
    recommendations?: SortOrderInput | SortOrder
    chartsData?: SortOrderInput | SortOrder
    confidenceScore?: SortOrderInput | SortOrder
    startedAt?: SortOrderInput | SortOrder
    completedAt?: SortOrderInput | SortOrder
    durationMs?: SortOrderInput | SortOrder
    errorMessage?: SortOrderInput | SortOrder
    errorDetails?: SortOrderInput | SortOrder
    retryCount?: SortOrder
    viewCount?: SortOrder
    shareCount?: SortOrder
    exportCount?: SortOrder
    lastViewedAt?: SortOrderInput | SortOrder
    shareToken?: SortOrderInput | SortOrder
    shareType?: SortOrder
    shareExpiresAt?: SortOrderInput | SortOrder
    tags?: SortOrderInput | SortOrder
    category?: SortOrderInput | SortOrder
    isFavorite?: SortOrder
    isArchived?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: AnalysisResultCountOrderByAggregateInput
    _avg?: AnalysisResultAvgOrderByAggregateInput
    _max?: AnalysisResultMaxOrderByAggregateInput
    _min?: AnalysisResultMinOrderByAggregateInput
    _sum?: AnalysisResultSumOrderByAggregateInput
  }

  export type AnalysisResultScalarWhereWithAggregatesInput = {
    AND?: AnalysisResultScalarWhereWithAggregatesInput | AnalysisResultScalarWhereWithAggregatesInput[]
    OR?: AnalysisResultScalarWhereWithAggregatesInput[]
    NOT?: AnalysisResultScalarWhereWithAggregatesInput | AnalysisResultScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"AnalysisResult"> | string
    userId?: StringWithAggregatesFilter<"AnalysisResult"> | string
    productId?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    title?: StringWithAggregatesFilter<"AnalysisResult"> | string
    description?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    type?: StringWithAggregatesFilter<"AnalysisResult"> | string
    status?: StringWithAggregatesFilter<"AnalysisResult"> | string
    priority?: StringWithAggregatesFilter<"AnalysisResult"> | string
    config?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    inputData?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    outputData?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    summary?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    insights?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    recommendations?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    chartsData?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    confidenceScore?: FloatNullableWithAggregatesFilter<"AnalysisResult"> | number | null
    startedAt?: DateTimeNullableWithAggregatesFilter<"AnalysisResult"> | Date | string | null
    completedAt?: DateTimeNullableWithAggregatesFilter<"AnalysisResult"> | Date | string | null
    durationMs?: IntNullableWithAggregatesFilter<"AnalysisResult"> | number | null
    errorMessage?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    errorDetails?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    retryCount?: IntWithAggregatesFilter<"AnalysisResult"> | number
    viewCount?: IntWithAggregatesFilter<"AnalysisResult"> | number
    shareCount?: IntWithAggregatesFilter<"AnalysisResult"> | number
    exportCount?: IntWithAggregatesFilter<"AnalysisResult"> | number
    lastViewedAt?: DateTimeNullableWithAggregatesFilter<"AnalysisResult"> | Date | string | null
    shareToken?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    shareType?: StringWithAggregatesFilter<"AnalysisResult"> | string
    shareExpiresAt?: DateTimeNullableWithAggregatesFilter<"AnalysisResult"> | Date | string | null
    tags?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    category?: StringNullableWithAggregatesFilter<"AnalysisResult"> | string | null
    isFavorite?: BoolWithAggregatesFilter<"AnalysisResult"> | boolean
    isArchived?: BoolWithAggregatesFilter<"AnalysisResult"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"AnalysisResult"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"AnalysisResult"> | Date | string
  }

  export type ImportTaskWhereInput = {
    AND?: ImportTaskWhereInput | ImportTaskWhereInput[]
    OR?: ImportTaskWhereInput[]
    NOT?: ImportTaskWhereInput | ImportTaskWhereInput[]
    id?: StringFilter<"ImportTask"> | string
    userId?: StringFilter<"ImportTask"> | string
    title?: StringFilter<"ImportTask"> | string
    description?: StringNullableFilter<"ImportTask"> | string | null
    fileName?: StringFilter<"ImportTask"> | string
    filePath?: StringFilter<"ImportTask"> | string
    fileSize?: IntFilter<"ImportTask"> | number
    fileType?: StringFilter<"ImportTask"> | string
    status?: StringFilter<"ImportTask"> | string
    startedAt?: DateTimeNullableFilter<"ImportTask"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"ImportTask"> | Date | string | null
    durationMs?: IntNullableFilter<"ImportTask"> | number | null
    totalRows?: IntFilter<"ImportTask"> | number
    processedRows?: IntFilter<"ImportTask"> | number
    successRows?: IntFilter<"ImportTask"> | number
    failedRows?: IntFilter<"ImportTask"> | number
    errorMessage?: StringNullableFilter<"ImportTask"> | string | null
    errorDetails?: StringNullableFilter<"ImportTask"> | string | null
    validationErrors?: StringNullableFilter<"ImportTask"> | string | null
    importSummary?: StringNullableFilter<"ImportTask"> | string | null
    config?: StringNullableFilter<"ImportTask"> | string | null
    createdAt?: DateTimeFilter<"ImportTask"> | Date | string
    updatedAt?: DateTimeFilter<"ImportTask"> | Date | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    products?: ProductListRelationFilter
  }

  export type ImportTaskOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrderInput | SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    fileSize?: SortOrder
    fileType?: SortOrder
    status?: SortOrder
    startedAt?: SortOrderInput | SortOrder
    completedAt?: SortOrderInput | SortOrder
    durationMs?: SortOrderInput | SortOrder
    totalRows?: SortOrder
    processedRows?: SortOrder
    successRows?: SortOrder
    failedRows?: SortOrder
    errorMessage?: SortOrderInput | SortOrder
    errorDetails?: SortOrderInput | SortOrder
    validationErrors?: SortOrderInput | SortOrder
    importSummary?: SortOrderInput | SortOrder
    config?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: UserOrderByWithRelationInput
    products?: ProductOrderByRelationAggregateInput
  }

  export type ImportTaskWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: ImportTaskWhereInput | ImportTaskWhereInput[]
    OR?: ImportTaskWhereInput[]
    NOT?: ImportTaskWhereInput | ImportTaskWhereInput[]
    userId?: StringFilter<"ImportTask"> | string
    title?: StringFilter<"ImportTask"> | string
    description?: StringNullableFilter<"ImportTask"> | string | null
    fileName?: StringFilter<"ImportTask"> | string
    filePath?: StringFilter<"ImportTask"> | string
    fileSize?: IntFilter<"ImportTask"> | number
    fileType?: StringFilter<"ImportTask"> | string
    status?: StringFilter<"ImportTask"> | string
    startedAt?: DateTimeNullableFilter<"ImportTask"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"ImportTask"> | Date | string | null
    durationMs?: IntNullableFilter<"ImportTask"> | number | null
    totalRows?: IntFilter<"ImportTask"> | number
    processedRows?: IntFilter<"ImportTask"> | number
    successRows?: IntFilter<"ImportTask"> | number
    failedRows?: IntFilter<"ImportTask"> | number
    errorMessage?: StringNullableFilter<"ImportTask"> | string | null
    errorDetails?: StringNullableFilter<"ImportTask"> | string | null
    validationErrors?: StringNullableFilter<"ImportTask"> | string | null
    importSummary?: StringNullableFilter<"ImportTask"> | string | null
    config?: StringNullableFilter<"ImportTask"> | string | null
    createdAt?: DateTimeFilter<"ImportTask"> | Date | string
    updatedAt?: DateTimeFilter<"ImportTask"> | Date | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    products?: ProductListRelationFilter
  }, "id">

  export type ImportTaskOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrderInput | SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    fileSize?: SortOrder
    fileType?: SortOrder
    status?: SortOrder
    startedAt?: SortOrderInput | SortOrder
    completedAt?: SortOrderInput | SortOrder
    durationMs?: SortOrderInput | SortOrder
    totalRows?: SortOrder
    processedRows?: SortOrder
    successRows?: SortOrder
    failedRows?: SortOrder
    errorMessage?: SortOrderInput | SortOrder
    errorDetails?: SortOrderInput | SortOrder
    validationErrors?: SortOrderInput | SortOrder
    importSummary?: SortOrderInput | SortOrder
    config?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: ImportTaskCountOrderByAggregateInput
    _avg?: ImportTaskAvgOrderByAggregateInput
    _max?: ImportTaskMaxOrderByAggregateInput
    _min?: ImportTaskMinOrderByAggregateInput
    _sum?: ImportTaskSumOrderByAggregateInput
  }

  export type ImportTaskScalarWhereWithAggregatesInput = {
    AND?: ImportTaskScalarWhereWithAggregatesInput | ImportTaskScalarWhereWithAggregatesInput[]
    OR?: ImportTaskScalarWhereWithAggregatesInput[]
    NOT?: ImportTaskScalarWhereWithAggregatesInput | ImportTaskScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"ImportTask"> | string
    userId?: StringWithAggregatesFilter<"ImportTask"> | string
    title?: StringWithAggregatesFilter<"ImportTask"> | string
    description?: StringNullableWithAggregatesFilter<"ImportTask"> | string | null
    fileName?: StringWithAggregatesFilter<"ImportTask"> | string
    filePath?: StringWithAggregatesFilter<"ImportTask"> | string
    fileSize?: IntWithAggregatesFilter<"ImportTask"> | number
    fileType?: StringWithAggregatesFilter<"ImportTask"> | string
    status?: StringWithAggregatesFilter<"ImportTask"> | string
    startedAt?: DateTimeNullableWithAggregatesFilter<"ImportTask"> | Date | string | null
    completedAt?: DateTimeNullableWithAggregatesFilter<"ImportTask"> | Date | string | null
    durationMs?: IntNullableWithAggregatesFilter<"ImportTask"> | number | null
    totalRows?: IntWithAggregatesFilter<"ImportTask"> | number
    processedRows?: IntWithAggregatesFilter<"ImportTask"> | number
    successRows?: IntWithAggregatesFilter<"ImportTask"> | number
    failedRows?: IntWithAggregatesFilter<"ImportTask"> | number
    errorMessage?: StringNullableWithAggregatesFilter<"ImportTask"> | string | null
    errorDetails?: StringNullableWithAggregatesFilter<"ImportTask"> | string | null
    validationErrors?: StringNullableWithAggregatesFilter<"ImportTask"> | string | null
    importSummary?: StringNullableWithAggregatesFilter<"ImportTask"> | string | null
    config?: StringNullableWithAggregatesFilter<"ImportTask"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"ImportTask"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"ImportTask"> | Date | string
  }

  export type UserCreateInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductCreateNestedManyWithoutUserInput
    analysisResults?: AnalysisResultCreateNestedManyWithoutUserInput
    importTasks?: ImportTaskCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductUncheckedCreateNestedManyWithoutUserInput
    analysisResults?: AnalysisResultUncheckedCreateNestedManyWithoutUserInput
    importTasks?: ImportTaskUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUpdateManyWithoutUserNestedInput
    analysisResults?: AnalysisResultUpdateManyWithoutUserNestedInput
    importTasks?: ImportTaskUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUncheckedUpdateManyWithoutUserNestedInput
    analysisResults?: AnalysisResultUncheckedUpdateManyWithoutUserNestedInput
    importTasks?: ImportTaskUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductCreateInput = {
    id?: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutProductsInput
    importTask?: ImportTaskCreateNestedOneWithoutProductsInput
    analysisResults?: AnalysisResultCreateNestedManyWithoutProductInput
  }

  export type ProductUncheckedCreateInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    importTaskId?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    analysisResults?: AnalysisResultUncheckedCreateNestedManyWithoutProductInput
  }

  export type ProductUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutProductsNestedInput
    importTask?: ImportTaskUpdateOneWithoutProductsNestedInput
    analysisResults?: AnalysisResultUpdateManyWithoutProductNestedInput
  }

  export type ProductUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    importTaskId?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    analysisResults?: AnalysisResultUncheckedUpdateManyWithoutProductNestedInput
  }

  export type ProductCreateManyInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    importTaskId?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    importTaskId?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AnalysisResultCreateInput = {
    id?: string
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutAnalysisResultsInput
    product?: ProductCreateNestedOneWithoutAnalysisResultsInput
  }

  export type AnalysisResultUncheckedCreateInput = {
    id?: string
    userId: string
    productId?: string | null
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AnalysisResultUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutAnalysisResultsNestedInput
    product?: ProductUpdateOneWithoutAnalysisResultsNestedInput
  }

  export type AnalysisResultUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    productId?: NullableStringFieldUpdateOperationsInput | string | null
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AnalysisResultCreateManyInput = {
    id?: string
    userId: string
    productId?: string | null
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AnalysisResultUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AnalysisResultUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    productId?: NullableStringFieldUpdateOperationsInput | string | null
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ImportTaskCreateInput = {
    id?: string
    title: string
    description?: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType?: string
    status?: string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    totalRows?: number
    processedRows?: number
    successRows?: number
    failedRows?: number
    errorMessage?: string | null
    errorDetails?: string | null
    validationErrors?: string | null
    importSummary?: string | null
    config?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutImportTasksInput
    products?: ProductCreateNestedManyWithoutImportTaskInput
  }

  export type ImportTaskUncheckedCreateInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType?: string
    status?: string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    totalRows?: number
    processedRows?: number
    successRows?: number
    failedRows?: number
    errorMessage?: string | null
    errorDetails?: string | null
    validationErrors?: string | null
    importSummary?: string | null
    config?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductUncheckedCreateNestedManyWithoutImportTaskInput
  }

  export type ImportTaskUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutImportTasksNestedInput
    products?: ProductUpdateManyWithoutImportTaskNestedInput
  }

  export type ImportTaskUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUncheckedUpdateManyWithoutImportTaskNestedInput
  }

  export type ImportTaskCreateManyInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType?: string
    status?: string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    totalRows?: number
    processedRows?: number
    successRows?: number
    failedRows?: number
    errorMessage?: string | null
    errorDetails?: string | null
    validationErrors?: string | null
    importSummary?: string | null
    config?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ImportTaskUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ImportTaskUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type ProductListRelationFilter = {
    every?: ProductWhereInput
    some?: ProductWhereInput
    none?: ProductWhereInput
  }

  export type AnalysisResultListRelationFilter = {
    every?: AnalysisResultWhereInput
    some?: AnalysisResultWhereInput
    none?: AnalysisResultWhereInput
  }

  export type ImportTaskListRelationFilter = {
    every?: ImportTaskWhereInput
    some?: ImportTaskWhereInput
    none?: ImportTaskWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type ProductOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type AnalysisResultOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type ImportTaskOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    avatar?: SortOrder
    phone?: SortOrder
    role?: SortOrder
    status?: SortOrder
    emailVerified?: SortOrder
    emailVerifiedAt?: SortOrder
    lastLoginAt?: SortOrder
    loginCount?: SortOrder
    preferences?: SortOrder
    settings?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserAvgOrderByAggregateInput = {
    loginCount?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    avatar?: SortOrder
    phone?: SortOrder
    role?: SortOrder
    status?: SortOrder
    emailVerified?: SortOrder
    emailVerifiedAt?: SortOrder
    lastLoginAt?: SortOrder
    loginCount?: SortOrder
    preferences?: SortOrder
    settings?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    username?: SortOrder
    password?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    avatar?: SortOrder
    phone?: SortOrder
    role?: SortOrder
    status?: SortOrder
    emailVerified?: SortOrder
    emailVerifiedAt?: SortOrder
    lastLoginAt?: SortOrder
    loginCount?: SortOrder
    preferences?: SortOrder
    settings?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSumOrderByAggregateInput = {
    loginCount?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type FloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type UserRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type ImportTaskNullableRelationFilter = {
    is?: ImportTaskWhereInput | null
    isNot?: ImportTaskWhereInput | null
  }

  export type ProductCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    url?: SortOrder
    imageUrl?: SortOrder
    brand?: SortOrder
    model?: SortOrder
    platform?: SortOrder
    platformProductId?: SortOrder
    shopName?: SortOrder
    shopUrl?: SortOrder
    importTaskId?: SortOrder
    dataSource?: SortOrder
    currentPrice?: SortOrder
    originalPrice?: SortOrder
    minPrice?: SortOrder
    maxPrice?: SortOrder
    currency?: SortOrder
    salesCount?: SortOrder
    rating?: SortOrder
    reviewCount?: SortOrder
    stockQuantity?: SortOrder
    category?: SortOrder
    subcategory?: SortOrder
    tags?: SortOrder
    attributes?: SortOrder
    status?: SortOrder
    isTracked?: SortOrder
    isAnalyzed?: SortOrder
    lastImportedAt?: SortOrder
    importCount?: SortOrder
    importErrors?: SortOrder
    analysisSummary?: SortOrder
    analysisScore?: SortOrder
    marketPosition?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductAvgOrderByAggregateInput = {
    currentPrice?: SortOrder
    originalPrice?: SortOrder
    minPrice?: SortOrder
    maxPrice?: SortOrder
    salesCount?: SortOrder
    rating?: SortOrder
    reviewCount?: SortOrder
    stockQuantity?: SortOrder
    importCount?: SortOrder
    analysisScore?: SortOrder
  }

  export type ProductMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    url?: SortOrder
    imageUrl?: SortOrder
    brand?: SortOrder
    model?: SortOrder
    platform?: SortOrder
    platformProductId?: SortOrder
    shopName?: SortOrder
    shopUrl?: SortOrder
    importTaskId?: SortOrder
    dataSource?: SortOrder
    currentPrice?: SortOrder
    originalPrice?: SortOrder
    minPrice?: SortOrder
    maxPrice?: SortOrder
    currency?: SortOrder
    salesCount?: SortOrder
    rating?: SortOrder
    reviewCount?: SortOrder
    stockQuantity?: SortOrder
    category?: SortOrder
    subcategory?: SortOrder
    tags?: SortOrder
    attributes?: SortOrder
    status?: SortOrder
    isTracked?: SortOrder
    isAnalyzed?: SortOrder
    lastImportedAt?: SortOrder
    importCount?: SortOrder
    importErrors?: SortOrder
    analysisSummary?: SortOrder
    analysisScore?: SortOrder
    marketPosition?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    url?: SortOrder
    imageUrl?: SortOrder
    brand?: SortOrder
    model?: SortOrder
    platform?: SortOrder
    platformProductId?: SortOrder
    shopName?: SortOrder
    shopUrl?: SortOrder
    importTaskId?: SortOrder
    dataSource?: SortOrder
    currentPrice?: SortOrder
    originalPrice?: SortOrder
    minPrice?: SortOrder
    maxPrice?: SortOrder
    currency?: SortOrder
    salesCount?: SortOrder
    rating?: SortOrder
    reviewCount?: SortOrder
    stockQuantity?: SortOrder
    category?: SortOrder
    subcategory?: SortOrder
    tags?: SortOrder
    attributes?: SortOrder
    status?: SortOrder
    isTracked?: SortOrder
    isAnalyzed?: SortOrder
    lastImportedAt?: SortOrder
    importCount?: SortOrder
    importErrors?: SortOrder
    analysisSummary?: SortOrder
    analysisScore?: SortOrder
    marketPosition?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductSumOrderByAggregateInput = {
    currentPrice?: SortOrder
    originalPrice?: SortOrder
    minPrice?: SortOrder
    maxPrice?: SortOrder
    salesCount?: SortOrder
    rating?: SortOrder
    reviewCount?: SortOrder
    stockQuantity?: SortOrder
    importCount?: SortOrder
    analysisScore?: SortOrder
  }

  export type FloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type ProductNullableRelationFilter = {
    is?: ProductWhereInput | null
    isNot?: ProductWhereInput | null
  }

  export type AnalysisResultCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    productId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    type?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    config?: SortOrder
    inputData?: SortOrder
    outputData?: SortOrder
    summary?: SortOrder
    insights?: SortOrder
    recommendations?: SortOrder
    chartsData?: SortOrder
    confidenceScore?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    durationMs?: SortOrder
    errorMessage?: SortOrder
    errorDetails?: SortOrder
    retryCount?: SortOrder
    viewCount?: SortOrder
    shareCount?: SortOrder
    exportCount?: SortOrder
    lastViewedAt?: SortOrder
    shareToken?: SortOrder
    shareType?: SortOrder
    shareExpiresAt?: SortOrder
    tags?: SortOrder
    category?: SortOrder
    isFavorite?: SortOrder
    isArchived?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AnalysisResultAvgOrderByAggregateInput = {
    confidenceScore?: SortOrder
    durationMs?: SortOrder
    retryCount?: SortOrder
    viewCount?: SortOrder
    shareCount?: SortOrder
    exportCount?: SortOrder
  }

  export type AnalysisResultMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    productId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    type?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    config?: SortOrder
    inputData?: SortOrder
    outputData?: SortOrder
    summary?: SortOrder
    insights?: SortOrder
    recommendations?: SortOrder
    chartsData?: SortOrder
    confidenceScore?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    durationMs?: SortOrder
    errorMessage?: SortOrder
    errorDetails?: SortOrder
    retryCount?: SortOrder
    viewCount?: SortOrder
    shareCount?: SortOrder
    exportCount?: SortOrder
    lastViewedAt?: SortOrder
    shareToken?: SortOrder
    shareType?: SortOrder
    shareExpiresAt?: SortOrder
    tags?: SortOrder
    category?: SortOrder
    isFavorite?: SortOrder
    isArchived?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AnalysisResultMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    productId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    type?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    config?: SortOrder
    inputData?: SortOrder
    outputData?: SortOrder
    summary?: SortOrder
    insights?: SortOrder
    recommendations?: SortOrder
    chartsData?: SortOrder
    confidenceScore?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    durationMs?: SortOrder
    errorMessage?: SortOrder
    errorDetails?: SortOrder
    retryCount?: SortOrder
    viewCount?: SortOrder
    shareCount?: SortOrder
    exportCount?: SortOrder
    lastViewedAt?: SortOrder
    shareToken?: SortOrder
    shareType?: SortOrder
    shareExpiresAt?: SortOrder
    tags?: SortOrder
    category?: SortOrder
    isFavorite?: SortOrder
    isArchived?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AnalysisResultSumOrderByAggregateInput = {
    confidenceScore?: SortOrder
    durationMs?: SortOrder
    retryCount?: SortOrder
    viewCount?: SortOrder
    shareCount?: SortOrder
    exportCount?: SortOrder
  }

  export type ImportTaskCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    fileSize?: SortOrder
    fileType?: SortOrder
    status?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    durationMs?: SortOrder
    totalRows?: SortOrder
    processedRows?: SortOrder
    successRows?: SortOrder
    failedRows?: SortOrder
    errorMessage?: SortOrder
    errorDetails?: SortOrder
    validationErrors?: SortOrder
    importSummary?: SortOrder
    config?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ImportTaskAvgOrderByAggregateInput = {
    fileSize?: SortOrder
    durationMs?: SortOrder
    totalRows?: SortOrder
    processedRows?: SortOrder
    successRows?: SortOrder
    failedRows?: SortOrder
  }

  export type ImportTaskMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    fileSize?: SortOrder
    fileType?: SortOrder
    status?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    durationMs?: SortOrder
    totalRows?: SortOrder
    processedRows?: SortOrder
    successRows?: SortOrder
    failedRows?: SortOrder
    errorMessage?: SortOrder
    errorDetails?: SortOrder
    validationErrors?: SortOrder
    importSummary?: SortOrder
    config?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ImportTaskMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    description?: SortOrder
    fileName?: SortOrder
    filePath?: SortOrder
    fileSize?: SortOrder
    fileType?: SortOrder
    status?: SortOrder
    startedAt?: SortOrder
    completedAt?: SortOrder
    durationMs?: SortOrder
    totalRows?: SortOrder
    processedRows?: SortOrder
    successRows?: SortOrder
    failedRows?: SortOrder
    errorMessage?: SortOrder
    errorDetails?: SortOrder
    validationErrors?: SortOrder
    importSummary?: SortOrder
    config?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ImportTaskSumOrderByAggregateInput = {
    fileSize?: SortOrder
    durationMs?: SortOrder
    totalRows?: SortOrder
    processedRows?: SortOrder
    successRows?: SortOrder
    failedRows?: SortOrder
  }

  export type ProductCreateNestedManyWithoutUserInput = {
    create?: XOR<ProductCreateWithoutUserInput, ProductUncheckedCreateWithoutUserInput> | ProductCreateWithoutUserInput[] | ProductUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ProductCreateOrConnectWithoutUserInput | ProductCreateOrConnectWithoutUserInput[]
    createMany?: ProductCreateManyUserInputEnvelope
    connect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
  }

  export type AnalysisResultCreateNestedManyWithoutUserInput = {
    create?: XOR<AnalysisResultCreateWithoutUserInput, AnalysisResultUncheckedCreateWithoutUserInput> | AnalysisResultCreateWithoutUserInput[] | AnalysisResultUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AnalysisResultCreateOrConnectWithoutUserInput | AnalysisResultCreateOrConnectWithoutUserInput[]
    createMany?: AnalysisResultCreateManyUserInputEnvelope
    connect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
  }

  export type ImportTaskCreateNestedManyWithoutUserInput = {
    create?: XOR<ImportTaskCreateWithoutUserInput, ImportTaskUncheckedCreateWithoutUserInput> | ImportTaskCreateWithoutUserInput[] | ImportTaskUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ImportTaskCreateOrConnectWithoutUserInput | ImportTaskCreateOrConnectWithoutUserInput[]
    createMany?: ImportTaskCreateManyUserInputEnvelope
    connect?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
  }

  export type ProductUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<ProductCreateWithoutUserInput, ProductUncheckedCreateWithoutUserInput> | ProductCreateWithoutUserInput[] | ProductUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ProductCreateOrConnectWithoutUserInput | ProductCreateOrConnectWithoutUserInput[]
    createMany?: ProductCreateManyUserInputEnvelope
    connect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
  }

  export type AnalysisResultUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<AnalysisResultCreateWithoutUserInput, AnalysisResultUncheckedCreateWithoutUserInput> | AnalysisResultCreateWithoutUserInput[] | AnalysisResultUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AnalysisResultCreateOrConnectWithoutUserInput | AnalysisResultCreateOrConnectWithoutUserInput[]
    createMany?: AnalysisResultCreateManyUserInputEnvelope
    connect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
  }

  export type ImportTaskUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<ImportTaskCreateWithoutUserInput, ImportTaskUncheckedCreateWithoutUserInput> | ImportTaskCreateWithoutUserInput[] | ImportTaskUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ImportTaskCreateOrConnectWithoutUserInput | ImportTaskCreateOrConnectWithoutUserInput[]
    createMany?: ImportTaskCreateManyUserInputEnvelope
    connect?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type ProductUpdateManyWithoutUserNestedInput = {
    create?: XOR<ProductCreateWithoutUserInput, ProductUncheckedCreateWithoutUserInput> | ProductCreateWithoutUserInput[] | ProductUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ProductCreateOrConnectWithoutUserInput | ProductCreateOrConnectWithoutUserInput[]
    upsert?: ProductUpsertWithWhereUniqueWithoutUserInput | ProductUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: ProductCreateManyUserInputEnvelope
    set?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    disconnect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    delete?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    connect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    update?: ProductUpdateWithWhereUniqueWithoutUserInput | ProductUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: ProductUpdateManyWithWhereWithoutUserInput | ProductUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: ProductScalarWhereInput | ProductScalarWhereInput[]
  }

  export type AnalysisResultUpdateManyWithoutUserNestedInput = {
    create?: XOR<AnalysisResultCreateWithoutUserInput, AnalysisResultUncheckedCreateWithoutUserInput> | AnalysisResultCreateWithoutUserInput[] | AnalysisResultUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AnalysisResultCreateOrConnectWithoutUserInput | AnalysisResultCreateOrConnectWithoutUserInput[]
    upsert?: AnalysisResultUpsertWithWhereUniqueWithoutUserInput | AnalysisResultUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: AnalysisResultCreateManyUserInputEnvelope
    set?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    disconnect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    delete?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    connect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    update?: AnalysisResultUpdateWithWhereUniqueWithoutUserInput | AnalysisResultUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: AnalysisResultUpdateManyWithWhereWithoutUserInput | AnalysisResultUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: AnalysisResultScalarWhereInput | AnalysisResultScalarWhereInput[]
  }

  export type ImportTaskUpdateManyWithoutUserNestedInput = {
    create?: XOR<ImportTaskCreateWithoutUserInput, ImportTaskUncheckedCreateWithoutUserInput> | ImportTaskCreateWithoutUserInput[] | ImportTaskUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ImportTaskCreateOrConnectWithoutUserInput | ImportTaskCreateOrConnectWithoutUserInput[]
    upsert?: ImportTaskUpsertWithWhereUniqueWithoutUserInput | ImportTaskUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: ImportTaskCreateManyUserInputEnvelope
    set?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
    disconnect?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
    delete?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
    connect?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
    update?: ImportTaskUpdateWithWhereUniqueWithoutUserInput | ImportTaskUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: ImportTaskUpdateManyWithWhereWithoutUserInput | ImportTaskUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: ImportTaskScalarWhereInput | ImportTaskScalarWhereInput[]
  }

  export type ProductUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<ProductCreateWithoutUserInput, ProductUncheckedCreateWithoutUserInput> | ProductCreateWithoutUserInput[] | ProductUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ProductCreateOrConnectWithoutUserInput | ProductCreateOrConnectWithoutUserInput[]
    upsert?: ProductUpsertWithWhereUniqueWithoutUserInput | ProductUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: ProductCreateManyUserInputEnvelope
    set?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    disconnect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    delete?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    connect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    update?: ProductUpdateWithWhereUniqueWithoutUserInput | ProductUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: ProductUpdateManyWithWhereWithoutUserInput | ProductUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: ProductScalarWhereInput | ProductScalarWhereInput[]
  }

  export type AnalysisResultUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<AnalysisResultCreateWithoutUserInput, AnalysisResultUncheckedCreateWithoutUserInput> | AnalysisResultCreateWithoutUserInput[] | AnalysisResultUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AnalysisResultCreateOrConnectWithoutUserInput | AnalysisResultCreateOrConnectWithoutUserInput[]
    upsert?: AnalysisResultUpsertWithWhereUniqueWithoutUserInput | AnalysisResultUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: AnalysisResultCreateManyUserInputEnvelope
    set?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    disconnect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    delete?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    connect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    update?: AnalysisResultUpdateWithWhereUniqueWithoutUserInput | AnalysisResultUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: AnalysisResultUpdateManyWithWhereWithoutUserInput | AnalysisResultUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: AnalysisResultScalarWhereInput | AnalysisResultScalarWhereInput[]
  }

  export type ImportTaskUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<ImportTaskCreateWithoutUserInput, ImportTaskUncheckedCreateWithoutUserInput> | ImportTaskCreateWithoutUserInput[] | ImportTaskUncheckedCreateWithoutUserInput[]
    connectOrCreate?: ImportTaskCreateOrConnectWithoutUserInput | ImportTaskCreateOrConnectWithoutUserInput[]
    upsert?: ImportTaskUpsertWithWhereUniqueWithoutUserInput | ImportTaskUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: ImportTaskCreateManyUserInputEnvelope
    set?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
    disconnect?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
    delete?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
    connect?: ImportTaskWhereUniqueInput | ImportTaskWhereUniqueInput[]
    update?: ImportTaskUpdateWithWhereUniqueWithoutUserInput | ImportTaskUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: ImportTaskUpdateManyWithWhereWithoutUserInput | ImportTaskUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: ImportTaskScalarWhereInput | ImportTaskScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutProductsInput = {
    create?: XOR<UserCreateWithoutProductsInput, UserUncheckedCreateWithoutProductsInput>
    connectOrCreate?: UserCreateOrConnectWithoutProductsInput
    connect?: UserWhereUniqueInput
  }

  export type ImportTaskCreateNestedOneWithoutProductsInput = {
    create?: XOR<ImportTaskCreateWithoutProductsInput, ImportTaskUncheckedCreateWithoutProductsInput>
    connectOrCreate?: ImportTaskCreateOrConnectWithoutProductsInput
    connect?: ImportTaskWhereUniqueInput
  }

  export type AnalysisResultCreateNestedManyWithoutProductInput = {
    create?: XOR<AnalysisResultCreateWithoutProductInput, AnalysisResultUncheckedCreateWithoutProductInput> | AnalysisResultCreateWithoutProductInput[] | AnalysisResultUncheckedCreateWithoutProductInput[]
    connectOrCreate?: AnalysisResultCreateOrConnectWithoutProductInput | AnalysisResultCreateOrConnectWithoutProductInput[]
    createMany?: AnalysisResultCreateManyProductInputEnvelope
    connect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
  }

  export type AnalysisResultUncheckedCreateNestedManyWithoutProductInput = {
    create?: XOR<AnalysisResultCreateWithoutProductInput, AnalysisResultUncheckedCreateWithoutProductInput> | AnalysisResultCreateWithoutProductInput[] | AnalysisResultUncheckedCreateWithoutProductInput[]
    connectOrCreate?: AnalysisResultCreateOrConnectWithoutProductInput | AnalysisResultCreateOrConnectWithoutProductInput[]
    createMany?: AnalysisResultCreateManyProductInputEnvelope
    connect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
  }

  export type NullableFloatFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type UserUpdateOneRequiredWithoutProductsNestedInput = {
    create?: XOR<UserCreateWithoutProductsInput, UserUncheckedCreateWithoutProductsInput>
    connectOrCreate?: UserCreateOrConnectWithoutProductsInput
    upsert?: UserUpsertWithoutProductsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutProductsInput, UserUpdateWithoutProductsInput>, UserUncheckedUpdateWithoutProductsInput>
  }

  export type ImportTaskUpdateOneWithoutProductsNestedInput = {
    create?: XOR<ImportTaskCreateWithoutProductsInput, ImportTaskUncheckedCreateWithoutProductsInput>
    connectOrCreate?: ImportTaskCreateOrConnectWithoutProductsInput
    upsert?: ImportTaskUpsertWithoutProductsInput
    disconnect?: ImportTaskWhereInput | boolean
    delete?: ImportTaskWhereInput | boolean
    connect?: ImportTaskWhereUniqueInput
    update?: XOR<XOR<ImportTaskUpdateToOneWithWhereWithoutProductsInput, ImportTaskUpdateWithoutProductsInput>, ImportTaskUncheckedUpdateWithoutProductsInput>
  }

  export type AnalysisResultUpdateManyWithoutProductNestedInput = {
    create?: XOR<AnalysisResultCreateWithoutProductInput, AnalysisResultUncheckedCreateWithoutProductInput> | AnalysisResultCreateWithoutProductInput[] | AnalysisResultUncheckedCreateWithoutProductInput[]
    connectOrCreate?: AnalysisResultCreateOrConnectWithoutProductInput | AnalysisResultCreateOrConnectWithoutProductInput[]
    upsert?: AnalysisResultUpsertWithWhereUniqueWithoutProductInput | AnalysisResultUpsertWithWhereUniqueWithoutProductInput[]
    createMany?: AnalysisResultCreateManyProductInputEnvelope
    set?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    disconnect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    delete?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    connect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    update?: AnalysisResultUpdateWithWhereUniqueWithoutProductInput | AnalysisResultUpdateWithWhereUniqueWithoutProductInput[]
    updateMany?: AnalysisResultUpdateManyWithWhereWithoutProductInput | AnalysisResultUpdateManyWithWhereWithoutProductInput[]
    deleteMany?: AnalysisResultScalarWhereInput | AnalysisResultScalarWhereInput[]
  }

  export type AnalysisResultUncheckedUpdateManyWithoutProductNestedInput = {
    create?: XOR<AnalysisResultCreateWithoutProductInput, AnalysisResultUncheckedCreateWithoutProductInput> | AnalysisResultCreateWithoutProductInput[] | AnalysisResultUncheckedCreateWithoutProductInput[]
    connectOrCreate?: AnalysisResultCreateOrConnectWithoutProductInput | AnalysisResultCreateOrConnectWithoutProductInput[]
    upsert?: AnalysisResultUpsertWithWhereUniqueWithoutProductInput | AnalysisResultUpsertWithWhereUniqueWithoutProductInput[]
    createMany?: AnalysisResultCreateManyProductInputEnvelope
    set?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    disconnect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    delete?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    connect?: AnalysisResultWhereUniqueInput | AnalysisResultWhereUniqueInput[]
    update?: AnalysisResultUpdateWithWhereUniqueWithoutProductInput | AnalysisResultUpdateWithWhereUniqueWithoutProductInput[]
    updateMany?: AnalysisResultUpdateManyWithWhereWithoutProductInput | AnalysisResultUpdateManyWithWhereWithoutProductInput[]
    deleteMany?: AnalysisResultScalarWhereInput | AnalysisResultScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutAnalysisResultsInput = {
    create?: XOR<UserCreateWithoutAnalysisResultsInput, UserUncheckedCreateWithoutAnalysisResultsInput>
    connectOrCreate?: UserCreateOrConnectWithoutAnalysisResultsInput
    connect?: UserWhereUniqueInput
  }

  export type ProductCreateNestedOneWithoutAnalysisResultsInput = {
    create?: XOR<ProductCreateWithoutAnalysisResultsInput, ProductUncheckedCreateWithoutAnalysisResultsInput>
    connectOrCreate?: ProductCreateOrConnectWithoutAnalysisResultsInput
    connect?: ProductWhereUniqueInput
  }

  export type UserUpdateOneRequiredWithoutAnalysisResultsNestedInput = {
    create?: XOR<UserCreateWithoutAnalysisResultsInput, UserUncheckedCreateWithoutAnalysisResultsInput>
    connectOrCreate?: UserCreateOrConnectWithoutAnalysisResultsInput
    upsert?: UserUpsertWithoutAnalysisResultsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutAnalysisResultsInput, UserUpdateWithoutAnalysisResultsInput>, UserUncheckedUpdateWithoutAnalysisResultsInput>
  }

  export type ProductUpdateOneWithoutAnalysisResultsNestedInput = {
    create?: XOR<ProductCreateWithoutAnalysisResultsInput, ProductUncheckedCreateWithoutAnalysisResultsInput>
    connectOrCreate?: ProductCreateOrConnectWithoutAnalysisResultsInput
    upsert?: ProductUpsertWithoutAnalysisResultsInput
    disconnect?: ProductWhereInput | boolean
    delete?: ProductWhereInput | boolean
    connect?: ProductWhereUniqueInput
    update?: XOR<XOR<ProductUpdateToOneWithWhereWithoutAnalysisResultsInput, ProductUpdateWithoutAnalysisResultsInput>, ProductUncheckedUpdateWithoutAnalysisResultsInput>
  }

  export type UserCreateNestedOneWithoutImportTasksInput = {
    create?: XOR<UserCreateWithoutImportTasksInput, UserUncheckedCreateWithoutImportTasksInput>
    connectOrCreate?: UserCreateOrConnectWithoutImportTasksInput
    connect?: UserWhereUniqueInput
  }

  export type ProductCreateNestedManyWithoutImportTaskInput = {
    create?: XOR<ProductCreateWithoutImportTaskInput, ProductUncheckedCreateWithoutImportTaskInput> | ProductCreateWithoutImportTaskInput[] | ProductUncheckedCreateWithoutImportTaskInput[]
    connectOrCreate?: ProductCreateOrConnectWithoutImportTaskInput | ProductCreateOrConnectWithoutImportTaskInput[]
    createMany?: ProductCreateManyImportTaskInputEnvelope
    connect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
  }

  export type ProductUncheckedCreateNestedManyWithoutImportTaskInput = {
    create?: XOR<ProductCreateWithoutImportTaskInput, ProductUncheckedCreateWithoutImportTaskInput> | ProductCreateWithoutImportTaskInput[] | ProductUncheckedCreateWithoutImportTaskInput[]
    connectOrCreate?: ProductCreateOrConnectWithoutImportTaskInput | ProductCreateOrConnectWithoutImportTaskInput[]
    createMany?: ProductCreateManyImportTaskInputEnvelope
    connect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
  }

  export type UserUpdateOneRequiredWithoutImportTasksNestedInput = {
    create?: XOR<UserCreateWithoutImportTasksInput, UserUncheckedCreateWithoutImportTasksInput>
    connectOrCreate?: UserCreateOrConnectWithoutImportTasksInput
    upsert?: UserUpsertWithoutImportTasksInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutImportTasksInput, UserUpdateWithoutImportTasksInput>, UserUncheckedUpdateWithoutImportTasksInput>
  }

  export type ProductUpdateManyWithoutImportTaskNestedInput = {
    create?: XOR<ProductCreateWithoutImportTaskInput, ProductUncheckedCreateWithoutImportTaskInput> | ProductCreateWithoutImportTaskInput[] | ProductUncheckedCreateWithoutImportTaskInput[]
    connectOrCreate?: ProductCreateOrConnectWithoutImportTaskInput | ProductCreateOrConnectWithoutImportTaskInput[]
    upsert?: ProductUpsertWithWhereUniqueWithoutImportTaskInput | ProductUpsertWithWhereUniqueWithoutImportTaskInput[]
    createMany?: ProductCreateManyImportTaskInputEnvelope
    set?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    disconnect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    delete?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    connect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    update?: ProductUpdateWithWhereUniqueWithoutImportTaskInput | ProductUpdateWithWhereUniqueWithoutImportTaskInput[]
    updateMany?: ProductUpdateManyWithWhereWithoutImportTaskInput | ProductUpdateManyWithWhereWithoutImportTaskInput[]
    deleteMany?: ProductScalarWhereInput | ProductScalarWhereInput[]
  }

  export type ProductUncheckedUpdateManyWithoutImportTaskNestedInput = {
    create?: XOR<ProductCreateWithoutImportTaskInput, ProductUncheckedCreateWithoutImportTaskInput> | ProductCreateWithoutImportTaskInput[] | ProductUncheckedCreateWithoutImportTaskInput[]
    connectOrCreate?: ProductCreateOrConnectWithoutImportTaskInput | ProductCreateOrConnectWithoutImportTaskInput[]
    upsert?: ProductUpsertWithWhereUniqueWithoutImportTaskInput | ProductUpsertWithWhereUniqueWithoutImportTaskInput[]
    createMany?: ProductCreateManyImportTaskInputEnvelope
    set?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    disconnect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    delete?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    connect?: ProductWhereUniqueInput | ProductWhereUniqueInput[]
    update?: ProductUpdateWithWhereUniqueWithoutImportTaskInput | ProductUpdateWithWhereUniqueWithoutImportTaskInput[]
    updateMany?: ProductUpdateManyWithWhereWithoutImportTaskInput | ProductUpdateManyWithWhereWithoutImportTaskInput[]
    deleteMany?: ProductScalarWhereInput | ProductScalarWhereInput[]
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedFloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type ProductCreateWithoutUserInput = {
    id?: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    importTask?: ImportTaskCreateNestedOneWithoutProductsInput
    analysisResults?: AnalysisResultCreateNestedManyWithoutProductInput
  }

  export type ProductUncheckedCreateWithoutUserInput = {
    id?: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    importTaskId?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    analysisResults?: AnalysisResultUncheckedCreateNestedManyWithoutProductInput
  }

  export type ProductCreateOrConnectWithoutUserInput = {
    where: ProductWhereUniqueInput
    create: XOR<ProductCreateWithoutUserInput, ProductUncheckedCreateWithoutUserInput>
  }

  export type ProductCreateManyUserInputEnvelope = {
    data: ProductCreateManyUserInput | ProductCreateManyUserInput[]
  }

  export type AnalysisResultCreateWithoutUserInput = {
    id?: string
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    product?: ProductCreateNestedOneWithoutAnalysisResultsInput
  }

  export type AnalysisResultUncheckedCreateWithoutUserInput = {
    id?: string
    productId?: string | null
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AnalysisResultCreateOrConnectWithoutUserInput = {
    where: AnalysisResultWhereUniqueInput
    create: XOR<AnalysisResultCreateWithoutUserInput, AnalysisResultUncheckedCreateWithoutUserInput>
  }

  export type AnalysisResultCreateManyUserInputEnvelope = {
    data: AnalysisResultCreateManyUserInput | AnalysisResultCreateManyUserInput[]
  }

  export type ImportTaskCreateWithoutUserInput = {
    id?: string
    title: string
    description?: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType?: string
    status?: string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    totalRows?: number
    processedRows?: number
    successRows?: number
    failedRows?: number
    errorMessage?: string | null
    errorDetails?: string | null
    validationErrors?: string | null
    importSummary?: string | null
    config?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductCreateNestedManyWithoutImportTaskInput
  }

  export type ImportTaskUncheckedCreateWithoutUserInput = {
    id?: string
    title: string
    description?: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType?: string
    status?: string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    totalRows?: number
    processedRows?: number
    successRows?: number
    failedRows?: number
    errorMessage?: string | null
    errorDetails?: string | null
    validationErrors?: string | null
    importSummary?: string | null
    config?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductUncheckedCreateNestedManyWithoutImportTaskInput
  }

  export type ImportTaskCreateOrConnectWithoutUserInput = {
    where: ImportTaskWhereUniqueInput
    create: XOR<ImportTaskCreateWithoutUserInput, ImportTaskUncheckedCreateWithoutUserInput>
  }

  export type ImportTaskCreateManyUserInputEnvelope = {
    data: ImportTaskCreateManyUserInput | ImportTaskCreateManyUserInput[]
  }

  export type ProductUpsertWithWhereUniqueWithoutUserInput = {
    where: ProductWhereUniqueInput
    update: XOR<ProductUpdateWithoutUserInput, ProductUncheckedUpdateWithoutUserInput>
    create: XOR<ProductCreateWithoutUserInput, ProductUncheckedCreateWithoutUserInput>
  }

  export type ProductUpdateWithWhereUniqueWithoutUserInput = {
    where: ProductWhereUniqueInput
    data: XOR<ProductUpdateWithoutUserInput, ProductUncheckedUpdateWithoutUserInput>
  }

  export type ProductUpdateManyWithWhereWithoutUserInput = {
    where: ProductScalarWhereInput
    data: XOR<ProductUpdateManyMutationInput, ProductUncheckedUpdateManyWithoutUserInput>
  }

  export type ProductScalarWhereInput = {
    AND?: ProductScalarWhereInput | ProductScalarWhereInput[]
    OR?: ProductScalarWhereInput[]
    NOT?: ProductScalarWhereInput | ProductScalarWhereInput[]
    id?: StringFilter<"Product"> | string
    userId?: StringFilter<"Product"> | string
    title?: StringFilter<"Product"> | string
    description?: StringNullableFilter<"Product"> | string | null
    url?: StringFilter<"Product"> | string
    imageUrl?: StringNullableFilter<"Product"> | string | null
    brand?: StringNullableFilter<"Product"> | string | null
    model?: StringNullableFilter<"Product"> | string | null
    platform?: StringFilter<"Product"> | string
    platformProductId?: StringNullableFilter<"Product"> | string | null
    shopName?: StringNullableFilter<"Product"> | string | null
    shopUrl?: StringNullableFilter<"Product"> | string | null
    importTaskId?: StringNullableFilter<"Product"> | string | null
    dataSource?: StringFilter<"Product"> | string
    currentPrice?: FloatNullableFilter<"Product"> | number | null
    originalPrice?: FloatNullableFilter<"Product"> | number | null
    minPrice?: FloatNullableFilter<"Product"> | number | null
    maxPrice?: FloatNullableFilter<"Product"> | number | null
    currency?: StringFilter<"Product"> | string
    salesCount?: IntNullableFilter<"Product"> | number | null
    rating?: FloatNullableFilter<"Product"> | number | null
    reviewCount?: IntNullableFilter<"Product"> | number | null
    stockQuantity?: IntNullableFilter<"Product"> | number | null
    category?: StringNullableFilter<"Product"> | string | null
    subcategory?: StringNullableFilter<"Product"> | string | null
    tags?: StringNullableFilter<"Product"> | string | null
    attributes?: StringNullableFilter<"Product"> | string | null
    status?: StringFilter<"Product"> | string
    isTracked?: BoolFilter<"Product"> | boolean
    isAnalyzed?: BoolFilter<"Product"> | boolean
    lastImportedAt?: DateTimeNullableFilter<"Product"> | Date | string | null
    importCount?: IntFilter<"Product"> | number
    importErrors?: StringNullableFilter<"Product"> | string | null
    analysisSummary?: StringNullableFilter<"Product"> | string | null
    analysisScore?: FloatNullableFilter<"Product"> | number | null
    marketPosition?: StringNullableFilter<"Product"> | string | null
    createdAt?: DateTimeFilter<"Product"> | Date | string
    updatedAt?: DateTimeFilter<"Product"> | Date | string
  }

  export type AnalysisResultUpsertWithWhereUniqueWithoutUserInput = {
    where: AnalysisResultWhereUniqueInput
    update: XOR<AnalysisResultUpdateWithoutUserInput, AnalysisResultUncheckedUpdateWithoutUserInput>
    create: XOR<AnalysisResultCreateWithoutUserInput, AnalysisResultUncheckedCreateWithoutUserInput>
  }

  export type AnalysisResultUpdateWithWhereUniqueWithoutUserInput = {
    where: AnalysisResultWhereUniqueInput
    data: XOR<AnalysisResultUpdateWithoutUserInput, AnalysisResultUncheckedUpdateWithoutUserInput>
  }

  export type AnalysisResultUpdateManyWithWhereWithoutUserInput = {
    where: AnalysisResultScalarWhereInput
    data: XOR<AnalysisResultUpdateManyMutationInput, AnalysisResultUncheckedUpdateManyWithoutUserInput>
  }

  export type AnalysisResultScalarWhereInput = {
    AND?: AnalysisResultScalarWhereInput | AnalysisResultScalarWhereInput[]
    OR?: AnalysisResultScalarWhereInput[]
    NOT?: AnalysisResultScalarWhereInput | AnalysisResultScalarWhereInput[]
    id?: StringFilter<"AnalysisResult"> | string
    userId?: StringFilter<"AnalysisResult"> | string
    productId?: StringNullableFilter<"AnalysisResult"> | string | null
    title?: StringFilter<"AnalysisResult"> | string
    description?: StringNullableFilter<"AnalysisResult"> | string | null
    type?: StringFilter<"AnalysisResult"> | string
    status?: StringFilter<"AnalysisResult"> | string
    priority?: StringFilter<"AnalysisResult"> | string
    config?: StringNullableFilter<"AnalysisResult"> | string | null
    inputData?: StringNullableFilter<"AnalysisResult"> | string | null
    outputData?: StringNullableFilter<"AnalysisResult"> | string | null
    summary?: StringNullableFilter<"AnalysisResult"> | string | null
    insights?: StringNullableFilter<"AnalysisResult"> | string | null
    recommendations?: StringNullableFilter<"AnalysisResult"> | string | null
    chartsData?: StringNullableFilter<"AnalysisResult"> | string | null
    confidenceScore?: FloatNullableFilter<"AnalysisResult"> | number | null
    startedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    durationMs?: IntNullableFilter<"AnalysisResult"> | number | null
    errorMessage?: StringNullableFilter<"AnalysisResult"> | string | null
    errorDetails?: StringNullableFilter<"AnalysisResult"> | string | null
    retryCount?: IntFilter<"AnalysisResult"> | number
    viewCount?: IntFilter<"AnalysisResult"> | number
    shareCount?: IntFilter<"AnalysisResult"> | number
    exportCount?: IntFilter<"AnalysisResult"> | number
    lastViewedAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    shareToken?: StringNullableFilter<"AnalysisResult"> | string | null
    shareType?: StringFilter<"AnalysisResult"> | string
    shareExpiresAt?: DateTimeNullableFilter<"AnalysisResult"> | Date | string | null
    tags?: StringNullableFilter<"AnalysisResult"> | string | null
    category?: StringNullableFilter<"AnalysisResult"> | string | null
    isFavorite?: BoolFilter<"AnalysisResult"> | boolean
    isArchived?: BoolFilter<"AnalysisResult"> | boolean
    createdAt?: DateTimeFilter<"AnalysisResult"> | Date | string
    updatedAt?: DateTimeFilter<"AnalysisResult"> | Date | string
  }

  export type ImportTaskUpsertWithWhereUniqueWithoutUserInput = {
    where: ImportTaskWhereUniqueInput
    update: XOR<ImportTaskUpdateWithoutUserInput, ImportTaskUncheckedUpdateWithoutUserInput>
    create: XOR<ImportTaskCreateWithoutUserInput, ImportTaskUncheckedCreateWithoutUserInput>
  }

  export type ImportTaskUpdateWithWhereUniqueWithoutUserInput = {
    where: ImportTaskWhereUniqueInput
    data: XOR<ImportTaskUpdateWithoutUserInput, ImportTaskUncheckedUpdateWithoutUserInput>
  }

  export type ImportTaskUpdateManyWithWhereWithoutUserInput = {
    where: ImportTaskScalarWhereInput
    data: XOR<ImportTaskUpdateManyMutationInput, ImportTaskUncheckedUpdateManyWithoutUserInput>
  }

  export type ImportTaskScalarWhereInput = {
    AND?: ImportTaskScalarWhereInput | ImportTaskScalarWhereInput[]
    OR?: ImportTaskScalarWhereInput[]
    NOT?: ImportTaskScalarWhereInput | ImportTaskScalarWhereInput[]
    id?: StringFilter<"ImportTask"> | string
    userId?: StringFilter<"ImportTask"> | string
    title?: StringFilter<"ImportTask"> | string
    description?: StringNullableFilter<"ImportTask"> | string | null
    fileName?: StringFilter<"ImportTask"> | string
    filePath?: StringFilter<"ImportTask"> | string
    fileSize?: IntFilter<"ImportTask"> | number
    fileType?: StringFilter<"ImportTask"> | string
    status?: StringFilter<"ImportTask"> | string
    startedAt?: DateTimeNullableFilter<"ImportTask"> | Date | string | null
    completedAt?: DateTimeNullableFilter<"ImportTask"> | Date | string | null
    durationMs?: IntNullableFilter<"ImportTask"> | number | null
    totalRows?: IntFilter<"ImportTask"> | number
    processedRows?: IntFilter<"ImportTask"> | number
    successRows?: IntFilter<"ImportTask"> | number
    failedRows?: IntFilter<"ImportTask"> | number
    errorMessage?: StringNullableFilter<"ImportTask"> | string | null
    errorDetails?: StringNullableFilter<"ImportTask"> | string | null
    validationErrors?: StringNullableFilter<"ImportTask"> | string | null
    importSummary?: StringNullableFilter<"ImportTask"> | string | null
    config?: StringNullableFilter<"ImportTask"> | string | null
    createdAt?: DateTimeFilter<"ImportTask"> | Date | string
    updatedAt?: DateTimeFilter<"ImportTask"> | Date | string
  }

  export type UserCreateWithoutProductsInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    analysisResults?: AnalysisResultCreateNestedManyWithoutUserInput
    importTasks?: ImportTaskCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutProductsInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    analysisResults?: AnalysisResultUncheckedCreateNestedManyWithoutUserInput
    importTasks?: ImportTaskUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutProductsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutProductsInput, UserUncheckedCreateWithoutProductsInput>
  }

  export type ImportTaskCreateWithoutProductsInput = {
    id?: string
    title: string
    description?: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType?: string
    status?: string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    totalRows?: number
    processedRows?: number
    successRows?: number
    failedRows?: number
    errorMessage?: string | null
    errorDetails?: string | null
    validationErrors?: string | null
    importSummary?: string | null
    config?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutImportTasksInput
  }

  export type ImportTaskUncheckedCreateWithoutProductsInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType?: string
    status?: string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    totalRows?: number
    processedRows?: number
    successRows?: number
    failedRows?: number
    errorMessage?: string | null
    errorDetails?: string | null
    validationErrors?: string | null
    importSummary?: string | null
    config?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ImportTaskCreateOrConnectWithoutProductsInput = {
    where: ImportTaskWhereUniqueInput
    create: XOR<ImportTaskCreateWithoutProductsInput, ImportTaskUncheckedCreateWithoutProductsInput>
  }

  export type AnalysisResultCreateWithoutProductInput = {
    id?: string
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutAnalysisResultsInput
  }

  export type AnalysisResultUncheckedCreateWithoutProductInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AnalysisResultCreateOrConnectWithoutProductInput = {
    where: AnalysisResultWhereUniqueInput
    create: XOR<AnalysisResultCreateWithoutProductInput, AnalysisResultUncheckedCreateWithoutProductInput>
  }

  export type AnalysisResultCreateManyProductInputEnvelope = {
    data: AnalysisResultCreateManyProductInput | AnalysisResultCreateManyProductInput[]
  }

  export type UserUpsertWithoutProductsInput = {
    update: XOR<UserUpdateWithoutProductsInput, UserUncheckedUpdateWithoutProductsInput>
    create: XOR<UserCreateWithoutProductsInput, UserUncheckedCreateWithoutProductsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutProductsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutProductsInput, UserUncheckedUpdateWithoutProductsInput>
  }

  export type UserUpdateWithoutProductsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    analysisResults?: AnalysisResultUpdateManyWithoutUserNestedInput
    importTasks?: ImportTaskUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutProductsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    analysisResults?: AnalysisResultUncheckedUpdateManyWithoutUserNestedInput
    importTasks?: ImportTaskUncheckedUpdateManyWithoutUserNestedInput
  }

  export type ImportTaskUpsertWithoutProductsInput = {
    update: XOR<ImportTaskUpdateWithoutProductsInput, ImportTaskUncheckedUpdateWithoutProductsInput>
    create: XOR<ImportTaskCreateWithoutProductsInput, ImportTaskUncheckedCreateWithoutProductsInput>
    where?: ImportTaskWhereInput
  }

  export type ImportTaskUpdateToOneWithWhereWithoutProductsInput = {
    where?: ImportTaskWhereInput
    data: XOR<ImportTaskUpdateWithoutProductsInput, ImportTaskUncheckedUpdateWithoutProductsInput>
  }

  export type ImportTaskUpdateWithoutProductsInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutImportTasksNestedInput
  }

  export type ImportTaskUncheckedUpdateWithoutProductsInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AnalysisResultUpsertWithWhereUniqueWithoutProductInput = {
    where: AnalysisResultWhereUniqueInput
    update: XOR<AnalysisResultUpdateWithoutProductInput, AnalysisResultUncheckedUpdateWithoutProductInput>
    create: XOR<AnalysisResultCreateWithoutProductInput, AnalysisResultUncheckedCreateWithoutProductInput>
  }

  export type AnalysisResultUpdateWithWhereUniqueWithoutProductInput = {
    where: AnalysisResultWhereUniqueInput
    data: XOR<AnalysisResultUpdateWithoutProductInput, AnalysisResultUncheckedUpdateWithoutProductInput>
  }

  export type AnalysisResultUpdateManyWithWhereWithoutProductInput = {
    where: AnalysisResultScalarWhereInput
    data: XOR<AnalysisResultUpdateManyMutationInput, AnalysisResultUncheckedUpdateManyWithoutProductInput>
  }

  export type UserCreateWithoutAnalysisResultsInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductCreateNestedManyWithoutUserInput
    importTasks?: ImportTaskCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutAnalysisResultsInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductUncheckedCreateNestedManyWithoutUserInput
    importTasks?: ImportTaskUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutAnalysisResultsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutAnalysisResultsInput, UserUncheckedCreateWithoutAnalysisResultsInput>
  }

  export type ProductCreateWithoutAnalysisResultsInput = {
    id?: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutProductsInput
    importTask?: ImportTaskCreateNestedOneWithoutProductsInput
  }

  export type ProductUncheckedCreateWithoutAnalysisResultsInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    importTaskId?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductCreateOrConnectWithoutAnalysisResultsInput = {
    where: ProductWhereUniqueInput
    create: XOR<ProductCreateWithoutAnalysisResultsInput, ProductUncheckedCreateWithoutAnalysisResultsInput>
  }

  export type UserUpsertWithoutAnalysisResultsInput = {
    update: XOR<UserUpdateWithoutAnalysisResultsInput, UserUncheckedUpdateWithoutAnalysisResultsInput>
    create: XOR<UserCreateWithoutAnalysisResultsInput, UserUncheckedCreateWithoutAnalysisResultsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutAnalysisResultsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutAnalysisResultsInput, UserUncheckedUpdateWithoutAnalysisResultsInput>
  }

  export type UserUpdateWithoutAnalysisResultsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUpdateManyWithoutUserNestedInput
    importTasks?: ImportTaskUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutAnalysisResultsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUncheckedUpdateManyWithoutUserNestedInput
    importTasks?: ImportTaskUncheckedUpdateManyWithoutUserNestedInput
  }

  export type ProductUpsertWithoutAnalysisResultsInput = {
    update: XOR<ProductUpdateWithoutAnalysisResultsInput, ProductUncheckedUpdateWithoutAnalysisResultsInput>
    create: XOR<ProductCreateWithoutAnalysisResultsInput, ProductUncheckedCreateWithoutAnalysisResultsInput>
    where?: ProductWhereInput
  }

  export type ProductUpdateToOneWithWhereWithoutAnalysisResultsInput = {
    where?: ProductWhereInput
    data: XOR<ProductUpdateWithoutAnalysisResultsInput, ProductUncheckedUpdateWithoutAnalysisResultsInput>
  }

  export type ProductUpdateWithoutAnalysisResultsInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutProductsNestedInput
    importTask?: ImportTaskUpdateOneWithoutProductsNestedInput
  }

  export type ProductUncheckedUpdateWithoutAnalysisResultsInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    importTaskId?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserCreateWithoutImportTasksInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductCreateNestedManyWithoutUserInput
    analysisResults?: AnalysisResultCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutImportTasksInput = {
    id?: string
    email: string
    username: string
    password: string
    firstName?: string | null
    lastName?: string | null
    avatar?: string | null
    phone?: string | null
    role?: string
    status?: string
    emailVerified?: boolean
    emailVerifiedAt?: Date | string | null
    lastLoginAt?: Date | string | null
    loginCount?: number
    preferences?: string | null
    settings?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    products?: ProductUncheckedCreateNestedManyWithoutUserInput
    analysisResults?: AnalysisResultUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutImportTasksInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutImportTasksInput, UserUncheckedCreateWithoutImportTasksInput>
  }

  export type ProductCreateWithoutImportTaskInput = {
    id?: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutProductsInput
    analysisResults?: AnalysisResultCreateNestedManyWithoutProductInput
  }

  export type ProductUncheckedCreateWithoutImportTaskInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    analysisResults?: AnalysisResultUncheckedCreateNestedManyWithoutProductInput
  }

  export type ProductCreateOrConnectWithoutImportTaskInput = {
    where: ProductWhereUniqueInput
    create: XOR<ProductCreateWithoutImportTaskInput, ProductUncheckedCreateWithoutImportTaskInput>
  }

  export type ProductCreateManyImportTaskInputEnvelope = {
    data: ProductCreateManyImportTaskInput | ProductCreateManyImportTaskInput[]
  }

  export type UserUpsertWithoutImportTasksInput = {
    update: XOR<UserUpdateWithoutImportTasksInput, UserUncheckedUpdateWithoutImportTasksInput>
    create: XOR<UserCreateWithoutImportTasksInput, UserUncheckedCreateWithoutImportTasksInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutImportTasksInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutImportTasksInput, UserUncheckedUpdateWithoutImportTasksInput>
  }

  export type UserUpdateWithoutImportTasksInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUpdateManyWithoutUserNestedInput
    analysisResults?: AnalysisResultUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutImportTasksInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    firstName?: NullableStringFieldUpdateOperationsInput | string | null
    lastName?: NullableStringFieldUpdateOperationsInput | string | null
    avatar?: NullableStringFieldUpdateOperationsInput | string | null
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    role?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    emailVerified?: BoolFieldUpdateOperationsInput | boolean
    emailVerifiedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    lastLoginAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    loginCount?: IntFieldUpdateOperationsInput | number
    preferences?: NullableStringFieldUpdateOperationsInput | string | null
    settings?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUncheckedUpdateManyWithoutUserNestedInput
    analysisResults?: AnalysisResultUncheckedUpdateManyWithoutUserNestedInput
  }

  export type ProductUpsertWithWhereUniqueWithoutImportTaskInput = {
    where: ProductWhereUniqueInput
    update: XOR<ProductUpdateWithoutImportTaskInput, ProductUncheckedUpdateWithoutImportTaskInput>
    create: XOR<ProductCreateWithoutImportTaskInput, ProductUncheckedCreateWithoutImportTaskInput>
  }

  export type ProductUpdateWithWhereUniqueWithoutImportTaskInput = {
    where: ProductWhereUniqueInput
    data: XOR<ProductUpdateWithoutImportTaskInput, ProductUncheckedUpdateWithoutImportTaskInput>
  }

  export type ProductUpdateManyWithWhereWithoutImportTaskInput = {
    where: ProductScalarWhereInput
    data: XOR<ProductUpdateManyMutationInput, ProductUncheckedUpdateManyWithoutImportTaskInput>
  }

  export type ProductCreateManyUserInput = {
    id?: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    importTaskId?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AnalysisResultCreateManyUserInput = {
    id?: string
    productId?: string | null
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ImportTaskCreateManyUserInput = {
    id?: string
    title: string
    description?: string | null
    fileName: string
    filePath: string
    fileSize: number
    fileType?: string
    status?: string
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    totalRows?: number
    processedRows?: number
    successRows?: number
    failedRows?: number
    errorMessage?: string | null
    errorDetails?: string | null
    validationErrors?: string | null
    importSummary?: string | null
    config?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    importTask?: ImportTaskUpdateOneWithoutProductsNestedInput
    analysisResults?: AnalysisResultUpdateManyWithoutProductNestedInput
  }

  export type ProductUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    importTaskId?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    analysisResults?: AnalysisResultUncheckedUpdateManyWithoutProductNestedInput
  }

  export type ProductUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    importTaskId?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AnalysisResultUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    product?: ProductUpdateOneWithoutAnalysisResultsNestedInput
  }

  export type AnalysisResultUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    productId?: NullableStringFieldUpdateOperationsInput | string | null
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AnalysisResultUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    productId?: NullableStringFieldUpdateOperationsInput | string | null
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ImportTaskUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUpdateManyWithoutImportTaskNestedInput
  }

  export type ImportTaskUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    products?: ProductUncheckedUpdateManyWithoutImportTaskNestedInput
  }

  export type ImportTaskUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    fileName?: StringFieldUpdateOperationsInput | string
    filePath?: StringFieldUpdateOperationsInput | string
    fileSize?: IntFieldUpdateOperationsInput | number
    fileType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    totalRows?: IntFieldUpdateOperationsInput | number
    processedRows?: IntFieldUpdateOperationsInput | number
    successRows?: IntFieldUpdateOperationsInput | number
    failedRows?: IntFieldUpdateOperationsInput | number
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    validationErrors?: NullableStringFieldUpdateOperationsInput | string | null
    importSummary?: NullableStringFieldUpdateOperationsInput | string | null
    config?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AnalysisResultCreateManyProductInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    type?: string
    status?: string
    priority?: string
    config?: string | null
    inputData?: string | null
    outputData?: string | null
    summary?: string | null
    insights?: string | null
    recommendations?: string | null
    chartsData?: string | null
    confidenceScore?: number | null
    startedAt?: Date | string | null
    completedAt?: Date | string | null
    durationMs?: number | null
    errorMessage?: string | null
    errorDetails?: string | null
    retryCount?: number
    viewCount?: number
    shareCount?: number
    exportCount?: number
    lastViewedAt?: Date | string | null
    shareToken?: string | null
    shareType?: string
    shareExpiresAt?: Date | string | null
    tags?: string | null
    category?: string | null
    isFavorite?: boolean
    isArchived?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AnalysisResultUpdateWithoutProductInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutAnalysisResultsNestedInput
  }

  export type AnalysisResultUncheckedUpdateWithoutProductInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AnalysisResultUncheckedUpdateManyWithoutProductInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    config?: NullableStringFieldUpdateOperationsInput | string | null
    inputData?: NullableStringFieldUpdateOperationsInput | string | null
    outputData?: NullableStringFieldUpdateOperationsInput | string | null
    summary?: NullableStringFieldUpdateOperationsInput | string | null
    insights?: NullableStringFieldUpdateOperationsInput | string | null
    recommendations?: NullableStringFieldUpdateOperationsInput | string | null
    chartsData?: NullableStringFieldUpdateOperationsInput | string | null
    confidenceScore?: NullableFloatFieldUpdateOperationsInput | number | null
    startedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    durationMs?: NullableIntFieldUpdateOperationsInput | number | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    errorDetails?: NullableStringFieldUpdateOperationsInput | string | null
    retryCount?: IntFieldUpdateOperationsInput | number
    viewCount?: IntFieldUpdateOperationsInput | number
    shareCount?: IntFieldUpdateOperationsInput | number
    exportCount?: IntFieldUpdateOperationsInput | number
    lastViewedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    shareToken?: NullableStringFieldUpdateOperationsInput | string | null
    shareType?: StringFieldUpdateOperationsInput | string
    shareExpiresAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    isFavorite?: BoolFieldUpdateOperationsInput | boolean
    isArchived?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductCreateManyImportTaskInput = {
    id?: string
    userId: string
    title: string
    description?: string | null
    url: string
    imageUrl?: string | null
    brand?: string | null
    model?: string | null
    platform?: string
    platformProductId?: string | null
    shopName?: string | null
    shopUrl?: string | null
    dataSource?: string
    currentPrice?: number | null
    originalPrice?: number | null
    minPrice?: number | null
    maxPrice?: number | null
    currency?: string
    salesCount?: number | null
    rating?: number | null
    reviewCount?: number | null
    stockQuantity?: number | null
    category?: string | null
    subcategory?: string | null
    tags?: string | null
    attributes?: string | null
    status?: string
    isTracked?: boolean
    isAnalyzed?: boolean
    lastImportedAt?: Date | string | null
    importCount?: number
    importErrors?: string | null
    analysisSummary?: string | null
    analysisScore?: number | null
    marketPosition?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductUpdateWithoutImportTaskInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutProductsNestedInput
    analysisResults?: AnalysisResultUpdateManyWithoutProductNestedInput
  }

  export type ProductUncheckedUpdateWithoutImportTaskInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    analysisResults?: AnalysisResultUncheckedUpdateManyWithoutProductNestedInput
  }

  export type ProductUncheckedUpdateManyWithoutImportTaskInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    url?: StringFieldUpdateOperationsInput | string
    imageUrl?: NullableStringFieldUpdateOperationsInput | string | null
    brand?: NullableStringFieldUpdateOperationsInput | string | null
    model?: NullableStringFieldUpdateOperationsInput | string | null
    platform?: StringFieldUpdateOperationsInput | string
    platformProductId?: NullableStringFieldUpdateOperationsInput | string | null
    shopName?: NullableStringFieldUpdateOperationsInput | string | null
    shopUrl?: NullableStringFieldUpdateOperationsInput | string | null
    dataSource?: StringFieldUpdateOperationsInput | string
    currentPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    originalPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    minPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    maxPrice?: NullableFloatFieldUpdateOperationsInput | number | null
    currency?: StringFieldUpdateOperationsInput | string
    salesCount?: NullableIntFieldUpdateOperationsInput | number | null
    rating?: NullableFloatFieldUpdateOperationsInput | number | null
    reviewCount?: NullableIntFieldUpdateOperationsInput | number | null
    stockQuantity?: NullableIntFieldUpdateOperationsInput | number | null
    category?: NullableStringFieldUpdateOperationsInput | string | null
    subcategory?: NullableStringFieldUpdateOperationsInput | string | null
    tags?: NullableStringFieldUpdateOperationsInput | string | null
    attributes?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    isTracked?: BoolFieldUpdateOperationsInput | boolean
    isAnalyzed?: BoolFieldUpdateOperationsInput | boolean
    lastImportedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    importCount?: IntFieldUpdateOperationsInput | number
    importErrors?: NullableStringFieldUpdateOperationsInput | string | null
    analysisSummary?: NullableStringFieldUpdateOperationsInput | string | null
    analysisScore?: NullableFloatFieldUpdateOperationsInput | number | null
    marketPosition?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Aliases for legacy arg types
   */
    /**
     * @deprecated Use UserCountOutputTypeDefaultArgs instead
     */
    export type UserCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use ProductCountOutputTypeDefaultArgs instead
     */
    export type ProductCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = ProductCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use ImportTaskCountOutputTypeDefaultArgs instead
     */
    export type ImportTaskCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = ImportTaskCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use UserDefaultArgs instead
     */
    export type UserArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserDefaultArgs<ExtArgs>
    /**
     * @deprecated Use ProductDefaultArgs instead
     */
    export type ProductArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = ProductDefaultArgs<ExtArgs>
    /**
     * @deprecated Use AnalysisResultDefaultArgs instead
     */
    export type AnalysisResultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = AnalysisResultDefaultArgs<ExtArgs>
    /**
     * @deprecated Use ImportTaskDefaultArgs instead
     */
    export type ImportTaskArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = ImportTaskDefaultArgs<ExtArgs>

  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}